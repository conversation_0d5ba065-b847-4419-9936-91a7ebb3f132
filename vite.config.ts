import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import { createHtmlPlugin } from 'vite-plugin-html';
import svgr from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, '.');
  return {
    plugins: [
      react(),
      svgr(),
      tsconfigPaths(),
      createHtmlPlugin({
        inject: {
          data: {
            ...env,
            ENV_NAME: mode === 'development' ? 'Local' : mode.toLowerCase().replace(/\b\w/g, (s) => s.toUpperCase()),
          },
        },
      }),
    ],
    server: {
      watch: {
        usePolling: true,
      },
    },
  };
});
