apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: patient-webapp
  description: FluentHealth companion web app
  tags:
    - reactjs
    - typescript

  links:
    - url: https://console.firebase.google.com/u/0/project/fh-dev-svc/hosting/sites/fh-dev-svc-webapp
      title: Deployment Link (Dev)
      icon: externalLink
    - url: https://fh-dev-svc-webapp.web.app/
      title: Link (Dev)
      icon: externalLink

spec:
  type: website
  lifecycle: production
  owner: group:webapp

  dependsOn:
    - resource:firebase-host-webapp
    - component:apim-prismic
    - component:apim-emr-fhir
    - component:apim-emr-share
    - component:apim-cms
    - component:apim-content-library
    - component:apim-auth-service
    # Deprecated apis that need to remove
    - component:apim-fh-microservices
    - component:yellow-ai
    - component:apim-medixcel
