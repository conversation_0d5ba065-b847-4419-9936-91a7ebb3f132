# Docs: https://cloud.google.com/build/docs/api/reference/rest/v1/projects.triggers#BuildTrigger
# Modified with [trigger-setup.sh](../../deploy/trigger-setup.sh)
# > ! Make sure to rerun that script after updating this file
name: 'webapp-pull-request'
github:
  owner: 'Fluent-Health'
  name: 'WebApp'
  pullRequest:
    branch: ^(prod)$
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS
filename: deploy/cloudbuild.pull-request.yaml
serviceAccount: projects/fh-prod-svc/serviceAccounts/<EMAIL>
