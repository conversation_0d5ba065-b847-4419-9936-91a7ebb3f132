# Docs: https://cloud.google.com/build/docs/api/reference/rest/v1/projects.triggers#BuildTrigger
# Modified with [trigger-setup.sh](../../deploy/trigger-setup.sh)
# > ! Make sure to rerun that script after updating this file
- id: Deploy Config
  name: asia-south1-docker.pkg.dev/$PROJECT_ID/firebase/firebase:latest
  args:
    - experiments:enable
    - webframeworks

- id: Deploy
  name: asia-south1-docker.pkg.dev/$PROJECT_ID/firebase/firebase:latest
  args:
    - hosting:channel:deploy
    - $_HEAD_BRANCH
    - --expires=14d
    - --only=hosting:$_HOSTING
    - --project=$PROJECT_ID
