# Docs: https://cloud.google.com/build/docs/api/reference/rest/v1/projects.triggers#BuildTrigger
# Modified with [trigger-setup.sh](../../deploy/trigger-setup.sh)
# > ! Make sure to rerun that script after updating this file
name: 'webapp'
github:
  owner: 'Fluent-Health'
  name: 'WebApp'
  push:
    branch: ^(main)$
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS
filename: deploy/cloudbuild.release.yaml
serviceAccount: projects/fh-dev-svc/serviceAccounts/<EMAIL>
