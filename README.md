# FluentHealth companion web app

Run `npm run dev` for a dev server. Navigate to `http://localhost:5173/` or press `o` while in console to access the app. The app will automatically reload if you change any of the source files.

Different environments use different values for the environment variables. The configuration is found in the `.env` file. Based on the configuration, the app will use the correct environment variables.

## Building for different environments

To build the project using default variables (`.env`), run `npm run build`.

To build for development using development variables (`.env.dev`), run `npm run build:dev`.

To build for staging using staging variables (`.env.staging`), run `npm run build:staging`.

The build artifacts will be stored in the `dist/` directory.

## Deployment

Creating pull request against main will trigger the [pull request build](deploy/cloudbuild.pull-request.yaml).
Merging into main will trigger the [release build](deploy/cloudbuild.release.yaml) to deploy to fh-dev-svc and fh-test-svc.

Creating pull request against prod will trigger the [pull request build](deploy/cloudbuild.pull-request.yaml).
Merging into main will trigger the [release build](deploy/cloudbuild.release.yaml) to deploy to fh-prod-svc.

| Dev                                                                                         | Test                                                                                          | Prod                                                                                          |
| ------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- |
| [Dev Build History](https://console.cloud.google.com/cloud-build/builds?project=fh-dev-svc) | [Test Build History](https://console.cloud.google.com/cloud-build/builds?project=fh-test-svc) | [Prod Build History](https://console.cloud.google.com/cloud-build/builds?project=fh-prod-svc) |
| [Dev Firebase Hosting](https://console.firebase.google.com/project/fh-dev-svc/hosting/sites/fh-dev-svc-webapp)              | [Test Firebase Hosting](https://console.firebase.google.com/project/fh-test-svc/hosting/sites/fh-test-svc-webapp)              | [Prod Firebase Hosting](https://console.firebase.google.com/project/fh-prod-svc/hosting/sites/fh-prod-svc-webapp)              |

## Configure CI/CD

Pipleines are configured in [Fluent-Health/infra-root](https://github.com/Fluent-Health/infra-root) in [pipeline-webapp.tf](https://github.com/Fluent-Health/infra-root/blob/main/terraform/pipeline-webapp.tf)

