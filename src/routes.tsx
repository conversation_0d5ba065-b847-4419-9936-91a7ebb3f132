/* eslint-disable prettier/prettier */
// Package modules
import { useEffect } from 'react';
import { ChakraProvider } from '@chakra-ui/react';
import { Outlet, createBrowserRouter, useLocation } from 'react-router-dom';

// Local modules
import Root from './__root';
import ErrorPage from './__error';
import { theme as medicalRecordTheme } from './app/medical-records/lib/theme';
// import { PublicMedicalRecordListPage } from './app/public-pages/PublicMedicalRecordListPage';
import { theme as profileRecordTheme } from './app/user/lib/theme';
import ProfilePage from './app/user/profile/ProfilePage';
import { AuthGuard } from './components/guards/AuthGuard';
import { LoginPage } from './app/auth/LoginPage';
import { MedicalRecordsPage } from './app/medical-records/MedicalRecordsPage';
import { SettingsPage } from './app/user/settings/SettingsPage';
import { SocialMediaPage } from './app/user/settings/SocialMediaPage';
import { FaqsPage } from './app/user/settings/FaqsPage';
import { LegalPage } from './app/user/settings/LegalPage';
import { TermsAndCondsPage } from './app/user/settings/TermsAndCondsPage';
import { PrivacyPolicyPage } from './app/user/settings/PrivacyPolicyPage';
import { CookiePolicyPage } from './app/user/settings/CookiePolicyPage';
import { CommunicationsAndConsentPage } from './app/user/settings/CommunicationsAndConsentPage';
import HomePage from './app/user/home/<USER>';
import BookAssistance from './app/user/book-assistance/BookAssistance';
import { MedicalRecordList } from './app/medical-records/MedicalRecordList';
import { BasicInfoPage } from './app/user/profile/basic-info/BasicInfoPage';
import { FamilyHistoryPage } from './app/user/profile/family-history/FamilyHistoryPage';
import { HealthProfilePage } from './app/user/profile/health-profile/HealthProfilePage';
import { CareTeamPage } from './app/user/profile/care-team/CareTeamPage';
import { MEDICAL_RECORD_BEHOLDER_TYPES } from '@lib/models/medical-record';
import ShareProfilePage from './app/user/share-profile/ShareProfile';
import { PublicMedicalRecordListPage } from './app/public-pages/PublicMedicalRecordListPage';
import { MeasurementPreferencePage } from './app/user/settings/MeasurementPreferencePage';
import { CommunicationPreferencePage } from './app/user/settings/CommunicationPreferencePage';
import { PersonalDataUsage } from './app/user/settings/PersonalDataUsage';
import { MedicalAdviceDisclaimer } from './app/user/settings/MedicalAdviceDisclaimer';
import { FluentSupportPage } from './app/user/settings/FluentSupport';
import { DownloadPersonalDataPage } from './app/user/settings/DownloadPersonalData';
import { DeletePersonalDataAccountPage } from './app/user/settings/DeletePersonalDataAccount';
// import { HealthGraphs } from './app/user/profile/health-graphs/health-graphs-main';

function RouteChangeTracker({ children }: { children: React.ReactNode }) {
  const location = useLocation()
  useEffect(() => {
    const newPath = localStorage.getItem('deep_link_path');
    if (newPath === window.location.pathname) {
      localStorage.removeItem('deep_link_path')
    }
    else if (newPath) {
      window.location.pathname = newPath;
      localStorage.removeItem('deep_link_path')
    }
  }, [location]);
  return <> {children} {" "} </>
}

export const router = createBrowserRouter([
  {
    path: '/',
    element: <RouteChangeTracker><Root /></RouteChangeTracker>,
    errorElement: <ErrorPage />,
    children: [
      {
        path: 'login',
        element: (
          <AuthGuard shouldBeAuthenticated={false}>
            <LoginPage />
          </AuthGuard>
        ),
      },
      {
        path: 'terms-n-conditions/view',
        element: <TermsAndCondsPage />,
      },
      {
        path: 'privacy-policy/view',
        element: <PrivacyPolicyPage />,
      },
      {
        path: '',
        element: (
          <AuthGuard>
            <Outlet />
          </AuthGuard>
        ),
        children: [
          {
            path: 'dashboard/view',
            element: <HomePage />,
          },
          {
            path: 'book-assistance',
            element: <BookAssistance />,
          },
          // TODO: Health Graphs - Will be pick after Launch
          {
            path: 'profile',
            element: (
              <ChakraProvider theme={profileRecordTheme}>
                <ProfilePage />
              </ChakraProvider>
            ),
            children: [
              {
                path: 'basics/view',
                element: <BasicInfoPage />,
              },
              {
                path: 'family-history/view',
                element: <FamilyHistoryPage />,
              },
              {
                path: 'ehr',
                element: <HealthProfilePage />,
                children: [
                  {
                    path: 'view',
                    element: <HealthProfilePage />,
                  },
                ],
              },
              {
                path: 'care-team',
                element: <CareTeamPage />,
              },
              {
                path: 'ehr/:ehrId/:action',
                element: window.innerWidth > 991 ? <BasicInfoPage /> : <HealthProfilePage />,
              },
              {
                path: 'ehr/:ehrId/:subEhrId/:action',
                element: window.innerWidth > 991 ? <BasicInfoPage /> : <HealthProfilePage />,
              },
            ],
          },
          {
            path: 'documents',
            element: (
              <ChakraProvider theme={medicalRecordTheme}>
                <Outlet />
              </ChakraProvider>
            ),
            children: [
              {
                path: '',
                element: <MedicalRecordsPage />,
                children: [
                  {
                    path: ':action',
                    loader: () => MEDICAL_RECORD_BEHOLDER_TYPES.OWN,
                    element: <MedicalRecordList key={MEDICAL_RECORD_BEHOLDER_TYPES.OWN} />,
                  },
                  {
                    path: ':active/:action',
                    loader: () =>
                      window.location.pathname.includes('documents/others')
                        ? MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS
                        : MEDICAL_RECORD_BEHOLDER_TYPES.OWN,
                    element: (
                      <MedicalRecordList
                        key={
                          window.location.pathname.includes('documents/others')
                            ? MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS
                            : MEDICAL_RECORD_BEHOLDER_TYPES.OWN
                        }
                      />
                    ),
                  },
                  {
                    path: 'others/view',
                    loader: () => MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS,
                    element: <MedicalRecordList key={MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS} />,
                  }
                ],
              },
            ],
          },
          {
            path: '/help/faqs/view',
            element: <FaqsPage />,
          },
          {
            path: 'settings',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <SettingsPage />,
              },

              {
                path: 'settings/consent',
                element: <SettingsPage />,
              },
              {
                path: 'settings/communication',
                element: <SettingsPage />,
              },
              {
                path: 'settings/consent',
                element: <SettingsPage />,
              },
              {
                path: 'settings/personal',
                element: <SettingsPage />,
              },
              {
                path: 'settings/third-party',
                element: <SettingsPage />,
              },
              {
                path: 'social',
                element: <SocialMediaPage />,
              },
              {
                path: 'legal',
                element: <Outlet />,
                children: [
                  {
                    path: '',
                    element: <LegalPage />,
                  },
                  {
                    path: 'privacy-policy/view',
                    element: <PrivacyPolicyPage />,
                  },
                  {
                    path: 'cookie-policy/view',
                    element: <CookiePolicyPage />,
                  },
                  {
                    path: 'communications-consent/view',
                    element: <CommunicationsAndConsentPage />,
                  },
                  {
                    path: 'medical-advice-disclaimer/view',
                    element: <MedicalAdviceDisclaimer />,
                  },
                ],
              },
            ],
          },
          {
            path: 'settings/measurement',
            element: <MeasurementPreferencePage />,
          },
          {
            path: 'settings/communication',
            element: <CommunicationPreferencePage />,
          },
          {
            path: 'settings/personal-data-usage',
            element: <PersonalDataUsage />,
          },
          {
            path: 'settings/fluent-support',
            element: <FluentSupportPage />,
          },
          {
            path: 'settings/account/takeout/view',
            element: <DownloadPersonalDataPage />,
          },
          {
            path: 'settings/account/delete/view',
            element: <DeletePersonalDataAccountPage />,
          },
        ],
      },
    ],
  },
  {
    path: 'share-profile',
    element: <Root />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: '',
        element: (
          <ChakraProvider theme={profileRecordTheme}>
            <ShareProfilePage />
          </ChakraProvider>
        ),
      },
    ],
  },
  {
    path: 'share-records/:shareId',
    element: <Root />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: '',
        element: (
          <ChakraProvider theme={profileRecordTheme}>
            <PublicMedicalRecordListPage />
          </ChakraProvider>
        ),
      },
    ],
  },
]);
