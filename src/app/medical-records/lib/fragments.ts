import { COMM_REMINDER_EXTENSION } from './constants';

export const commonFragments = `
  fragment PatientFields on Patient {
    id
    name {
      family
      prefix
      given
      use
    }
  }

  fragment PractitionerFields on Practitioner {
    id
    name {
      family
      prefix
      given
      use
    }
  }

  fragment DocumentReferenceFields on DocumentReference {
    id
    type {
      coding {
        display
        code
        system
      }
    }
    description
    extension {
      url
      valueCode
      valueString
      valueBoolean
      valueDateTime
      valueCoding {
        code
        display
        system
      }
    }
    status
    docStatus
    subject {
      reference
      resource {
        ...PatientFields
        ...PractitionerFields
      }
    }
    content {
      attachment {
        url
        size
        title
        hash
        creation
        contentType
      }
    }
    context {
      period {
        start
        end
      }
      encounter {
        reference
        resource {
          ... on Encounter {
            id
            class {
              code
              display
              system
            }
          }
        }
      }
      related {
        reference
        resource {
          ... on DiagnosticReport {
            __typename
            id
            status
            code {
              coding {
                code
                display
                system
              }
            }
            subject {
              reference
            }
            issued
            performer {
              reference
            }
            result {
              reference
              resource {
                ... on Observation {
                  id
                  status
                  code {
                    coding {
                      code
                      display
                      system
                    }
                  }
                  subject {
                    reference
                  }
                  component {
                    code {
                      coding {
                        code
                        system
                        display
                      }
                    }
                  }
                  valueString
                  valueQuantity {
                    value
                    unit
                    system
                    code
                  }
                  referenceRange {
                    low {
                      value
                      unit
                    }
                    high {
                      value
                      unit
                    }
                  }
                }
              }
            }
          }
          ... on MedicationStatement {
            __typename
            id
            extension {
              url
              valueBoolean
            }
          }
          ... on Practitioner {
            __typename
            ...PractitionerFields
            name {
              text
            }
          }
          ... on PractitionerRole {
            __typename
            id
            specialty {
              coding {
                code
                display
                system
              }
            }
          }
          ... on Condition {
            __typename
            id
            conditionCode: code {
              coding {
                display
                code
                system
              }
            }
          }
          ... on Organization {
            __typename
            id
            hospitalName: name
          }
          ... on FamilyMemberHistory {
            __typename
            id
            familyMemberName: name
          }
          ... on CommunicationRequest {
            __typename
            id
            payload {
              contentAttachment {
                title
              }
            }
            occurrenceDateTime
            note {
              text
            }
            status
            frequency: extension(url: "${COMM_REMINDER_EXTENSION}") {
              extension {
                valueCode
                valueBoolean
                url
              }
            }
          }
        }
      }
    }
    meta {
      tag {
        code
        display
        system
      }
    }
  }

  fragment CommonTaskFields on Task {
    id
    identifier {
      value
    }
    meta {
      lastUpdated
    }
    businessStatus {
      coding {
        code
        display
        system
      }
    }
    output {
      type {
        coding {
          code
          display
        }
      }
     valueString
    }
    intent
    status
    for {
      reference
      resource {
        ...PatientFields
        ...PractitionerFields
      }
    }
    requester {
      reference
      resource {
        ...PatientFields
        ...PractitionerFields
      }
    }
    owner {
      reference
      resource {
        ...PatientFields
        ...PractitionerFields
      }
    }
    focus {
      reference
      resource {
        ...DocumentReferenceFields
      }
    }
  }
`;
