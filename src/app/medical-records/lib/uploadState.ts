import create from 'zustand';

interface UploadFile {
  file: File;
  progress: number;
  error?: string;
  isRetrying?: boolean;
  controller?: AbortController;
}

interface FileUploadState {
  files: { [key: string]: UploadFile };
  addFile: (file: File, controller?: AbortController) => void;
  updateProgress: (fileName: string, progress: number) => void;
  setError: (fileName: string, error: string, isRetrying?: boolean) => void;
  retryUpload: (file: any) => void;
  cancelUpload: (fileName: string) => void;
  removeUpload: (fileName: string) => void;
  clearFiles: () => void;
}

export const useFileUploadStore = create<FileUploadState>((set) => ({
  files: {},

  addFile: (file, controller) =>
    set((state) => ({
      files: {
        ...state.files,
        [file.name]: { file, progress: 0, error: undefined, controller },
      },
    })),
  updateProgress: (fileName, progress) =>
    set((state) => {
      const file = state.files[fileName];
      if (!file) return {}; // Don't update if file doesn't exist
      return {
        files: {
          ...state.files,
          [fileName]: { ...file, progress },
        },
      };
    }),

  setError: (fileName, error, isRetrying) =>
    set((state) => {
      const file = state.files[fileName];
      if (!file) return {}; // Don't update if file doesn't exist
      return {
        files: {
          ...state.files,
          [fileName]: { ...file, error, isRetrying },
        },
      };
    }),
  retryUpload: (file) => {
    set((state) => {
      const updatedFiles = { ...state.files };
      const fileName = file?.name || file?.path;
      updatedFiles[fileName]?.controller?.abort();
      delete updatedFiles[fileName];
      return { files: { ...updatedFiles, [fileName]: { ...file, progress: 0, error: undefined } } };
    });
  },

  cancelUpload: (fileName) =>
    set((state) => {
      const updatedFiles = { ...state.files };
      const fileToCancel = updatedFiles[fileName];
      if (fileToCancel?.controller) {
        fileToCancel.controller.abort();
      }
      delete updatedFiles[fileName];
      return { files: updatedFiles };
    }),

  removeUpload: (fileName) =>
    set((state) => {
      const updatedFiles = { ...state.files };
      delete updatedFiles[fileName];
      return { files: updatedFiles };
    }),
  clearFiles: () => set({ files: {} }),
}));
