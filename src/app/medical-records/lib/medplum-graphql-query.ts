import { ValueSetUrls } from '@lib/models/medical-record';
import { commonFragments } from './fragments';

const generateQuery = (name: string, taskArgs: string) => `
  query ${name} {
    ${name}: TaskList(${taskArgs}) {
      ...CommonTaskFields
    }
  }
  ${commonFragments}
`;

export const getDocData = {
  getTaskQuery({ identifier, patientId }: any) {
    return generateQuery(
      'TaskList',
      `
      identifier: "${identifier}"
      subject: "${patientId}"
      _sort: "-_lastUpdated"
      _count: 1000
    `
    );
  },
  getOrganization() {
    const query = `query OrganizationList {
      OrganizationList(name: "Fluent Health") {
        id
      }
    }`;
    return query;
  },
};

export const getFileFromTask = `
  query getTask($id: ID!) {
      Task(id: $id){
        id
        status
        output {
          type {
            coding {
              code
              display
            }
          }
          valueReference {
            reference
          }
        }
      }
    }`;

export const getDocumentReference = `
  query getDocumentReference($id: ID!) {
    DocumentReference(id: $id) {
      id
      status
      type {
        coding {
          code
          display
        }
      }
      content {
        attachment {
          contentType
          url
          title
          creation
        }
      }
      subject {
        reference
        display
      }
      author {
        reference
        display
      }
      date
      description
    }
  }
`;

export const getObservationList = `
  query GetObservation($id: ID!) {
    Observation(id: $id) {
      id
      status
      category {
          coding {
              system
              code
              display
          }
      }
      code {
          coding {
              code
              system
              display
          }
      }
      effectiveDateTime
      component {
          code {
              coding {
                  system
                  code
                  display
              }
          }

          valueQuantity {
              value
              unit
              system
              code
          }
          referenceRange {
              low {
                  value
                  unit
                  system
                  code
              }
              high {
                  value
                  unit
                  system
                  code
              }
          }
      }
      valueQuantity {
          value
          unit
          system
          code
      }
      referenceRange {
          low {
              value
              unit
              system
              code
          }
          high {
              value
              unit
              system
              code
          }
      }
    }
  }
`;

export const getDocRefQuery = `
  query DocRef($id: ID!) {
    DocumentReference(id: $id) {
      meta {
        tag {
          code
          display
          system
        }
      }
    }
  }
`;

const valueSets = {
  getValueSets({ urls }: { urls: ValueSetUrls[] }) {
    let query = '';
    urls?.forEach(({ type, url }) => {
      query += `
        ${type}: ValueSetList(url: "${url}") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
      `;
    });

    const finalQuery = `
    query ValueSets {
      ${query}
    }
    `;

    return finalQuery;
  },
};

export const getListOfTags = /* GraphQL */ `
  query List($patientId: String!, $code: String) {
    ListList(patient: $patientId, code: $code) {
      resourceType
      id
      entry {
        item {
          type
          display
        }
      }
      code {
        coding {
          code
          display
        }
      }
      subject {
        reference
      }
    }
  }
`;

export const medplumGraphQlQuery = {
  valueSets,
};
