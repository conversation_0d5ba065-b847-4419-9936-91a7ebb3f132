import axios from 'axios';
import { <PERSON>uffer } from 'buffer';
import { Client } from 'urql';
import { DOCUMENT_REF } from '@user/lib/constants';
import { medplumApi as medplumApiUser } from '@user/lib/medplum-api';

import { MEDPLUM_API_URL } from '@lib/constants';
import { AuthService } from '@lib/authService';
import { getDocRefQuery, medplumGraphQlQuery } from './medplum-graphql-query';
import { Coding } from 'src/gql/graphql';
import { FH_UI_CODESYSTEM } from './constants';
import ApiError from '../../../components/error/ApiError';

const medicalRecord = {
  async addOne(payload: any) {
    try {
      const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });
      return response;
    } catch (error) {
      throw new ApiError(error);
    }
  },

  async updateOne(payload: any) {
    try {
      await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });
    } catch (error) {
      throw new ApiError(error);
    }
  },

  async deleteOne(recordId: string) {
    const dataDocumentReference: string = Buffer.from(
      JSON.stringify([{ op: 'replace', path: '/status', value: 'superseded' }]),
      'utf8'
    ).toString('base64');
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          request: {
            method: 'POST',
            url: 'Task',
          },
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'unknown',
            identifier: [
              {
                value: 'urn:fh-workflow:task:delete:document',
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: 'task-input-type:delete',
                    },
                  ],
                },
                valueReference: {
                  reference: `${DOCUMENT_REF}/${recordId}`,
                },
              },
            ],
          },
        },
        {
          request: {
            method: 'PATCH',
            url: `${DOCUMENT_REF}/${recordId}`,
          },
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${dataDocumentReference}}}`,
          },
        },
      ],
    };

    try {
      await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });
    } catch (error) {
      throw new ApiError(error);
    }
  },
};

interface PatchOperation {
  op: 'add' | 'remove';
  path: string;
  value?: Coding[];
}

type PatchPayloadTypes = {
  add: PatchOperation;
  remove: PatchOperation;
};

type BookmarkParamTypes = {
  id: string;
  client: Client;
  operationType: 'add' | 'remove';
  bookMarkIdx?: number;
  onSuccess: Function;
};

export async function bookmarkARecord(props: BookmarkParamTypes) {
  const { id, client, operationType, onSuccess } = props;

  let bookmarkedIdx = -1;
  try {
    const response = await client.query(getDocRefQuery, { id });
    const tag = response?.data?.DocumentReference?.meta?.tag || [];
    if (operationType === 'remove') {
      bookmarkedIdx = tag?.findIndex((tagObj: any) => tagObj?.code === 'mr-book') ?? -1;
    }

    const PAYLOAD_MAPPING: PatchPayloadTypes = {
      add: {
        op: 'add',
        path: '/meta/tag',
        value: [
          ...tag,
          {
            system: FH_UI_CODESYSTEM,
            code: 'mr-book',
            display: 'Bookmarked',
          },
        ],
      },
      remove: {
        op: 'remove',
        path: `/meta/tag/${bookmarkedIdx}`,
      },
    };

    await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/${DOCUMENT_REF}/${id}`, [PAYLOAD_MAPPING[operationType]], {
      headers: AuthService.instance.withAuthHeader(),
    });

    onSuccess();
  } catch (error) {
    console.error({ error });
  }
}
export async function supersededRecord({ id }: any) {
  try {
    const PAYLOAD_MAPPING = {
      op: 'add',
      path: '/status',
      value: 'superseded',
    };
    await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/${DOCUMENT_REF}/${id}`, [PAYLOAD_MAPPING], {
      headers: AuthService.instance.withAuthHeader(),
    });
  } catch (error) {
    console.error({ error });
  }
}

export async function createDownloadRecord(payload: any) {
  const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
    headers: AuthService.instance.withAuthHeader(),
  });
  if (status !== 200) throw new ApiError(data);
  return data;
}

const valueSets = {
  async getAll({ urls }: { urls: any }) {
    const {
      data: { data },
      status,
    } = await axios.post(
      `${MEDPLUM_API_URL}/fhir/R4/$graphql`,
      {
        query: medplumGraphQlQuery.valueSets.getValueSets({ urls }),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
  async getAllExpand({ urls }: { urls: { type: string; url: string }[] }) {
    const entries = await Promise.all(
      urls.map(({ type, url }) =>
        medplumApiUser.valueSetList.getAllValueSetFromDirectus(false, url).then((details) => [type, details])
      )
    );
    return Object.fromEntries(entries);
  },
};

export const medplumApi = {
  medicalRecord,
  valueSets,
};
