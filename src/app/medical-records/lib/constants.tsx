import { ReactNode } from 'react';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { MEDICAL_RECORD_BEHOLDER_TYPES, MEDICAL_RECORD_TYPES, ValueSetUrls } from '@lib/models/medical-record';
import { FHIR_CODE_SYSTEM_URL, FHIR_STRUCTURE_DEFINITION_URL, FHIR_URL, FHIR_VALUE_SET_URL } from '@lib/constants';

import { ReactComponent as LabResultsIcon } from '@assets/icons/lab-results.svg';
import { ReactComponent as ConsultationIcon } from '@assets/icons/consultation.svg';
import { ReactComponent as GeneralIcon } from '@assets/icons/general.svg';
import { ReactComponent as VaccineIcon } from '@assets/icons/immunization.svg';
import { ReactComponent as ImagingIcon } from '@assets/icons/imaging.svg';
import { ReactComponent as PrescriptionIcon } from '@assets/icons/prescriotion.svg';
import { ReactComponent as InvoiceIcon } from '@assets/icons/invoice.svg';

export const MEDICAL_RECORD_TYPE = 'medical-record-type-of-record';

export const MEDICAL_RECORD_ICON_MAP: Record<string, ReactNode> = {
  [MEDICAL_RECORD_TYPES.LAB_RESULTS]: (
    <LabResultsIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.CONSULTATION_NOTE]: (
    <ConsultationIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.GENERAL]: (
    <GeneralIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.VACCINE_RECORD]: (
    <VaccineIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.PRESCRIPTIONS]: (
    <PrescriptionIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.INVOICE]: (
    <InvoiceIcon
      width="100%"
      height="100%"
    />
  ),
  [MEDICAL_RECORD_TYPES.IMAGING_RECORD]: (
    <ImagingIcon
      width="100%"
      height="100%"
    />
  ),
};

export const MEDICAL_RECORD_ICON_BG_MAP: Record<string, string> = {
  [MEDICAL_RECORD_TYPES.LAB_RESULTS]: '#FFF3F0',
  [MEDICAL_RECORD_TYPES.CONSULTATION_NOTE]: '#E9F4F1',
  [MEDICAL_RECORD_TYPES.GENERAL]: '#F5F9FF',
  [MEDICAL_RECORD_TYPES.VACCINE_RECORD]: '#F3F4FF',
  [MEDICAL_RECORD_TYPES.PRESCRIPTIONS]: '#F3F4FF',
  [MEDICAL_RECORD_TYPES.INVOICE]: '#EBFAF6',
  [MEDICAL_RECORD_TYPES.IMAGING_RECORD]: '#FEF7ED',
};

export const MEDICAL_RECORD_ICON_HOVE_BG_MAP: Record<string, string> = {
  [MEDICAL_RECORD_TYPES.LAB_RESULTS]: '#FFE5DD',
  [MEDICAL_RECORD_TYPES.CONSULTATION_NOTE]: '#D8F0EA',
  [MEDICAL_RECORD_TYPES.GENERAL]: '#E7F0FF',
  [MEDICAL_RECORD_TYPES.VACCINE_RECORD]: '#E7E9FF',
  [MEDICAL_RECORD_TYPES.PRESCRIPTIONS]: '#E7E9FF',
  [MEDICAL_RECORD_TYPES.INVOICE]: '#DFFBF3',
  [MEDICAL_RECORD_TYPES.IMAGING_RECORD]: '#FFF0DA',
};

// Todo the below constant can be removed
export const MEDICAL_RECORD_NAME_MAP: Record<string, string> = {
  [MEDICAL_RECORD_TYPES.LAB_RESULTS]: 'Lab Result',
  [MEDICAL_RECORD_TYPES.CONSULTATION_NOTE]: 'Consultation Note',
  [MEDICAL_RECORD_TYPES.GENERAL]: 'General',
  [MEDICAL_RECORD_TYPES.VACCINE_RECORD]: 'Vaccine',
  [MEDICAL_RECORD_TYPES.PRESCRIPTIONS]: 'Prescription',
  [MEDICAL_RECORD_TYPES.INVOICE]: 'Invoice',
  [MEDICAL_RECORD_TYPES.IMAGING_RECORD]: 'Diagnostic Scan',
};

// Utility function to convert bytes to MB for analytics
export const convertBytesToMB = (bytes: number): number => {
  if (!bytes || bytes <= 0) return 0;
  return Number((bytes / (1024 * 1024)).toFixed(2));
};

export const SORT_BY_VALUES = {
  ALPHABETIC: 'description',
  REVERSE_ALPHABETIC: '-description',
  RECENT: '-date',
  LEAST_RECENT: 'date',
};

export const SORT_BY_OPTION_LIST = [
  { label: 'Alphabetical (A-Z)', value: SORT_BY_VALUES.ALPHABETIC },
  { label: 'Reverse Alphabetical (Z-A)', value: SORT_BY_VALUES.REVERSE_ALPHABETIC },
  { label: 'Most recent', value: SORT_BY_VALUES.RECENT },
  { label: 'Least recent', value: SORT_BY_VALUES.LEAST_RECENT },
];

export const MEDICAL_RECORD_BEHOLDER_TYPES_MAP: Record<string, string> = {
  [MEDICAL_RECORD_BEHOLDER_TYPES.OWN]: 'Myself',
  [MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS]: 'Someone else',
};

export const EDIT_RECORD_VALUESET_URLS: ValueSetUrls[] = [
  { type: 'conditionList', url: 'FACT-Category-cn' },
  { type: 'specialityList', url: 'FACT-sty' },
  { type: 'typeOfVisitList', url: 'EncounterClass' },
];

export const ALLERGIES_VALUESET_URLS: ValueSetUrls[] = [
  { type: 'categoryList', url: `${FHIR_VALUE_SET_URL}/AllergiesIntolerancesCategory` },
  { type: 'criticalityList', url: `${FHIR_VALUE_SET_URL}/AllergiesIntolerancesCriticality` },
  { type: 'clinicalStatusList', url: `${FHIR_VALUE_SET_URL}/allergyintolerance-clinical` },
];

export const EDIT_ALERT_FREQUENCY_VALUESET_URLS: ValueSetUrls[] = [
  { type: 'frequencyList', url: `${FHIR_VALUE_SET_URL}/reminder-frequency` },
  { type: 'customFrequencyList', url: `${FHIR_VALUE_SET_URL}/CustomReminderFrequencyBase` },
];

export const QUESTIONNAIRE_URL = `${FHIR_URL}/Questionnaire/MedicalRecord`;

export const FH_UI_CODESYSTEM = `${FHIR_CODE_SYSTEM_URL}/FluentHealthUI`;
export const USER_META_CODESYSTEM = `${FHIR_CODE_SYSTEM_URL}/UserMetaTag`;
export const FACT_CODESYSTEM = `${FHIR_CODE_SYSTEM_URL}/FACT`;
export const TERMINOLOGY_CODE_SYSTEM = `http://terminology.hl7.org/CodeSystem`;
export const ACTCODE_CODESYSTEM = `${TERMINOLOGY_CODE_SYSTEM}/v3-ActCode`;
export const COMM_REMINDER_EXTENSION = `${FHIR_STRUCTURE_DEFINITION_URL}/CommunicationRequestReminder`;

const { DOCUMENTS, PERSONAL, REVIEW, OTHERS } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;
export const MEDICAL_RECORD_TAB_LINKS: Record<string, string> = {
  MY_RECORDS: `/${DOCUMENTS}/${PERSONAL}/${VIEW}`,
  OTHERS_RECORDS: `/${DOCUMENTS}/${OTHERS}/${VIEW}`,
  IN_REVIEW_RECORDS: `/${DOCUMENTS}/${REVIEW}/${VIEW}`,
};

export const CODE_SYSTEMS_MAPPING: Record<string, string> = {
  report_type: FH_UI_CODESYSTEM,
  tags: USER_META_CODESYSTEM,
  starred: FH_UI_CODESYSTEM,
  status: FH_UI_CODESYSTEM,
};

export const QUESTIONNAIRE_EDIT_URL = `${FHIR_URL}/Questionnaire/MedicalRecordEdit`;

export const QUESTIONNAIRE_RESP_ID = 'cfbaafee-a358-42d2-9f6c-6ee89b3dc213';

export const NON_FLUENT_PRACTITIONER_ID = '39e96537-eb2c-494b-9527-e335523cfe87';

export const identifierUrn = 'urn:fh-workflow:task:document-engine';

export const documentUploadWorkflow = {
  doucmentUpload: {
    started: 'document-engine:patient:document-upload:started',
    completed: 'document-engine:patient:document-upload:completed',
  },
  passwordSubmission: {
    started: 'document-engine:patient:password-submission:started',
    cancelled: 'document-engine:patient:password-submission:cancelled',
    inProgress: 'document-engine:patient:password-submission:in-progress',
    completed: 'document-engine:patient:password-submission:completed',
    required: 'document-engine:patient:document-decryption:required',
    failed: 'document-engine:patient:document-decryption:failed',
  },
  documentInReview: {
    notCompleted: 'document-engine:workflow:process:completed',
  },
  documentActionNeeded: {
    error: 'entered-in-error',
  },
  documentCompleted: {
    completed: 'document-engine:workflow:process:completed',
  },
};

export const documentRejectionReason = {
  medicallyIrrelevant: 'document-engine:boom:review:rejected:medicallyIrrelevant',
  blurryDocument: 'document-engine:boom:review:rejected:blurryDocument',
  missingData: 'document-engine:boom:review:rejected:missingData',
  nameNotFound: 'document-engine:boom:review:rejected:nameNotFound',
  nameMismatch: 'document-engine:boom:review:rejected:nameMismatch',
  other: 'document-engine:boom:review:rejected:other',
};

export const documentRejectionMessages = {
  [documentRejectionReason.medicallyIrrelevant]: {
    title: 'File Rejected',
    body: "This document doesn't contain relevant medical information. Please upload a valid health record or report.",
  },
  [documentRejectionReason.blurryDocument]: {
    title: 'File Rejected',
    body: 'Your document is too blurry to read clearly. Please upload a sharper, clearer version.',
  },
  [documentRejectionReason.missingData]: {
    title: 'File Rejected',
    body: 'Important information is missing from this document. Please upload a complete version with all required details.',
  },
  [documentRejectionReason.nameNotFound]: {
    title: 'File Rejected',
    body: "We couldn't find your name on this document. Please upload a record that clearly shows your personal details.",
  },
  [documentRejectionReason.nameMismatch]: {
    title: 'File Rejected',
    body: "The name on this document doesn't match your profile. Please upload a record with your correct name.",
  },
  [documentRejectionReason.other]: {
    title: 'File Rejected',
    body: "This document couldn't be processed. Please try uploading again or contact support if the problem continues.",
  },
};

/**
 * Helper function to get rejection message by code
 * @param rejectionCode - The rejection code from documentRejectionReason
 * @returns Object with title and body, or default message if code not found
 */
export const getDocumentRejectionMessage = (rejectionCode: string) => {
  return (
    documentRejectionMessages[rejectionCode] || {
      title: 'File Rejected',
      body: 'Your document has been rejected as it is blurry and unusable. Please re-upload a clearer version.',
    }
  );
};

export const MAX_SIZE_MB = 25;
export const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;

export const documentPasswordSubmission = new Set([
  documentUploadWorkflow.passwordSubmission.failed,
  documentUploadWorkflow.passwordSubmission.required,
  documentUploadWorkflow.passwordSubmission.cancelled,
]);
