import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Badge,
  Box,
  Flex,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react';
import { enumStatus } from '@user/lib/constants';
import { recordHealthRecordHubEvents } from '@user/lib/events-analytics-manager';

import { FilesPreview } from './components/FilesPreview';
import { useAnalyticsService } from '@lib/state';

import { ReactComponent as Settings } from '@assets/icons/settings.svg';
import { ReactComponent as Mail } from '@assets/icons/email-review.svg';

enum enumType {
  OTHERS = 'others',
  EMAIL = 'email',
}
interface MedicalRecord {
  id: string;
  title: string;
  type: 'others' | 'email';
  email?: string;
  status: enumStatus.PROCESSING;
  files?: any[];
}

interface MedicalRecordInReviewListProps {
  medicalRecordListReview: any[];
  medicalRecordListActionNeeded: any[];

  // fetchMedicalRecord: () => void; // Will be use in future
}

// Function to check if a DocumentReference is from an email source
export const isEmailRecord = (record: any): boolean => {
  return record?.meta?.tag?.some(
    (tag: any) =>
      tag?.system === 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI' && tag?.code === 'source:email'
  );
};

// Function to transform API response into required format
const formatMedicalRecords = (medicalRecordList: any[]): MedicalRecord[] => {
  return medicalRecordList.map((record) => {
    const doc = record?.focus?.resource;
    return {
      id: record.id,
      title: doc?.content?.[0]?.attachment?.title || 'Unknown Title',
      type: isEmailRecord(doc) ? 'email' : 'others',
      email: isEmailRecord(doc) ? record?.author?.[0]?.display : undefined,
      status: enumStatus.PROCESSING,
      files: doc?.content?.map((c: any) => c.attachment) || [],
    };
  });
};

export function MedicalRecordInReviewList({
  medicalRecordListReview,
  medicalRecordListActionNeeded,
}: MedicalRecordInReviewListProps) {
  const { trackEventInFlow } = useAnalyticsService();

  // Responsive values for font and icon sizes
  const fontSize = useBreakpointValue({ base: '16px', md: '20px' });
  const titleFontSize = useBreakpointValue({ base: '16px', md: '20px' });
  const emailFontSize = useBreakpointValue({ base: '14px', md: '16px' });
  const iconSize = useBreakpointValue({ base: '64px', md: '68px' });

  // Transform medical records
  const records = formatMedicalRecords(medicalRecordListReview);
  if (!records.length) return null;

  return (
    <Accordion
      allowToggle
      defaultIndex={medicalRecordListActionNeeded?.length > 0 ? [] : [0]}
      borderRadius="12px"
      background="#FFF"
      mt="16px"
      mb="24px"
    >
      <AccordionItem border="none">
        {({ isExpanded }) => (
          <>
            <AccordionButton
              onClick={() => {
                recordHealthRecordHubEvents(trackEventInFlow, {
                  EventName: 'HealthRecordHubInteracted',
                  hrh_tab_name: 'processing',
                });
              }}
              padding={{ base: '8px 12px', md: '10px 16px' }}
              borderRadius="12px"
              background="iris.100"
              color="iris.600"
              fontSize={fontSize}
              fontWeight="400"
              lineHeight="28px"
              letterSpacing="-0.4px"
              _hover={{ background: 'iris.100' }}
            >
              <Box
                flex="1"
                textAlign="left"
              >
                Processing ({records.length})
              </Box>
              <AccordionIcon
                width="24px"
                height="24px"
                transform={isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
              />
            </AccordionButton>

            <AccordionPanel padding={{ base: '8px', md: '12px' }}>
              {records.map((record) => (
                <FilesPreview
                  key={record.id}
                  files={record?.files}
                >
                  {({ onClick }) => (
                    <Box
                      border="1px solid"
                      borderColor="papaya.400"
                      borderRadius="12px"
                      padding={{ base: '8px', md: '12px' }}
                      marginBottom="10px"
                      background="beige.100"
                      cursor="pointer"
                      onClick={() => {
                        onClick();
                        recordHealthRecordHubEvents(trackEventInFlow, {
                          EventName: 'ProcessingRecordInteracted',
                          pri_record_type: record.type,
                          document_id: record.id,
                        });
                      }}
                    >
                      <Flex align="center">
                        <Flex align="center">
                          <Box
                            flexShrink={0}
                            width={iconSize}
                            height={iconSize}
                            display="flex"
                            alignItems="center"
                          >
                            {record.type === enumType.OTHERS ? (
                              <Settings
                                width="100%"
                                height="100%"
                              />
                            ) : (
                              <Mail
                                width="100%"
                                height="100%"
                              />
                            )}
                          </Box>

                          <Box
                            flex="1"
                            ml="12px"
                          >
                            <Text
                              fontSize={titleFontSize}
                              fontWeight="400"
                              ml="16px"
                              mr="16px"
                              color="gray.500"
                              lineHeight="24px"
                              letterSpacing="-0.48px"
                              wordBreak="break-word"
                              whiteSpace="normal"
                              overflowWrap="break-word"
                            >
                              {record.title}
                            </Text>
                            {record.email && (
                              <Text
                                fontSize={emailFontSize}
                                fontWeight="400"
                                color="charcoal.60"
                                ml="16px"
                                mr="16px"
                                lineHeight="20px"
                                letterSpacing="-0.32px"
                                mt="4px"
                                wordBreak="break-word"
                                whiteSpace="normal"
                                overflowWrap="break-word"
                              >
                                {record.email}
                              </Text>
                            )}
                          </Box>
                        </Flex>

                        <Badge
                          background="papaya.400"
                          color="#FFF"
                          borderRadius="8px"
                          padding="4px 8px"
                          fontSize={{ base: '10px', md: '12px' }}
                        >
                          {record.status}
                        </Badge>
                      </Flex>
                    </Box>
                  )}
                </FilesPreview>
              ))}
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    </Accordion>
  );
}
