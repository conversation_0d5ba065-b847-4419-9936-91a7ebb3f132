// Package modules
import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Box, Button, CheckboxGroup, Flex, Heading, Text, useCheckboxGroup, useRadioGroup } from '@chakra-ui/react';

// Local modules
import { useStepper } from '../lib/utils';
import { CheckboxButton } from './CheckboxButton';
import { RadioButton } from './RadioButton';

// Constants
const STEPS = {
  FIRST: 1,
  SECOND: 2,
  FINISH: 3,
};

const MOCK_QUIZ_DATA = {
  exerciseTypes: [
    { id: 1, name: 'Weight lifting' },
    { id: 2, name: 'Running' },
    { id: 3, name: 'Yoga' },
    { id: 4, name: '<PERSON><PERSON>' },
    { id: 5, name: 'Stretching' },
    { id: 6, name: 'Callisthenics' },
    { id: 7, name: 'Pilates' },
  ],
  weekExercises: [
    { id: '1', name: 'Everyday' },
    { id: '2', name: '2 - 4 times a week' },
    { id: '3', name: 'Once a week' },
  ],
};

interface ICheckboxButtonsStep {
  title: string;
  selectedItemIds: number[];
  items: any[];
  canGoToPrevStep?: boolean;
  onSubmit: (data: any) => void;
  onPrev?: () => void;
}
function CheckboxButtonsStep({
  title,
  selectedItemIds = [],
  items,
  canGoToPrevStep,
  onSubmit,
  onPrev,
}: ICheckboxButtonsStep) {
  const { value, getCheckboxProps } = useCheckboxGroup({ defaultValue: selectedItemIds });
  const canSubmit = value.length > 0;

  return (
    <>
      <Heading
        size="lg"
        fontSize="3xl"
        lineHeight="11"
        maxWidth="500px"
        textAlign="center"
        marginBottom="60px"
      >
        {title}
      </Heading>
      <CheckboxGroup>
        <Flex
          justify="center"
          marginBottom="60px"
        >
          <Flex
            wrap="wrap"
            justify="center"
            maxW="400px"
            gap="4px"
          >
            {items.map((item) => (
              <CheckboxButton
                key={item.id}
                checkboxProps={getCheckboxProps({ value: item.id })}
              >
                <Text color="fluentHealthSecondary.100">{item.name}</Text>
              </CheckboxButton>
            ))}
          </Flex>
        </Flex>
      </CheckboxGroup>
      <Flex
        justify={canGoToPrevStep ? 'space-between' : 'center'}
        align="center"
      >
        {canGoToPrevStep && (
          <Button
            variant="ghost"
            fontSize="16px"
            onClick={onPrev}
            color="fluentHealthSecondary.150"
            px="0"
            leftIcon={
              <ChevronLeftIcon
                width="18px"
                height="18px"
              />
            }
          >
            Previous
          </Button>
        )}
        <Button
          onClick={() => onSubmit(value)}
          isDisabled={!canSubmit}
          fontSize="16px"
          rightIcon={
            <ChevronRightIcon
              width="18px"
              height="18px"
            />
          }
        >
          Next
        </Button>
      </Flex>
    </>
  );
}

interface IRadioButtonsStep {
  title: string;
  groupName: string;
  initialValueId: string | null;
  items: any[];
  canGoToPrevStep?: boolean;
  onSubmit: (data: any) => void;
  onPrev?: () => void;
}
function RadioButtonsStep({
  title,
  groupName,
  initialValueId,
  items,
  canGoToPrevStep,
  onPrev,
  onSubmit,
}: IRadioButtonsStep) {
  const { value, getRootProps, getRadioProps } = useRadioGroup({
    name: groupName,
    defaultValue: initialValueId || '',
  });

  let canSubmit = false;
  if (typeof value !== 'number') {
    canSubmit = value.length > 0;
  }

  const group = getRootProps();

  return (
    <>
      <Heading
        size="lg"
        fontSize="3xl"
        lineHeight="11"
        maxWidth="500px"
        textAlign="center"
        marginBottom="60px"
      >
        {title}
      </Heading>
      <Flex
        align="center"
        direction="column"
        marginBottom="60px"
      >
        <Flex
          width="300px"
          direction="column"
          gap="16px"
          {...group}
        >
          {items.map((item) => {
            const radio = getRadioProps({ value: item.id });
            return (
              <RadioButton
                key={item.id}
                radioProps={radio}
              >
                {item.name}
              </RadioButton>
            );
          })}
        </Flex>
      </Flex>
      <Flex
        justify={canGoToPrevStep ? 'space-between' : 'center'}
        align="center"
      >
        {canGoToPrevStep && (
          <Button
            variant="ghost"
            fontSize="16px"
            onClick={onPrev}
            color="fluentHealthSecondary.150"
            px="0"
            leftIcon={
              <ChevronLeftIcon
                width="18px"
                height="18px"
              />
            }
            transition="gap 0.1s ease"
            gap={0}
            _hover={{ gap: '4px' }}
          >
            Previous
          </Button>
        )}
        <Button
          onClick={() => onSubmit(value)}
          isDisabled={!canSubmit}
          fontSize="16px"
          rightIcon={
            <ChevronRightIcon
              width="18px"
              height="18px"
            />
          }
        >
          Next
        </Button>
      </Flex>
    </>
  );
}

export function QuizStepper() {
  // State
  const { stepperState, currentStep, goToNextStep, goToPrevStep, canGoToPrevStep, updateState } = useStepper(
    Object.values(STEPS).length,
    {
      exerciseTypes: [3, 4],
      weekExercises: null,
    }
  );

  // Handlers
  const handleExerciseTypesStepSubmit = (data: any) => {
    goToNextStep();
    updateState((prevState) => ({
      ...prevState,
      exerciseTypes: data,
    }));
  };

  const handleWeekExercisesStepSubmit = (data: any) => {
    goToNextStep();
    updateState((prevState) => ({
      ...prevState,
      weekExercises: data,
    }));
  };

  return (
    <Box
      width="560px"
      bg="white"
      borderRadius="20px"
      pt="48px"
      pb="24px"
      px="38px"
    >
      {currentStep === STEPS.FIRST && (
        <CheckboxButtonsStep
          title="What exercise activities do you currently take part in ?"
          onSubmit={handleExerciseTypesStepSubmit}
          selectedItemIds={stepperState.exerciseTypes}
          items={MOCK_QUIZ_DATA.exerciseTypes}
        />
      )}
      {currentStep === STEPS.SECOND && (
        <RadioButtonsStep
          title="In an average week, you exercise..."
          groupName="week-exercises"
          onSubmit={handleWeekExercisesStepSubmit}
          initialValueId={stepperState.weekExercises}
          items={MOCK_QUIZ_DATA.weekExercises}
          canGoToPrevStep={canGoToPrevStep}
          onPrev={goToPrevStep}
        />
      )}
      {currentStep === STEPS.FINISH && (
        <>
          Finish!
          <Button onClick={goToPrevStep}>Back</Button>
        </>
      )}
    </Box>
  );
}
