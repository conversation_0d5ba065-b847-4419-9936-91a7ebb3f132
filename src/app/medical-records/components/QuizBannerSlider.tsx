// Package modules
import { useState } from 'react';
import { Box, Button, Flex, Heading, Text, useTheme } from '@chakra-ui/react';
import { Swiper as ISwiper, Pagination } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { NavLink } from 'react-router-dom';

// Local modules
import { useSwiperRef } from '../lib/utils';
import { hexOpacity } from '../../../components/theme/utils';
import { SliderNavigationButton } from 'src/components/ui/Slider';

// Assets
import { ReactComponent as BannerCircles } from '@assets/objects/banner-circles.svg';
import { ReactComponent as BannerEllipse } from '@assets/objects/banner-ellipse.svg';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface IProgressBar {
  progress: number;
}
function ProgressBar({ progress }: IProgressBar) {
  return (
    <Box
      position="relative"
      width="112px"
    >
      <Box
        position="absolute"
        top="0"
        left="0"
        width={`${progress}%`}
        height="2px"
        borderRadius="4px"
        bgColor="fluentHealthText.100"
      />
      <Box
        borderRadius="4px"
        width="100%"
        height="2px"
        bgColor="fluentHealthText.100"
        opacity="0.2"
      />
    </Box>
  );
}

interface IQuizBanner {
  title: string;
  subTitle: string;
  progress?: number;
  progressTitle: string;
  ctaButtonText: string;
  bannerTheme: {
    bgColor: string;
  };
}
export function QuizBanner({ title, subTitle, progress, progressTitle, ctaButtonText, bannerTheme }: IQuizBanner) {
  const theme = useTheme();

  return (
    <Box
      position="relative"
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height={{ base: '230px', sm: '300px' }}
      width="100%"
      px="12px"
      borderRadius="24px"
      bgColor={bannerTheme.bgColor}
      overflow="hidden"
    >
      <Box
        position="absolute"
        top={{ base: '-40px', sm: '0' }}
        left={{ base: '-80px', sm: '0' }}
        pointerEvents="none"
        width={{ base: '470px', sm: '370px' }}
        height={{ base: '145px', sm: '245px' }}
        overflow="hidden"
      >
        <BannerCircles
          width="100%"
          height="100%"
        />
      </Box>
      <Text
        fontSize={{ base: 'xs', sm: 'sm' }}
        fontStyle="italic"
        fontFamily="heading"
        marginBottom="8px"
      >
        {subTitle}
      </Text>
      <Heading
        size="lg"
        fontSize={{ base: 'lg', sm: '3xl' }}
        lineHeight={{ base: '8', sm: '11' }}
        maxWidth="500px"
        textAlign="center"
        marginBottom={{ base: '16px', sm: '20px' }}
      >
        {title}
      </Heading>
      {progress && (
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          marginBottom="16px"
        >
          <Text
            fontSize="12px"
            opacity="0.5"
            marginBottom="4px"
          >
            {progressTitle}
          </Text>
          <ProgressBar progress={progress} />
        </Box>
      )}
      <NavLink to="/quizzes">
        <Button
          variant="outline"
          fontSize="14px"
          py="10px"
          lineHeight="1"
          borderColor={hexOpacity(theme.colors.fluentHealthText['100'], 0.2)}
          _hover={{
            borderColor: theme.colors.fluentHealthText['100'],
          }}
        >
          {ctaButtonText}
        </Button>
      </NavLink>
      <Box
        position="absolute"
        pointerEvents="none"
        bottom={{ base: '-40px', sm: '0' }}
        right={{ base: '-80px', sm: '0' }}
        width={{ base: '470px', sm: '370px' }}
        height={{ base: '145px', sm: '245px' }}
        overflow="hidden"
      >
        <BannerEllipse
          width="100%"
          height="100%"
        />
      </Box>
    </Box>
  );
}

export function QuizBannerSlider() {
  const [swiper, setSwiper] = useState<ISwiper>();
  const [paginationEl, paginationElRef] = useSwiperRef();
  const [showNavigationButton, setShowNavigationButton] = useState(false);

  const theme = useTheme();

  return (
    <>
      <Box
        position="relative"
        onMouseEnter={() => setShowNavigationButton(true)}
        onMouseLeave={() => setShowNavigationButton(false)}
      >
        <Swiper
          spaceBetween={20}
          slidesPerView={1}
          onInit={(sw) => {
            setSwiper(sw);
          }}
          modules={[Pagination]}
          pagination={{
            el: paginationEl,
            clickable: true,
            dynamicBullets: true,
          }}
        >
          <SwiperSlide>
            <QuizBanner
              title="You still have some fitness & workout routine questions left to answer if you have time"
              subTitle="Lifestyle and Nutriton"
              progress={40}
              progressTitle="Almost there!"
              ctaButtonText="Continue · 3 min"
              bannerTheme={{ bgColor: 'green.300' }}
            />
          </SwiperSlide>
          <SwiperSlide>
            <QuizBanner
              title="You’re close to completing your Health Profile. How is your Reproductive Health?"
              subTitle="Reproductive health"
              progressTitle="Almost there!"
              ctaButtonText="Start quizz · 5 min"
              bannerTheme={{ bgColor: 'fluentHealthComplementary.Salmon2' }}
            />
          </SwiperSlide>
          <SwiperSlide>
            <QuizBanner
              title="Do you have your Covid Vaccine yet?"
              subTitle="Immunization"
              progressTitle="Almost there!"
              ctaButtonText="Start quizz · 5 min"
              bannerTheme={{ bgColor: 'fluentHealthSecondary.300' }}
            />
          </SwiperSlide>
        </Swiper>
        <SliderNavigationButton
          onClick={() => swiper?.slidePrev()}
          side="left"
          left={{ base: '-30px', sm: '-40px' }}
          paddingLeft="12px"
          justifyContent="flex-start"
          display={{ base: 'none', md: 'flex' }}
          showButton={showNavigationButton}
        />
        <SliderNavigationButton
          onClick={() => swiper?.slideNext()}
          side="right"
          right={{ base: '-30px', sm: '-40px' }}
          paddingRight="12px"
          justifyContent="flex-end"
          display={{ base: 'none', md: 'flex' }}
          showButton={showNavigationButton}
        />
      </Box>
      <Flex
        justify="center"
        mt="4px"
      >
        <Box
          ref={paginationElRef}
          sx={{
            transform: 'translateX(0) !important',
            '--swiper-pagination-bullet-inactive-color': theme.colors.fluentHealthSecondary['200'],
            '--swiper-pagination-color': theme.colors.fluentHealthSecondary['100'],
          }}
        />
      </Flex>
    </>
  );
}
