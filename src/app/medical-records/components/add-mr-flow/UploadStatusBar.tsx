import { Box, IconButton, Progress, Slide, Text } from '@chakra-ui/react';
import { CheckIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { useNavigate } from 'react-router-dom';
import { recordUploadRecordEvents } from '@user/lib/events-analytics-manager';

import { useAnalyticsService } from '@lib/state';

type UploadStatus = 'uploading' | 'failed' | 'success';

interface UploadStatusProps {
  status: UploadStatus;
  uploaded: number;
  total: number;
}

function UploadStatusBar({ status, uploaded, total }: UploadStatusProps) {
  const navigate = useNavigate();
  const { trackEventInFlow } = useAnalyticsService();

  const { DOCUMENTS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const getBackgroundColor = () => {
    switch (status) {
      case 'uploading':
        return 'white';
      case 'failed':
        return 'red.200';
      case 'success':
        return 'green.400';
      default:
        return 'gray.200';
    }
  };

  const getTextColor = () => {
    switch (status) {
      case 'uploading':
        return 'royalBlue.500';
      case 'failed':
        return 'red.600';
      case 'success':
        return 'white';
      default:
        return 'white';
    }
  };

  const getMessage = () => {
    switch (status) {
      case 'uploading':
        return `${total} files uploading...`;
      case 'failed':
        return `${uploaded}/${total} files uploaded. ${total - uploaded} files failed.`;
      case 'success':
        return `${uploaded}/${total} files uploaded`;
      default:
        return `${total} files uploading...`;
    }
  };

  const progressValue = (uploaded / total) * 100;

  return (
    <Slide
      direction="bottom"
      in
      style={{ zIndex: 20 }}
      onClick={() => {
        recordUploadRecordEvents(trackEventInFlow, { EventName: 'UploadRecordStarted' });
        navigate(`/${DOCUMENTS}/${ADD}`);
      }}
    >
      <Box
        position="fixed"
        bottom="0"
        left="0"
        zIndex={20}
        sx={{
          width: ['100vw', '100vw', '420px'],
          left: ['0', '0', 'auto'],
          right: [0, 0, '90px'],
        }}
      >
        {/* Progress Bar */}
        {status === 'uploading' && (
          <Progress
            sx={{
              '& > div': {
                backgroundColor: '#FF825C',
              },
            }}
            position="absolute"
            bottom="0"
            left="0"
            width="100%"
            height="4px"
            size="sm"
            value={progressValue}
            borderRadius="0px 10px 10px 0px"
            zIndex={23}
          />
        )}

        <Box
          position="relative"
          bg={getBackgroundColor()}
          width="100%"
          borderRadius="10px 10px 0px 0px"
          p={4}
          boxShadow="lg"
          margin="0 auto"
          zIndex={22}
          textAlign="start"
          display="flex"
          alignItems="center"
          gap="8px"
        >
          {/* Left Check Icon */}
          {status === 'success' && (
            <CheckIcon
              color="white"
              boxSize={5}
            />
          )}

          <Text
            fontSize="18px"
            fontWeight="400"
            color={getTextColor()}
          >
            {getMessage()}
          </Text>

          {/* Optional Collapse Button */}
          <IconButton
            aria-label="Collapse upload status"
            icon={<ChevronUpIcon />}
            size="sm"
            position="absolute"
            right="10px"
            top="50%"
            transform="translateY(-50%)"
            variant="ghost"
            color={getTextColor()}
          />
        </Box>
      </Box>
    </Slide>
  );
}

export default UploadStatusBar;
