import { PropsWithChildren } from 'react';
import { Button, ButtonProps, ChakraProps, Flex, Heading, Text } from '@chakra-ui/react';
import { ChevronLeft as ArrowLeftIcon, ChevronRight as ArrowRightIcon } from 'react-feather';

export function StepHeading(props: PropsWithChildren<ChakraProps>) {
  return (
    <Heading
      fontSize="2rem"
      mb="16px"
      textAlign="center"
      {...props}
    />
  );
}

export function StepDescription(props: PropsWithChildren<ChakraProps>) {
  return (
    <Text
      fontSize="lg"
      color="fluentHealthText.300"
      maxW="456px"
      textAlign="center"
      mx="auto"
      mb="100px"
      {...props}
    />
  );
}

export function StepOptionalLabel(props: PropsWithChildren<ChakraProps>) {
  return (
    <Text
      color="gray.400"
      textAlign="center"
      fontStyle="italic"
      fontFamily="heading"
      mx="auto"
      mb="16px"
      {...props}
    />
  );
}

export function StepFormContainer(props: PropsWithChildren<ChakraProps>) {
  return (
    <Flex
      justifyContent="space-between"
      mx="auto"
      maxW="380px"
      width="100%"
      {...props}
    />
  );
}

export function StepPreviousButton(props: PropsWithChildren<ChakraProps | ButtonProps>) {
  return (
    <Button
      variant="quiet"
      color="fluentHealth.500"
      size="xl"
      leftIcon={<ArrowLeftIcon size={16} />}
      {...props}
    />
  );
}

export function StepPrimaryButton({
  hideIcon = false,
  ...props
}: PropsWithChildren<ChakraProps | ButtonProps> & {
  hideIcon?: boolean;
}) {
  return (
    <Button
      size="lg"
      {...(hideIcon ? {} : { rightIcon: <ArrowRightIcon size={16} /> })}
      {...props}
    />
  );
}

export function StepSkipButton(props: PropsWithChildren<ChakraProps | ButtonProps>) {
  return (
    <Button
      variant="quiet"
      color="fluentHealthText.300"
      {...props}
    />
  );
}
