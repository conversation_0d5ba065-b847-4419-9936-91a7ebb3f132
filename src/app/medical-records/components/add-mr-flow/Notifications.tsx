import { Box, Flex, Link, Text, useBreakpointValue } from '@chakra-ui/react';
import { NavLink } from 'react-router-dom';

import { ReactComponent as SecurityIcon } from '@assets/icons/security.svg';
import { ReactComponent as SecurityWarningIcon } from '@assets/icons/security-warning.svg';
import { ReactComponent as OCRIcon } from '@assets/icons/ocr.svg';

interface WarningLink {
  to: string;
  text: string;
}

interface WarningItemProps {
  id?: string;
  Icon: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  title: string;
  text: string;
  link: WarningLink;
}

// Warning data array
const warnings: WarningItemProps[] = [
  {
    id: 'privacy-policy',
    Icon: SecurityIcon,
    title: 'Your information is always kept private and secure.',
    text: 'Our platform meets global security and privacy standards, and we employ advanced security protocols to protect your data.',
    link: { to: '/settings/legal/privacy-policy/view', text: 'Learn more in our Privacy Policy' },
  },
  {
    id: 'terms-of-use',
    Icon: SecurityWarningIcon,
    title: 'No unlawful/offensive content can be stored.',
    text: 'Please refer to the section titled “Restrictions of Use” in the Terms of Use to understand the types of information and records that cannot be stored.',
    link: { to: '/terms-n-conditions/view', text: 'Learn more in our Terms of Use' },
  },
];

function WarningItem({ id, Icon, text, link, title }: WarningItemProps) {
  // Responsive values for font and icon sizes
  const isMobile = useBreakpointValue({ base: true, md: false });
  const fontSize = useBreakpointValue({ base: '14px', md: '16px' });
  const linkFontSize = useBreakpointValue({ base: '16px', md: '16px' });
  const iconSize = useBreakpointValue({ base: '44px', md: '48px' });
  return (
    <Flex
      id={id}
      align="flex-start"
      gap="12px"
    >
      <Box flexShrink={0}>
        <Icon
          width={iconSize}
          height={iconSize}
        />
      </Box>
      <Flex
        direction="column"
        gap="6px"
      >
        {!isMobile ? (
          <Text
            color="charcoal.70"
            fontSize={fontSize}
          >
            {`${title} ${text}`}
          </Text>
        ) : (
          <Flex direction="column">
            <Text
              color="charcoal.100"
              fontSize="16px"
            >
              {title}
            </Text>
            <Text
              color="charcoal.70"
              fontSize="16px"
            >
              {text}
            </Text>
          </Flex>
        )}
        <Flex
          align="center"
          gap="4px"
          color="fluentHealth.500"
        >
          <Link
            as={NavLink}
            to={link.to}
            fontWeight="500"
            fontSize={linkFontSize}
            lineHeight="24px"
            cursor="pointer"
          >
            {link.text}
          </Link>
          {/* <ArrowRightIcon size={18} /> */}
        </Flex>
      </Flex>
    </Flex>
  );
}

export function PrivacyWarning() {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Flex
      {...(isMobile
        ? { margin: '0px' }
        : {
            margin: '22px',
            position: 'absolute',
            bottom: '0px',
            left: '46px',
            right: '46px',
            width: 'calc(100% - 148px)',
            overflow: 'hidden',
          })}
      justifyContent="center"
      bgColor="rgba(218, 220, 255, 0.80)"
      boxShadow="0px 5px 16px 0px rgba(20, 24, 26, 0.06)"
      borderRadius="12px"
      flexDirection="column"
      gap="20px"
      padding="20px"
    >
      {warnings.map(({ id, Icon, text, link, title }) => (
        <WarningItem
          key={id}
          id={id}
          Icon={Icon}
          text={text}
          link={link}
          title={title}
        />
      ))}
    </Flex>
  );
}

export function DataProcessingInfo() {
  return (
    <Flex
      position={{ md: 'absolute' }}
      right={{ md: '32px' }}
      bottom={{ md: '32px' }}
      mx="auto"
      my={{ base: '16px', md: 0 }}
      gap="20px"
      padding="20px"
      bgColor="periwinkle.50"
      borderTop="1px solid"
      borderColor="periwinkle.200"
      borderRadius="20px"
      maxW="333px"
    >
      <Box>
        <OCRIcon />
      </Box>
      <Flex
        direction="column"
        gap="6px"
      >
        <Text
          color="periwinkle.700"
          alignSelf="stretch"
        >
          Lab results are processed through our image-to-text functionality so you can view your data in historical
          trends and easily search through your records.
        </Text>
      </Flex>
    </Flex>
  );
}
