import { PropsWithChildren } from 'react';
import { ChakraProps, Flex } from '@chakra-ui/react';

export function StepCardFooter({ children, ...props }: PropsWithChildren<ChakraProps>) {
  return (
    <Flex
      mx={{ base: '-16px', sm: '-40px' }}
      mb="-24px"
      mt="auto"
      borderBottomEndRadius="20px"
      borderBottomStartRadius="20px"
      {...props}
    >
      {children}
    </Flex>
  );
}

export function StepCard({ children, ...props }: PropsWithChildren<ChakraProps>) {
  return (
    <Flex
      direction="column"
      width={{ base: 'calc(100% - 16px)', md: '560px' }}
      height="max-content"
      px={{ base: '0px', md: '40px' }}
      pt="36px"
      pb="24px"
      mx="auto"
      borderRadius="20px"
      {...props}
    >
      {children}
    </Flex>
  );
}

export function StepCardWithFiles(props: PropsWithChildren<ChakraProps>) {
  return (
    <Flex
      width={{ base: '100%', md: 'max-content' }}
      gap="12px"
      direction={{ base: 'column-reverse', lg: 'row' }}
      mx="auto"
      mt={{ base: '8px', md: '120px' }}
      {...props}
    />
  );
}
