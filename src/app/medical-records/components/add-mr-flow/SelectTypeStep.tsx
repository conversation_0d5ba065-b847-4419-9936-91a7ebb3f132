import { useRef, useState } from 'react';
import { Flex, <PERSON>ing, Menu, MenuButton, MenuItem, MenuList, useDisclosure } from '@chakra-ui/react';
import { ChevronDown as ArrowDownIcon } from 'react-feather';
import { useForm } from 'react-hook-form';
import { recordFamilyMemberHistoryEvents, recordUploadRecordEvents } from '@user/lib/events-analytics-manager';
import { useFamilyMemberHistoryList } from '@user/lib/medplum-state';

import { Modal } from 'src/components/Modal';
import { StepCard } from './StepCard';
import { AMRFSelectTypePayload } from './models';
import { StepHeading, StepPrimaryButton } from './StepComponents';
import { FamilyMemberForm } from 'src/app/user/profile/family-history/components/FamilyMemberForm';
import { useAnalyticsService, useAuthService } from '@lib/state';

export function SelectTypeStep({
  initialState,
  newHealthRecordSubmitHandler,
  familyMemberId,
}: {
  initialState?: AMRFSelectTypePayload;
  newHealthRecordSubmitHandler: Function;
  familyMemberId: string;
}) {
  const familyMemberModal = useDisclosure();
  const menuButtonRef = useRef<HTMLButtonElement | null>(null);

  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const { familyMemberList } = useFamilyMemberHistoryList(authenticatedUser?.id);

  const [familyMemberData, setFamilyMemberData] = useState<any>(
    familyMemberList?.FamilyMemberHistoryList?.find((member: any) => member.id === familyMemberId)
  );

  const relationships = familyMemberList?.FamilyMemberHistoryList?.map(
    (member: any) => member.relationship?.coding?.[0]?.display
  );

  const onceAddedRelationipLabel = [
    'Natural Mother',
    'Natural Father',
    'Adoptive Mother',
    'Adoptive Father',
    'Step Mother',
    'Step Father',
  ];
  const matchingRelationships = relationships?.filter((relationship: string) =>
    onceAddedRelationipLabel.includes(relationship)
  );

  const form = useForm({
    mode: 'all',
    defaultValues: {
      someoneElseName: initialState?.someoneElseName?.trim() || '',
    },
  });

  const assigneeChangeHandler = (personVal: any) => {
    setFamilyMemberData(personVal);
    form.setValue('someoneElseName', `${personVal?.name}`);
    recordUploadRecordEvents(trackEventInFlow, {
      EventName: 'UploadRecordInProgUserSelected',
      ur_user: personVal?.name ? 'others' : 'myself',
    });
  };

  const onAddButtonClick = () => {
    familyMemberModal.onOpen();
    recordFamilyMemberHistoryEvents(trackEventInFlow, {
      EventName: 'FamilyMemberHistoryAddStarted',
      fm_entry_point: 'record',
    });
  };

  return (
    <Flex
      width="100%"
      minHeight={{ base: undefined, md: '100vh' }}
      justifyContent={{ base: 'space-between', md: 'center' }}
      alignItems="center"
      flexDirection="column"
      paddingBottom={{ base: '0px', md: '200px' }}
      marginTop={{ base: '72px', md: '0px' }}
    >
      <StepCard
        width={{ base: 'calc(100% - 36px)', md: '720px' }}
        minWidth="360px"
        padding={{ base: '24px', md: '36px 40px 24px 40px' }}
        bg="transparent"
      >
        <Flex
          direction="column"
          justify="center"
          align="center"
        >
          <StepHeading
            mb="8px"
            textAlign="center"
            fontSize={{ base: '28px', md: '32px' }}
            maxW={{ base: '270px', sm: '100%' }}
          >
            Add a new health record for
          </StepHeading>

          <Flex
            style={{
              borderRadius: '36px',
              border: '1px solid var(--gray-40-ced-1-d-2, #CED1D2)',
              background: 'var(--periwinkle-40-f-0-f-1-ff, #F0F1FF)',
              display: 'flex',
              height: '70px',
              padding: '8px 28px',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <Menu placement="bottom-start">
              <MenuButton
                ref={menuButtonRef}
                borderBottom="1px solid"
                borderColor="iris.500"
              >
                <Heading
                  display="flex"
                  alignItems="center"
                  gap="8px"
                  color="royalBlue.500"
                  fontSize="32px"
                >
                  {!familyMemberData ? 'Myself' : familyMemberData?.name}
                  <ArrowDownIcon size={20} />
                </Heading>
              </MenuButton>
              <MenuList
                boxShadow="0px 1px 4px rgba(7, 16, 84, 0.1), 0px 10px 28px -2px rgba(7, 16, 84, 0.14)"
                borderRadius="8px"
                padding="8px 0px"
                width="471px"
                maxHeight="268px"
                overflowY="scroll"
              >
                <MenuItem
                  onClick={() => assigneeChangeHandler('')}
                  p="10px 12px"
                  _hover={{ bgColor: '#F5F5FF' }}
                  _focus={{ bgColor: 'F5F5FF' }}
                >
                  <Heading
                    as="p"
                    fontSize="18px"
                    lineHeight="26px"
                  >
                    Myself
                  </Heading>
                </MenuItem>
                {familyMemberList?.FamilyMemberHistoryList?.map((familyMember: any) => (
                  <MenuItem
                    key={familyMember.id}
                    onClick={() => assigneeChangeHandler(familyMember)}
                    flexDirection="column"
                    alignItems="flex-start"
                    p="10px 12px"
                    _hover={{ bgColor: '#F5F5FF' }}
                    _focus={{ bgColor: '#F5F5FF' }}
                  >
                    <Heading
                      as="p"
                      fontSize="18px"
                      lineHeight="26px"
                    >
                      {familyMember?.name}
                    </Heading>
                  </MenuItem>
                ))}
                <MenuItem
                  onClick={() => {
                    menuButtonRef.current?.blur(); // Close the menu
                    onAddButtonClick(); // Open modal
                  }}
                  p="10px 12px"
                  _hover={{ bgColor: '#F5F5FF' }}
                  _focus={{ bgColor: '#F5F5FF' }}
                  color="royalBlue.500"
                >
                  <Heading
                    as="p"
                    fontSize="18px"
                    lineHeight="26px"
                  >
                    Add family member
                  </Heading>
                </MenuItem>
              </MenuList>
            </Menu>
          </Flex>

          <StepPrimaryButton
            onClick={() => newHealthRecordSubmitHandler(familyMemberData)}
            mt="72px"
          >
            Continue
          </StepPrimaryButton>
        </Flex>
      </StepCard>

      <Modal
        title="Family Member"
        showModalFooter={false}
        isCentered
        {...familyMemberModal}
        scrollY="false"
      >
        <FamilyMemberForm
          relationships={matchingRelationships}
          closeDialog={(familyMember: any) => {
            if (familyMember?.id) {
              newHealthRecordSubmitHandler(familyMember);
              setFamilyMemberData(familyMember);
            }
            familyMemberModal.onClose();
          }}
        />
      </Modal>
    </Flex>
  );
}
