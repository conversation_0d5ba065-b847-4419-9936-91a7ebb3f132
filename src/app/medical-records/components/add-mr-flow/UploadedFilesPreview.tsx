import { Box, Flex, Text } from '@chakra-ui/react';

import { DragAndDropFilePreview } from '../../../../components/ui/DragAndDrop';
import { MEDICAL_RECORD_ICON_MAP, MEDICAL_RECORD_NAME_MAP } from '../../lib/constants';
import { MEDICAL_RECORD_TYPES } from '@lib/models/medical-record';
import { FilesPreview } from '../FilesPreview';
import { pluralize } from '@lib/utils/utils';
import { useIsTablet } from 'src/components/ui/hooks/device.hook';
// import { QuestionnaireResponseItemAnswer } from 'src/gql/graphql';

export function UploadedFilesPreview({
  type,
  files,
}: {
  type: MEDICAL_RECORD_TYPES;
  // Todo can only be removed only when every UploadedFilesPreview can be fixed
  files: any;
  // files: QuestionnaireResponseItemAnswer[];
}) {
  const isTablet = useIsTablet();
  return (
    <Box
      width={{ base: 'calc(100% - 80px)', lg: '304px' }}
      minWidth={{ base: '360px', lg: '304px' }}
      height={{ lg: '382px' }}
      mb={{ base: '40px', md: '0px' }}
      p="22px"
      bgColor="#F0F1FF"
      borderRadius="20px"
      mx="auto"
    >
      <Flex
        gap="14px"
        align="center"
        mb={{ base: '20px', lg: '28px' }}
      >
        <Box
          maxW="28px"
          height="34px"
        >
          {MEDICAL_RECORD_ICON_MAP[type]}
        </Box>
        <Text
          color="fluentHealthText.200"
          fontWeight="500"
        >
          {MEDICAL_RECORD_NAME_MAP[type]}
        </Text>
      </Flex>
      {isTablet ? (
        <Flex
          direction="column"
          gap="12px"
        >
          {files.map((file: any) => (
            <DragAndDropFilePreview
              key={file.name}
              file={file}
              backgroundColor="transparent"
            />
          ))}
        </Flex>
      ) : (
        <Flex
          direction="column"
          align="center"
          gap="18px"
        >
          <FilesPreview files={files} />
          <Text
            fontSize="sm"
            color="fluentHealthText.300"
          >
            {files.length}
            &nbsp;
            {pluralize('file', files.length)}
          </Text>
        </Flex>
      )}
    </Box>
  );
}
