import { FormControl, FormErrorMessage } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';

import { AutoExpandedTextarea } from 'src/components/ui/Form/AutoExpandedTextarea';
import { StepCard, StepCardWithFiles } from './StepCard';
import { ADD_MEDICAL_RECORD_STEPS, AMRFNotesPayload, AMRStepperState } from './models';
import { UploadedFilesPreview } from './UploadedFilesPreview';
import {
  StepDescription,
  StepFormContainer,
  StepHeading,
  StepOptionalLabel,
  StepPreviousButton,
  StepPrimaryButton,
} from './StepComponents';

const MAX_ALLOWED_CHARACTERS = 5000;

export function NotesStep({
  stepperState,
  initialState,
  onSubmit,
  onPrevious,
}: {
  stepperState: AMRStepperState;
  initialState?: AMRFNotesPayload;
  onSubmit: (payload: AMRFNotesPayload) => void;
  onPrevious: Function;
}) {
  const form = useForm<AMRFNotesPayload>({
    mode: 'all',
    defaultValues: {
      notes: initialState?.notes || '',
    },
  });

  const handleFromSubmit = (values: AMRFNotesPayload) => {
    onSubmit(values);
  };

  return (
    <StepCardWithFiles>
      <UploadedFilesPreview
        type={stepperState[ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]?.recordType!}
        files={stepperState[ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]!}
      />
      <StepCard pt={{ base: '36px', md: '0' }}>
        <StepOptionalLabel mb="12px">optional</StepOptionalLabel>
        <StepHeading>
          Are there any notes you would <br />
          like to add to this record?
        </StepHeading>
        <StepDescription mb="40px">Add any relevant details.</StepDescription>
        <StepFormContainer mb="40px">
          <FormControl isInvalid={!!form.formState.errors.notes}>
            <AutoExpandedTextarea
              variant="roundedTransparent"
              placeholder="Add something you need to remember about this record"
              borderColor="iris.500"
              minH="136px"
              controllerProps={{
                name: 'notes',
                control: form.control,
                rules: { maxLength: MAX_ALLOWED_CHARACTERS },
              }}
            />
            <FormErrorMessage>The maximum number of characters allowed is {MAX_ALLOWED_CHARACTERS}</FormErrorMessage>
          </FormControl>
        </StepFormContainer>
        <StepFormContainer>
          <StepPreviousButton onClick={() => onPrevious()}>Previous</StepPreviousButton>
          <StepPrimaryButton
            onClick={form.handleSubmit(handleFromSubmit)}
            isDisabled={!form.formState.isValid}
          >
            Continue
          </StepPrimaryButton>
        </StepFormContainer>
      </StepCard>
    </StepCardWithFiles>
  );
}
