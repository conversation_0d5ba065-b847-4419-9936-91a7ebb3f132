import { FormControl, FormErrorMessage, Input } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';

import { StepCard, StepCardWithFiles } from './StepCard';
import { ADD_MEDICAL_RECORD_STEPS, AMRFDoctorPayload, AMRStepperState } from './models';
import { UploadedFilesPreview } from './UploadedFilesPreview';
import {
  StepDescription,
  StepFormContainer,
  StepHeading,
  StepPreviousButton,
  StepPrimaryButton,
} from './StepComponents';
import { validateForTrimmingStr } from '../../lib/utils';

export function DoctorStep({
  stepperState,
  initialState,
  onSubmit,
  onPrevious,
}: {
  stepperState: AMRStepperState;
  initialState?: AMRFDoctorPayload;
  onSubmit: Function;
  onPrevious: Function;
}) {
  const form = useForm<AMRFDoctorPayload>({
    mode: 'all',
    defaultValues: {
      doctor: initialState?.doctor || '',
    },
  });

  const handleFromSubmit = (values: AMRFDoctorPayload) => {
    onSubmit(values);
  };

  return (
    <StepCardWithFiles>
      <UploadedFilesPreview
        type={stepperState[ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]?.recordType!}
        files={stepperState[ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]!}
      />
      <StepCard>
        <StepHeading>Who was your doctor?</StepHeading>
        <StepDescription mb="40px">This is the name of the doctor you consulted for this record.</StepDescription>
        <StepFormContainer mb="40px">
          <FormControl isInvalid={!!form.formState.errors.doctor}>
            <Input
              variant="roundedTransparent"
              size="xl"
              placeholder="Doctor's name"
              {...form.register('doctor', {
                required: true,
                validate: validateForTrimmingStr,
              })}
            />
            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>
        </StepFormContainer>
        <StepFormContainer>
          <StepPreviousButton onClick={() => onPrevious()}>Previous</StepPreviousButton>
          <StepPrimaryButton
            onClick={form.handleSubmit(handleFromSubmit)}
            isDisabled={!form.formState.isValid}
          >
            Continue
          </StepPrimaryButton>
        </StepFormContainer>
      </StepCard>
    </StepCardWithFiles>
  );
}
