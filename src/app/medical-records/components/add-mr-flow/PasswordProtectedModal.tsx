import {
  Button,
  Modal as ChakraModal,
  Flex,
  FormControl,
  FormErrorMessage,
  Heading,
  Input,
  InputGroup,
  InputLeftElement,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Text,
  UseDisclosureProps,
} from '@chakra-ui/react';
import { useState } from 'react';
import { MODAL_VARIANTS } from '@components/Modal';
import { GlobalWorkerOptions, getDocument } from 'pdfjs-dist';
import pdfWorker from 'pdfjs-dist/build/pdf.worker.min?url';
import { recordHealthRecordHubEvents } from '@user/lib/events-analytics-manager';

import { updateTaskPayload } from '../../lib/utils';
import { medplumApi } from '../../lib/medplum-api';
import { MedicalRecord } from '../../lib/state';
import { documentUploadWorkflow } from '../../lib/constants';
import { useAnalyticsService, useAuthService } from '@lib/state';

import { ReactComponent as Unlock } from '@assets/icons/unlock.svg';

// Set the worker source for PDF.js
GlobalWorkerOptions.workerSrc = pdfWorker;
export interface IPasswordProtectedModal extends UseDisclosureProps {
  selectedRecord: MedicalRecord;
  fetchMedicalRecordList: any;
}

export function PasswordProtectedModal({
  isOpen,
  onClose,
  selectedRecord,
  fetchMedicalRecordList,
}: IPasswordProtectedModal) {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const patientId = authenticatedUser?.id;
  const handleSubmit = async (): Promise<void> => {
    const pdfUrl: string | null =
      selectedRecord?.doc?.content?.find((item) => item?.attachment?.contentType === 'application/pdf')?.attachment
        ?.url ?? null;

    if (!pdfUrl) {
      return; // Return explicitly to satisfy ESLint
    }

    try {
      const response = await fetch(pdfUrl);
      if (!response.ok) throw new Error('Failed to fetch PDF');

      const data = await response.blob();
      const file = new File([data], 'mockFile.pdf', { type: data.type });

      const arrayBuffer = await file.arrayBuffer();
      const loadingTask = getDocument({ data: arrayBuffer, password });

      await loadingTask.promise;

      setError('');

      const entries = [
        {
          resource: updateTaskPayload(
            'in-progress',
            documentUploadWorkflow.passwordSubmission.inProgress,
            selectedRecord?.task,
            password,
            patientId
          ),
          request: { method: 'PUT', url: `Task/${selectedRecord?.task?.id}` },
        },
      ];

      const payload = {
        resourceType: 'Bundle',
        type: 'transaction',
        entry: entries,
      };

      await medplumApi.medicalRecord.addOne(payload);
      recordHealthRecordHubEvents(trackEventInFlow, {
        EventName: 'EncryptedRecordUnlocked',
        document_id: selectedRecord?.id,
      });
      fetchMedicalRecordList();
      setPassword('');
      onClose?.();
    } catch (err: any) {
      console.error({ error: err });
      recordHealthRecordHubEvents(trackEventInFlow, {
        EventName: 'EncryptedRecordPasswordError',
        document_id: selectedRecord?.id,
      });
      setError('Incorrect password. Please try again.');
    }
  };

  return (
    <ChakraModal
      variant={MODAL_VARIANTS.PERIWINKLE}
      isOpen={isOpen!}
      onClose={() => {
        onClose?.();
        setPassword('');
        setError('');
      }}
      isCentered
    >
      <ModalOverlay />
      <ModalContent
        minW={{ base: '100%', md: '480px' }}
        padding="64px 80px 40px 80px"
        borderRadius="16px"
        textAlign="center"
      >
        <ModalCloseButton
          position="absolute"
          top="20px"
          right="20px"
        />
        <ModalHeader>
          <Heading
            mt="34px"
            fontSize="28px"
            fontWeight="400"
            color="charcoal.100"
          >
            File Is Password Protected
          </Heading>
        </ModalHeader>
        <ModalBody
          display="flex"
          flexDirection="column"
          alignItems="center"
        >
          <Text
            fontSize="18px"
            color="gray.400"
            mb="20px"
            maxWidth="360px"
          >
            Please enter your password to unlock the file and send it for review.
          </Text>
          <FormControl
            isInvalid={!!error}
            mb="20px"
          >
            <InputGroup
              maxW={['325px', '280px']}
              m="auto"
            >
              <InputLeftElement
                pointerEvents="none"
                mt="8px"
                ml="5px"
              >
                <Unlock color="gray.400" />
              </InputLeftElement>
              <Input
                type="password"
                placeholder="Enter password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setError('');
                }}
                mb="1px"
                bg="white"
                borderRadius="40px"
                border="none"
                height="14"
                fontSize="16px"
                color="fluentHealthText.100"
                _focus={{ border: 'none', boxShadow: 'none' }}
                _invalid={{ border: 'none', boxShadow: 'none' }}
              />
            </InputGroup>
            {error && (
              <Flex justifyContent="center">
                <FormErrorMessage
                  color="red.100"
                  textAlign="center"
                >
                  {error}
                </FormErrorMessage>
              </Flex>
            )}
          </FormControl>
          <Button
            mb="30px"
            color="fluentHealthText.300"
            textColor="#FFF"
            onClick={handleSubmit}
            isDisabled={!password.length}
          >
            Submit
          </Button>
        </ModalBody>
      </ModalContent>
    </ChakraModal>
  );
}
