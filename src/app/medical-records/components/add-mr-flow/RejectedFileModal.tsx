import {
  <PERSON><PERSON>,
  Modal as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>nt,
  ModalHeader,
  ModalOverlay,
  Text,
  UseDisclosureProps,
} from '@chakra-ui/react';
import { MODAL_VARIANTS } from '@components/Modal';

import { MedicalRecord } from '../../lib/state';
import { getDocumentRejectionMessage } from '../../lib/constants';

export interface IRejectedFileModal extends UseDisclosureProps {
  onReupload: (selectedRecord: MedicalRecord) => void;
  selectedRecord: MedicalRecord;
}

export function RejectedFileModal({ isOpen, onClose, onReupload, selectedRecord }: IRejectedFileModal) {
  const rejectionCode = selectedRecord?.task?.output?.[0]?.type?.coding?.[0]?.code;
  const rejectionMessage = getDocumentRejectionMessage(rejectionCode || '');

  return (
    <ChakraModal
      variant={MODAL_VARIANTS.PERIWINKLE}
      isOpen={isOpen!}
      onClose={onClose!}
      isCentered
    >
      <ModalOverlay />
      <ModalContent
        minW={{ base: '100%', md: '480px' }}
        padding="64px 80px 40px 80px"
        borderRadius="16px"
        textAlign="center"
      >
        <ModalCloseButton
          position="absolute"
          top="20px"
          right="20px"
        />
        <ModalHeader>
          <Heading
            mt="34px"
            fontSize="28px"
            fontWeight="500"
            color="charcoal.100"
          >
            {rejectionMessage.title}
          </Heading>
        </ModalHeader>
        <ModalBody
          display="flex"
          flexDirection="column"
          alignItems="center"
        >
          <Text
            fontSize="18px"
            color="gray.400"
            mb="20px"
            maxWidth="320px"
          >
            {rejectionMessage.body}
          </Text>
          <Button
            mb="20px"
            color="fluentHealthText.300"
            textColor="#FFF"
            onClick={() => onReupload(selectedRecord)}
          >
            Re-upload record
          </Button>
        </ModalBody>
      </ModalContent>
    </ChakraModal>
  );
}
