import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { MEDICAL_RECORD_TYPES } from '@lib/models/medical-record';

export enum ADD_MEDICAL_RECORD_STEPS {
  SELECT_TYPE = 1,
  UPLOAD_FILES = 2,
  PROCESSING = 8,
  SUCCESS = 7,
}
const { DOCUMENTS, UPLOAD, USER_METADATA } = ROUTE_VARIABLES;
const { ADD, EDIT } = ROUTE_ACTIONS;
export const ROUTE_DOCS = {
  DOC_ADD: `/${DOCUMENTS}/${ADD}`,
  DOC_UPLOAD_ADD: `/${DOCUMENTS}/${UPLOAD}/${ADD}`,
  DOC_USER_EDIT: `/${DOCUMENTS}/${USER_METADATA}/${EDIT}`,
};

export type AMRFSelectTypePayload = {
  someoneElseName?: string;
  newConsentRequired?: string;
  recordType: MEDICAL_RECORD_TYPES;
};

export type AMRFUploadFilesPayload = File[];

export type AMRFSelectTitlePayload = {
  title: string;
};

export type AMRFSelectDatePayload = {
  date: string; // YYYY-MM-DD
};

export type AMRFDoctorPayload = {
  doctor: string;
};

export type AMRFNotesPayload = {
  notes: string;
};

export type AMRFTagsPayload = {
  tags: string[];
};

export type AMRStepperState = {
  [ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]?: AMRFSelectTypePayload;
  [ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]?: AMRFUploadFilesPayload;
};
