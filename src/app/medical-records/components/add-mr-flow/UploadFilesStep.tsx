import { Box, Flex, Stack, Text, useBreakpointValue } from '@chakra-ui/react';
import { useCallback, useMemo } from 'react';
import { CheckIcon } from '@chakra-ui/icons';

import { DragAndDropAlertPrompt, DragAndDropFilePreview } from 'src/components/ui/DragAndDrop';
import {
  DRAG_AND_DROP_AREA_VARIANT,
  DragAndDropArea,
  DragPrompt,
  useDragAndDropArea,
} from 'src/components/ui/DragAndDrop/DragAndDropAreaWithoutError';
import { AMRFUploadFilesPayload } from './models';
import { StepHeading, StepPreviousButton, StepPrimaryButton } from './StepComponents';
import { useFileUploadStore } from '../../lib/uploadState';

const ACCEPTED_FILE_TYPES = {
  'image/png': ['.png'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'application/pdf': ['.pdf'],
};

const MAX_FILES = Infinity;

export function UploadFilesStep({
  initialState,
  onSubmit,
  onChange,
  onPrevious,
}: {
  initialState?: AMRFUploadFilesPayload;
  onSubmit: (files: AMRFUploadFilesPayload) => void;
  onChange: (files: AMRFUploadFilesPayload) => void;
  onPrevious: Function;
}) {
  const { files: storeFiles, cancelUpload, retryUpload } = useFileUploadStore.getState();

  // Memoize expensive calculations to avoid recalculating on every render
  const { total, uploaded, failed } = useMemo(() => {
    const fileEntries = Object.values(storeFiles);
    const totalCount = fileEntries.length;
    const uploadedCount = fileEntries.filter(({ progress }) => progress === 100).length;
    const failedCount = fileEntries.filter(({ error }) => error).length;

    return { total: totalCount, uploaded: uploadedCount, failed: failedCount };
  }, [storeFiles]);

  const {
    files: uploadedFiles,
    uploadFilesHandler,
    setUploadedFiles,
  } = useDragAndDropArea({
    initialState,
    maxFiles: MAX_FILES,
    onChange,
  });

  // Memoize progress completion check
  const isProgressComplete = useCallback((progressDetails: any) => {
    return (
      Object.keys(progressDetails).length > 0 &&
      Object.values(progressDetails).every((value: any) => value.progress === 100 || value.error)
    );
  }, []);

  // Memoize submit handler
  const submitFilesHandler = useCallback(() => {
    onSubmit(uploadedFiles);
  }, [onSubmit, uploadedFiles]);

  // Memoize files array to avoid recreating on every render
  const files = useMemo(() => Object.values(storeFiles), [storeFiles]);

  // Memoize button disabled state to avoid recalculating
  const isSubmitDisabled = useMemo(() => {
    return !isProgressComplete(storeFiles) || !uploaded;
  }, [isProgressComplete, storeFiles, uploaded]);

  // Responsive values for layout
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Flex
      flexDirection="column"
      alignItems="center"
      maxWidth={{ base: '100%', md: '535px', lg: files.length === 0 ? '535px' : '1130px' }}
      width="100%"
      mx="auto"
      px={{ base: '16px', md: '0' }}
    >
      <StepHeading
        mb={{ base: '32px', md: '66px' }}
        mt={{ base: '60px', md: '120px' }}
        textAlign="center"
        fontSize={{ base: '20px', md: '24px' }}
      >
        Upload health records
      </StepHeading>
      <Flex
        flexDirection={{ base: 'column', md: 'row' }}
        width="100%"
        alignItems="center"
        justifyContent="center"
      >
        <DragAndDropArea
          mb="24px"
          acceptedFileTypes={ACCEPTED_FILE_TYPES}
          onFilesDrop={uploadFilesHandler}
          disabled={files.length >= MAX_FILES}
          bg="periwinkle.50"
          height={{ base: '250px', md: '300px' }}
          width={{ base: '100%', md: '520px' }}
          borderRadius="8px"
          maxFiles={MAX_FILES}
        >
          {(props) => (
            <DragPrompt
              variant={DRAG_AND_DROP_AREA_VARIANT.VERTICAL}
              height="100%"
              width="100%"
              alertPrompt={
                <DragAndDropAlertPrompt
                  mt="8px"
                  width="126px"
                  textAlign="center"
                >
                  PDF, JPEG or PNG files that do not exceed 25 MB.
                </DragAndDropAlertPrompt>
              }
              {...props}
            />
          )}
        </DragAndDropArea>

        {files.length !== 0 && (
          <Flex
            direction="column"
            ml={{ base: '0', md: '60px' }}
            mt={{ base: '16px', md: '0' }}
          >
            {isProgressComplete(storeFiles) && (
              <Box
                display="flex"
                alignItems="center"
                gap="4px"
              >
                <CheckIcon color="green.500" />
                <Text color="royalBlue.500">
                  {uploaded}/{total} files uploaded.
                </Text>
                {failed > 0 && <Text color="red.100">{failed} files failed.</Text>}
              </Box>
            )}
            <Flex
              direction="column"
              width={{ base: '100%', md: '560px' }}
              height={{ base: 'max-content', md: '472px' }}
              borderRadius={!isMobile ? '8px' : undefined}
              background={!isMobile ? 'rgba(255, 255, 255, 0.40)' : undefined}
              padding={!isMobile ? '16px 12px' : undefined}
              overflowX="hidden"
              overflowY={{ base: 'hidden', md: 'scroll' }}
            >
              <Stack spacing="16px">
                {files.map(({ file, progress, error, isRetrying }: any, index: number) => {
                  // Memoize remove and retry handlers to avoid recreating on every render
                  const handleRemove =
                    progress !== 100 && !error
                      ? () => {
                          cancelUpload(file?.name);
                          // Also remove from drag-drop state immediately for instant UI update
                          const updatedFiles = uploadedFiles.filter((f) => f.name !== file?.name);
                          setUploadedFiles(updatedFiles);
                        }
                      : undefined;
                  const handleRetry = isRetrying ? () => retryUpload(file) : undefined;
                  const progressColor = progress !== 100 ? 'royalBlue.500' : 'seaGreen.500';

                  return (
                    <Flex
                      direction="column"
                      minHeight="60px"
                      key={file?.name || `file-${index}`}
                    >
                      <DragAndDropFilePreview
                        file={file}
                        onRemove={handleRemove}
                        onRetry={handleRetry}
                        progress={progress}
                        progressColor={progressColor}
                      />
                      {error && (
                        <Flex mt="4px">
                          <Text
                            fontSize="14px"
                            color="red.100"
                            wordBreak="break-word"
                          >
                            {error}
                          </Text>
                        </Flex>
                      )}
                    </Flex>
                  );
                })}
              </Stack>
            </Flex>
          </Flex>
        )}
      </Flex>
      {isMobile ? (
        <Flex
          width="100%"
          justifyContent={files.length === 0 ? 'flex-start' : 'center'}
          mt="24px"
          mb="24px"
        >
          {files.length === 0 ? (
            <StepPreviousButton onClick={() => onPrevious()}>Previous</StepPreviousButton>
          ) : (
            <StepPrimaryButton
              onClick={submitFilesHandler}
              isDisabled={isSubmitDisabled}
            >
              Done
            </StepPrimaryButton>
          )}
        </Flex>
      ) : (
        <Flex
          width="100%"
          justifyContent="space-between"
          mt="24px"
        >
          <StepPreviousButton
            onClick={() => onPrevious()}
            visibility={files.length === 0 ? 'visible' : 'hidden'}
          >
            Previous
          </StepPreviousButton>
          <StepPrimaryButton
            onClick={submitFilesHandler}
            visibility={files.length !== 0 ? 'visible' : 'hidden'}
            isDisabled={isSubmitDisabled}
          >
            Done
          </StepPrimaryButton>
        </Flex>
      )}
    </Flex>
  );
}
