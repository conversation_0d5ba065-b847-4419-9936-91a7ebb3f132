import { useDisclosure } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import dayjs from 'dayjs';

import { StepCard, StepCardWithFiles } from './StepCard';
import { ADD_MEDICAL_RECORD_STEPS, AMRFSelectDatePayload, AMRStepperState } from './models';
import { UploadedFilesPreview } from './UploadedFilesPreview';
import { DATEPICKER_VARIANTS, DatePickerField } from 'src/components/ui/Form';
import {
  StepDescription,
  StepFormContainer,
  StepHeading,
  StepPreviousButton,
  StepPrimaryButton,
} from './StepComponents';

export function SelectDateStep({
  stepperState,
  initialState,
  onSubmit,
  onPrevious,
}: {
  stepperState: AMRStepperState;
  initialState?: AMRFSelectDatePayload;
  onSubmit: Function;
  onPrevious: Function;
}) {
  const datePickerPopover = useDisclosure();

  const form = useForm<AMRFSelectDatePayload>({
    mode: 'all',
    defaultValues: {
      date: initialState?.date || '',
    },
  });
  const dateField = form.watch('date');

  const handleFromSubmit = (values: AMRFSelectDatePayload) => {
    onSubmit(values);
  };

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('date', '');
    }
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('date', '');
    datePickerPopover.onClose();
  };

  return (
    <FormProvider {...form}>
      <StepCardWithFiles>
        <UploadedFilesPreview
          type={stepperState[ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]?.recordType!}
          files={stepperState[ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]!}
        />
        <StepCard>
          <StepHeading>When was the date of this visit?</StepHeading>
          <StepDescription mb="40px">Record the date of this appointment or test.</StepDescription>
          <StepFormContainer mb="40px">
            <DatePickerField
              variant={DATEPICKER_VARIANTS.ROUNDED_TRANSPARENT}
              // Field props
              name="date"
              labelText="Date of visit"
              errorText="This field is required"
              rules={{
                required: true,
                validate: (value) => dayjs(value).isBefore(dayjs(), 'day') || dayjs(value).isSame(dayjs(), 'day'),
              }}
              isInvalid={form.formState.touchedFields.date && form.control._formValues.date.length === 0}
              // Datepicker props
              datePickerChangeHandler={datePickerChangeHandler}
              datePickerClearHandler={datePickerClearHandler}
              datePickerPopover={datePickerPopover}
              isClearDateButtonDisabled={dateField.length === 0}
              selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
              maxDate={new Date()}
            />
          </StepFormContainer>
          <StepFormContainer mt="auto">
            <StepPreviousButton onClick={() => onPrevious()}>Previous</StepPreviousButton>
            <StepPrimaryButton
              onClick={form.handleSubmit(handleFromSubmit)}
              isDisabled={!form.formState.isValid}
            >
              Continue
            </StepPrimaryButton>
          </StepFormContainer>
        </StepCard>
      </StepCardWithFiles>
    </FormProvider>
  );
}
