import { Flex, FormControl, FormErrorMessage, Input } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';

import { StepCard, StepCardWithFiles } from './StepCard';
import { ADD_MEDICAL_RECORD_STEPS, AMRFSelectTitlePayload, AMRStepperState } from './models';
import { UploadedFilesPreview } from './UploadedFilesPreview';
import {
  StepDescription,
  StepFormContainer,
  StepHeading,
  StepPreviousButton,
  StepPrimaryButton,
} from './StepComponents';
import { validateForTrimmingStr } from '../../lib/utils';

export function SelectTitleStep({
  stepperState,
  initialState,
  onSubmit,
  onPrevious,
}: {
  stepperState: AMRStepperState;
  initialState?: AMRFSelectTitlePayload;
  onSubmit: Function;
  onPrevious: Function;
}) {
  const form = useForm<AMRFSelectTitlePayload>({
    mode: 'all',
    defaultValues: {
      title: initialState?.title || '',
    },
  });

  const handleFromSubmit = (values: AMRFSelectTitlePayload) => {
    onSubmit(values);
  };

  return (
    <StepCardWithFiles>
      <UploadedFilesPreview
        type={stepperState[ADD_MEDICAL_RECORD_STEPS.SELECT_TYPE]?.recordType!}
        files={stepperState[ADD_MEDICAL_RECORD_STEPS.UPLOAD_FILES]!}
      />
      <StepCard>
        <Flex
          direction="column"
          justify="center"
          align="center"
        >
          <StepHeading>What is the name of this record?</StepHeading>
          <StepDescription mb="40px">Name your record for easy access later.</StepDescription>
        </Flex>
        <StepFormContainer mb="40px">
          <FormControl isInvalid={!!form.formState.errors.title}>
            <Input
              variant="roundedTransparent"
              size="xl"
              placeholder="Record title"
              {...form.register('title', {
                required: true,
                validate: validateForTrimmingStr,
              })}
            />
            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>
        </StepFormContainer>
        <StepFormContainer mt="auto">
          <StepPreviousButton onClick={() => onPrevious()}>Previous</StepPreviousButton>
          <StepPrimaryButton
            onClick={form.handleSubmit(handleFromSubmit)}
            isDisabled={!form.formState.isValid}
          >
            Continue
          </StepPrimaryButton>
        </StepFormContainer>
      </StepCard>
    </StepCardWithFiles>
  );
}
