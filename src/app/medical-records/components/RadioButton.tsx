// Package modules
import React from 'react';
import { Box, Flex, RadioProps, useRadio, useTheme } from '@chakra-ui/react';

// Local modules
import { hexOpacity } from '../../../components/theme/utils';

interface IRadioButton {
  radioProps: RadioProps;
  children: React.ReactElement;
}
export function RadioButton({ children, radioProps }: IRadioButton) {
  const { getInputProps, getCheckboxProps } = useRadio(radioProps);
  const theme = useTheme();

  const input = getInputProps();
  const checkbox = getCheckboxProps();

  return (
    <Box as="label">
      <input {...input} />
      <Flex
        gap="18px"
        align="center"
        cursor="pointer"
        borderWidth="1.5px"
        borderRadius="40px"
        px={5}
        py={4}
        transition="box-shadow 0.1s ease"
        _checked={{
          bg: 'fluentHealthSecondary.400',
          color: 'fluentHealthSecondary.100',
          borderColor: 'fluentHealthSecondary.200',
        }}
        _hover={{
          boxShadow: `0 5px 6px 0px ${hexOpacity(theme.colors.fluentHealth['500'], 0.08)}`,
          borderColor: 'fluentHealthSecondary.200',
        }}
        {...checkbox}
      >
        <Flex
          align="center"
          justify="center"
          width="20px"
          height="20px"
          border="1.5px solid"
          borderColor={hexOpacity(theme.colors.fluentHealth['500'], 0.3)}
          borderRadius="50%"
          flexShrink="0"
        >
          {radioProps.isChecked && (
            <Box
              width="12px"
              height="12px"
              borderRadius="50%"
              backgroundColor="fluentHealth.500"
            />
          )}
        </Flex>
        {children}
      </Flex>
    </Box>
  );
}
