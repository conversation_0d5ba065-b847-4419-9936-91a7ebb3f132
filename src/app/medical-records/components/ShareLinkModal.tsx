// Package modules
import React, { Suspense, useEffect, useState } from 'react';
import { Box, Flex, Text, useTheme, useToast } from '@chakra-ui/react';
import { AlertTriangle as AlertIcon } from 'react-feather';
import { FormProvider, useForm } from 'react-hook-form';
import { StylesConfig } from 'react-select';
import { useShareProfileRecord } from '@user/lib/medplum-state';
import { medplumApi } from '@user/lib/medplum-api';
import { recordSharedMedicalRecordEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF } from '@user/lib/constants';

import { PUBLIC_SETTINGS_PROPERTY_NAME } from '@lib/models/public-settings';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { MedicalRecord } from '@lib/models/medical-record';
import { MODAL_VARIANTS, Modal, useModalContext } from '../../../components/Modal';
import { colors } from '../../../components/theme/colors';
import { CopyToClipboardInput } from '../../../components/ui/Input/CopyToClipboardInput';
import { SELECT_STYLES as BASE_SELECT_STYLES, Select, SelectOptionProps } from '../../../components/ui/Select';
import { FluentHealthLoader } from '../../../components/FluentHealthLoader';

type FormValues = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN]: string;
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXTERNAL_REPORTS]: MedicalRecord[];
};

const UPDATE_SETTINGS_DELAY = 3000;
// const DATE_MANIPULATE_DIVIDER = '|';

// https://day.js.org/docs/en/manipulate/add
const EXPIRATION_SELECT_OPTIONS = [
  { label: '1 day', value: `1` },
  { label: '2 days', value: `2` },
  { label: '7 days', value: `7` },
  // { label: '1 month', value: `30` },
];

const DEFAULT_FORM_VALUES: FormValues = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN]: EXPIRATION_SELECT_OPTIONS[1].value,
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXTERNAL_REPORTS]: [],
};

// Overwrite select styles
const SELECT_STYLES: StylesConfig = {
  ...BASE_SELECT_STYLES,
  menu: (base, props) => ({
    ...BASE_SELECT_STYLES.menu!(base, props),
    minWidth: '100px',
  }),
  indicatorsContainer: (base, props) => ({
    ...BASE_SELECT_STYLES.indicatorsContainer!(base, props),
    padding: '5px',
  }),
  dropdownIndicator: (base, props) => ({
    ...BASE_SELECT_STYLES.dropdownIndicator!(base, props),
    padding: '0',
    color: colors.gray[200],
  }),
  singleValue: (base, props) => ({
    ...BASE_SELECT_STYLES.singleValue!(base, props),
    fontSize: '16px',
    color: colors.gray[500],
  }),
  valueContainer: (base, props) => ({
    ...BASE_SELECT_STYLES.valueContainer!(base, props),
    minHeight: '28px',
    height: '28px',
    marginTop: '-3px',
    paddingLeft: '8px',
    color: colors.gray[500],
  }),
  control: (base, props) => ({
    ...BASE_SELECT_STYLES.control!(base, props),
    backgroundColor: 'white',
    borderRadius: '8px',
    minHeight: '28px',
    height: '28px',
    border: `1px solid ${colors.gray[100]}`,
    borderTop: `1px solid ${colors.gray[100]}`,
    borderLeft: `1px solid ${colors.gray[100]}`,
    borderRight: `1px solid ${colors.gray[100]}`,
  }),
};

function ShareLinkForm({ records }: { records: any[] }) {
  const [publicUrl, setPublicUrl] = useState<string>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { authenticatedUser } = useAuthService();
  const { createTaskForShare } = useShareProfileRecord(authenticatedUser?.id);

  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();

  const form = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: DEFAULT_FORM_VALUES,
  });
  const expirationTimeField = form.watch(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN);

  const copyToClipboardHandler = async (value: string) => {
    try {
      setIsLoading(true);
      await navigator.clipboard.writeText(value);
      recordSharedMedicalRecordEvents(trackEventInFlow, {
        EventName: 'RecordShared',
        rs_record_type: `${records?.[0]?.medicalRecordType?.display}`,
        rs_record_owner: records?.[0]?.nameOnRecord,
        document_id: records?.[0]?.id,
        rs_platform: 'web',
      });
      toast({
        title: 'Successfully copied',
        status: 'success',
        duration: 1500,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setTimeout(setIsLoading, 500, false);
    }
  };

  const updatePublicUrl = async (daysToExpire: string) => {
    const respData = await createTaskForShare({
      type: 'share-records',
      daysToExpire,
      data:
        records.length > 0
          ? records.map((record) => ({
              meaning: DOCUMENT_REF,
              reference: {
                reference: `${DOCUMENT_REF}/${record?.id}`,
              },
            }))
          : [],
    });
    const taskEntry = respData?.entry?.find((e: any) => e.resource?.resourceType === 'Task');

    const taskId = taskEntry?.resource?.id;
    if (taskId) {
      // TODO: currently doing the polling move to shift to subscription when notification
      const poll = setInterval(async () => {
        const urlData: any = await medplumApi.shareProfileRecord.getShareRecordURL(taskId).catch((error: any) => {
          clearInterval(poll);
          setIsLoading(false);
          return error;
        });
        if (urlData?.TaskList?.[0]?.output?.length > 0) {
          clearInterval(poll);
          setPublicUrl(urlData?.TaskList?.[0]?.output?.[0]?.valueString);
          setIsLoading(false);
        }
      }, UPDATE_SETTINGS_DELAY);
    }
  };
  const onSettingsChange = () => {
    setIsLoading(true);
  };

  const onExpiryTimeSelectChange = async (option: SelectOptionProps | any) => {
    form.setValue(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN, option.value);
    await form.trigger(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN);
    onSettingsChange();
  };

  // Generate an initial public url on first render
  useEffect(() => {
    updatePublicUrl(expirationTimeField);
  }, [expirationTimeField]);

  return (
    <FormProvider {...form}>
      <form onChange={form.handleSubmit(onSettingsChange)}>
        <CopyToClipboardInput
          value={publicUrl}
          onCopy={() => copyToClipboardHandler(publicUrl!)}
          isLoading={isLoading}
          bgColor="periwinkle.50"
          border="none"
          borderRadius="12px"
        />
        <Flex
          direction="column"
          gap="14px"
          marginBottom="26px"
        >
          <Flex
            direction="row"
            gap="4px"
            alignItems="center"
            mt="16px"
          >
            <Text
              fontFamily="Apercu"
              fontWeight="normal"
              fontSize="sm"
              lineHeight="1"
              color="fluentHealthText.200"
            >
              Link expires in
            </Text>
            <Select
              value={EXPIRATION_SELECT_OPTIONS.find((option) => option.value === expirationTimeField)}
              options={EXPIRATION_SELECT_OPTIONS}
              onChange={onExpiryTimeSelectChange}
              styles={SELECT_STYLES}
              isSearchable={false}
              menuPosition="fixed"
            />
          </Flex>
        </Flex>
      </form>
    </FormProvider>
  );
}

export function ShareLinkModal({ records }: { records: any[] }) {
  const theme = useTheme();
  const { modalProps, modalDisclosure } = useModalContext();

  return (
    <Modal
      variant={MODAL_VARIANTS.PERIWINKLE}
      title="Share Record"
      showPrimaryButton={false}
      showSecondaryButton={false}
      minWidth="600px"
      footerAlert={
        <Flex
          alignItems="flex-start"
          direction="row"
          gap="17px"
          mx="-24px"
          mb="-20px"
          py="16px"
          px="24px"
          bg="fluentHealthSecondary.500"
          borderBottomRightRadius="20px"
          borderBottomLeftRadius="20px"
          borderTop="1px solid"
          borderColor="fluentHealthSecondary.400"
        >
          <AlertIcon
            size={20}
            color={theme.colors.iris[500]}
            style={{ flexShrink: 0, marginTop: '5px' }}
          />
          <Text
            fontFamily="Apercu"
            fontWeight="normal"
            lineHeight="shorter"
            color="fluentHealth.500"
          >
            Be Fluent LLP has no control over who accesses the information/records that you are sharing and shall not be
            liable in any manner for any unauthorised access to the information/records via the shared link. By
            exporting and sharing this information/records outside the App, you acknowledge that there is a risk of the
            information / record no longer remaining confidential and secure.
          </Text>
        </Flex>
      }
      isCentered
      {...modalProps}
      {...modalDisclosure}
    >
      <Box
        pt="12px"
        mx="-4px"
      >
        <Suspense fallback={<FluentHealthLoader my="12" />}>
          <ShareLinkForm records={records} />
        </Suspense>
      </Box>
    </Modal>
  );
}
