// Package modules
import React, { PropsWithChildren, Suspense, useMemo, useState } from 'react';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Container,
  Drawer,
  <PERSON>erContent,
  DrawerOverlay,
  Flex,
  IconButton,
  Radio,
  RadioGroup,
  Skeleton,
  Spinner,
  Stack,
  Text,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { PlusCircle as AddIcon, Check as CheckIcon, X as CloseIcon } from 'react-feather';
import dayjs from 'dayjs';
import { useSearchParams } from 'react-router-dom';
// Local modules
import { recordMedicalRecordFilterEvents } from '@user/lib/events-analytics-manager';

import { DatePicker, DateRange } from '../../../components/ui/DatePicker';
import { useIsMobile } from '../../../components/ui/hooks/device.hook';
import { ExternalReportTag, ExternalReportType, MEDICAL_RECORD_FILTERS } from '@lib/models/medical-record';
import { PeriodButtons, PeriodInputs } from '../../../components/PeriodSelector';
import { hexOpacity } from '../../../components/theme/utils';
import { BREAKPOINTS } from '@lib/constants';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { SORT_BY_OPTION_LIST, SORT_BY_VALUES } from '../lib/constants';
import { useIsLoadingMedicalRecords, useMedicalRecordDisplayCategories, useMedicalRecordTagList } from '../lib/state';
import {
  getDateSearchParamValue,
  getRecordTypeSearchParamValue,
  getSortByParamValue,
  getTagsSearchParamValue,
} from '../lib/utils';
import { SidebarCloseButton } from 'src/app/user/profile/components/SidebarComponents';

import { ReactComponent as FilterIcon } from '@assets/icons/filters.svg';

// Helpers

const getAppliedFiltersCount = (searchParams: URLSearchParams) => {
  const keys = Array.from(searchParams.keys());
  const sidebarFiltersKeys: string[] = [MEDICAL_RECORD_FILTERS.REPORT_TYPE, MEDICAL_RECORD_FILTERS.TAGS];

  if (keys) {
    return keys.length > 0 ? keys.filter((key: string) => sidebarFiltersKeys.includes(key)).length : 0;
  }
  return 0;
};

function FilterPropertyCard({
  title,
  description,
  filterValueText,
  filterValuePlaceholder,
  children,
}: PropsWithChildren<{
  title: string;
  filterValueText?: string;
  filterValuePlaceholder?: string;
  description?: string;
}>) {
  const isMobile = useIsMobile();

  return (
    <AccordionItem
      borderTopWidth="0"
      _last={{ borderBottomWidth: '0' }}
    >
      {({ isExpanded }) => (
        <Card
          border="1px solid"
          borderRadius="xl"
          borderColor="iris.500"
          boxShadow="none"
        >
          <AccordionButton
            as={CardHeader}
            py="20px"
            cursor="default"
            _hover={{
              bg: 'transparent',
            }}
            {...(isMobile ? {} : { onClick: (e: React.MouseEvent) => e.preventDefault() })}
          >
            <Flex
              justify="space-between"
              align="flex-start"
              width="full"
            >
              <Flex
                direction="column"
                justify="flex-start"
                align="flex-start"
                gap="6px"
                width="full"
              >
                <Flex
                  justify="space-between"
                  align="flex-start"
                  gap="32px"
                  width="full"
                >
                  <Text
                    fontSize={isExpanded ? 'xl' : 'md'}
                    color={isExpanded ? 'fluentHealthText.100' : 'fluentHealthText.200'}
                    lineHeight="1"
                    flexShrink="0"
                  >
                    {title}
                  </Text>
                  {isMobile &&
                    ((filterValueText || filterValuePlaceholder) && filterValueText ? (
                      <Text
                        fontSize="sm"
                        overflow="hidden"
                        textOverflow="ellipsis"
                        whiteSpace="nowrap"
                      >
                        {filterValueText}
                      </Text>
                    ) : (
                      <Text
                        fontSize="sm"
                        color="fluentHealthText.250"
                      >
                        {filterValuePlaceholder}
                      </Text>
                    ))}
                </Flex>
                {(!isMobile && description) ||
                  (isMobile && isExpanded && description && (
                    <Text
                      fontSize="sm"
                      textAlign="start"
                      color="fluentHealthText.200"
                    >
                      {description}
                    </Text>
                  ))}
              </Flex>
            </Flex>
          </AccordionButton>
          <CardBody
            as={AccordionPanel}
            pt="0"
          >
            {children}
          </CardBody>
        </Card>
      )}
    </AccordionItem>
  );
}

function FilterCardSkeleton() {
  return (
    <FilterPropertyCard title="Loading...">
      <Flex
        gap="12px"
        wrap="wrap"
      >
        <Skeleton
          width="182px"
          height="38px"
          borderRadius="full"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          width="112px"
          height="38px"
          borderRadius="full"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          width="142px"
          height="38px"
          borderRadius="full"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          width="182px"
          height="38px"
          borderRadius="full"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
      </Flex>
    </FilterPropertyCard>
  );
}

export function SelectionComponent({
  title,
  onClick,
  isSelected,
  isDisabled = false,
}: {
  title: string;
  onClick?: () => void;
  isSelected: boolean;
  isDisabled: boolean;
}) {
  const theme = useTheme();
  return (
    <Button
      mr="2"
      mb="3"
      py="8px"
      variant="outline"
      size="sm"
      color={isSelected ? 'fluentHealth.500' : 'gray'}
      border="1.5px solid"
      borderColor={isSelected ? 'fluentHealth.500' : 'fluentHealthComplementary.GrayBorder'}
      onClick={onClick}
      isDisabled={isDisabled}
      rightIcon={
        isSelected ? (
          <Box
            bgColor="fluentHealth.500"
            borderRadius="full"
            p="3px"
          >
            <CheckIcon
              size={14}
              color="white"
            />
          </Box>
        ) : (
          <AddIcon
            size={20}
            color={hexOpacity(theme.colors.gray[600], 0.2)}
          />
        )
      }
    >
      <Text
        color="fluentHealthText.100"
        fontWeight="normal"
        maxWidth="100px"
        overflow="hidden"
        textOverflow="ellipsis"
      >
        {title}
      </Text>
    </Button>
  );
}

function PeriodFilter({ dateRange, setRangeDate }: { dateRange: DateRange; setRangeDate: (range: DateRange) => void }) {
  const [startDate, endDate] = dateRange;

  const [searchParams, setSearchParams] = useSearchParams();

  const updateSearchParams = async (fromDate: Date | null = null, toDate: Date | null = null) => {
    if (fromDate && fromDate?.toString()?.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.FROM_DATE, dayjs(fromDate).format('YYYY-MM-DD'));
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.FROM_DATE);
    }

    if (toDate && toDate?.toString()?.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.TO_DATE, dayjs(toDate).format('YYYY-MM-DD'));
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.TO_DATE);
    }

    await setSearchParams(searchParams, { replace: true });
  };

  const onDateChange = async (date: DateRange | Date | null) => {
    let resultDate: DateRange | Date | null = null;

    if (date instanceof Date) {
      resultDate = [date, date];
    } else {
      resultDate = date === null ? [null, null] : date;
    }
    setRangeDate(resultDate);
    await updateSearchParams(resultDate[0], resultDate[1]);
  };

  const onStartDateInputChange = async (date: Date) => {
    setRangeDate([date, dateRange[1]]);
    await updateSearchParams(date, dateRange[1]);
  };

  const onEndDateInputChange = async (date: Date) => {
    let resultDate: [Date | null, Date | null] = [dateRange[0], date];

    // If the end date is less than the start date, then reset the start date.
    if (startDate && startDate.valueOf() > date.valueOf()) {
      resultDate = [null, date];
    }

    setRangeDate(resultDate);
    await updateSearchParams(resultDate[0], resultDate[1]);
  };

  const onPeriodButtonChange = async (dates: DateRange) => {
    setRangeDate(dates);
    await updateSearchParams(dates[0], dates[1]);
  };

  return (
    <>
      <PeriodButtons onChange={onPeriodButtonChange} />
      <PeriodInputs
        startDate={startDate}
        endDate={endDate}
        onStartDateChange={onStartDateInputChange}
        onEndDateChange={onEndDateInputChange}
      />
      <DatePicker
        monthsShown={1}
        selected={startDate}
        startDate={startDate}
        endDate={endDate}
        onChange={onDateChange}
        // @ts-ignore
        selectsRange
        inline
        isClearable
      />
    </>
  );
}

interface LabelValuePair {
  label: string;
  value: string;
}

function RecordTypeFilter({
  selectedRecordTypeIds,
  setSelectedRecordTypeIds,
}: {
  selectedRecordTypeIds: ExternalReportType['report_type_id'][];
  setSelectedRecordTypeIds: Function;
}) {
  const { trackEventInFlow } = useAnalyticsService();
  const [categories, setCategories] = useState<LabelValuePair[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();

  const { isCategoryLoading } = useMedicalRecordDisplayCategories({ setCategories });

  const selectedRecordTypes = useMemo(
    () => categories.filter((recordType) => selectedRecordTypeIds.includes(recordType.value)),
    [selectedRecordTypeIds]
  );
  const updateSearchParams = (searchQuery: string) => {
    if (searchQuery.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.REPORT_TYPE, searchQuery);
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.REPORT_TYPE);
    }
    setSearchParams(searchParams);
  };

  const onSelectionChange = (recordTypeId: ExternalReportType['report_type_id']) => {
    let result: ExternalReportType['report_type_id'][] = [];

    if (selectedRecordTypeIds.find((sTypeId) => sTypeId === recordTypeId)) {
      result = selectedRecordTypeIds.filter((sTypeId) => sTypeId !== recordTypeId);
    } else {
      result = [...selectedRecordTypeIds, recordTypeId];
    }
    recordMedicalRecordFilterEvents(trackEventInFlow, {
      EventName: 'RecordsFilteredApplied',
      filter_type: 'Record Type',
      filter_value: result.join(','),
    });
    setSelectedRecordTypeIds(result);
    updateSearchParams(result.join(','));
  };

  return (
    <FilterPropertyCard
      title="Filter by record type"
      description="View your records related to the type of record."
      filterValueText={selectedRecordTypes.map((type) => type.value).join(', ')}
      filterValuePlaceholder="Select record type"
    >
      {categories.map((type: any) => {
        const { value, label } = type || {};

        return (
          <SelectionComponent
            key={value}
            title={label}
            onClick={() => onSelectionChange(value)}
            isSelected={!!selectedRecordTypeIds.find((tag) => tag === value)}
            isDisabled={isCategoryLoading}
          />
        );
      })}
    </FilterPropertyCard>
  );
}

interface ListType {
  item: any;
  title: string;
}

function TagsFilter({
  selectedTags,
  setSelectedTags,
}: {
  selectedTags: ExternalReportTag['name'][];
  setSelectedTags: Function;
}) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [medicalRecordTagList, setMedicalRecordTagList] = useState<ListType[]>([]);

  const { authenticatedUser } = useAuthService();
  useMedicalRecordTagList(authenticatedUser?.id, setMedicalRecordTagList);

  const isLoadingMedicalRecords = useIsLoadingMedicalRecords(authenticatedUser?.id, Object.fromEntries(searchParams));

  const selectedRecordTypes = useMemo(
    () => medicalRecordTagList?.filter((tag) => selectedTags.includes(tag.title)),
    [selectedTags]
  );

  const updateSearchParams = (searchQuery: string) => {
    if (searchQuery.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.TAGS, searchQuery);
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.TAGS);
    }
    setSearchParams(searchParams);
  };

  const onSelectionChange = (tag: ExternalReportTag['name']) => {
    let result: ExternalReportTag['name'][] = [];

    if (selectedTags.find((sTag) => sTag === tag)) {
      result = selectedTags.filter((sTag) => sTag !== tag);
    } else {
      result = [...selectedTags, tag];
    }

    setSelectedTags(result);
    updateSearchParams(result.join(','));
  };
  return (
    <FilterPropertyCard
      title="Filter by tags"
      description="View your records related to a particular condition using the tags you created."
      filterValueText={selectedRecordTypes?.map((tag) => tag.title).join(', ')}
      filterValuePlaceholder="Add tags"
    >
      {medicalRecordTagList?.map((tag) => (
        <SelectionComponent
          key={tag?.item?.display}
          title={tag?.item?.display}
          onClick={() => onSelectionChange(tag?.item?.display)}
          isSelected={!!selectedTags.find((sTag) => sTag === tag?.item?.display)}
          isDisabled={isLoadingMedicalRecords}
        />
      ))}
    </FilterPropertyCard>
  );
}

function SortByFilter({ selectedSortBy, setSelectedSortBy }: { selectedSortBy: string; setSelectedSortBy: Function }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const isLoadingMedicalRecords = useIsLoadingMedicalRecords(authenticatedUser?.id, Object.fromEntries(searchParams));

  const updateSearchParams = (searchQuery: string) => {
    if (searchQuery.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.SORT_BY, searchQuery);
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.SORT_BY);
    }
    setSearchParams(searchParams);
  };

  const onSelectionChange = async (sortBy: string) => {
    await setSelectedSortBy(sortBy);
    await updateSearchParams(sortBy);
    recordMedicalRecordFilterEvents(trackEventInFlow, {
      EventName: 'RecordsFilteredApplied',
      filter_type: 'Sort',
      filter_value: SORT_BY_OPTION_LIST.find((item) => item.value === sortBy)?.label,
    });
  };

  return (
    <FilterPropertyCard
      title="Sort by"
      filterValueText={SORT_BY_OPTION_LIST.find((item) => item.value === selectedSortBy)?.label}
      filterValuePlaceholder="Select sorting"
    >
      <RadioGroup
        onChange={onSelectionChange}
        value={selectedSortBy}
        isDisabled={isLoadingMedicalRecords}
      >
        <Stack
          direction="column"
          spacing="5"
        >
          {SORT_BY_OPTION_LIST.map((item) => (
            <Radio
              variant="checkbox"
              key={item.value}
              value={item.value}
            >
              {item.label}
            </Radio>
          ))}
        </Stack>
      </RadioGroup>
    </FilterPropertyCard>
  );
}

function FilterContainer({ onClose }: { onClose: () => void }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRecordTypeIds, setSelectedRecordTypeIds] = useState(getRecordTypeSearchParamValue(searchParams));
  const [selectedTags, setSelectedTags] = useState(getTagsSearchParamValue(searchParams));
  const [selectedSortBy, setSelectedSortBy] = useState<string>(
    getSortByParamValue(searchParams) || SORT_BY_VALUES.RECENT
  );
  const { trackEventInFlow } = useAnalyticsService();
  const [dateRange, setRangeDate] = useState<DateRange>([
    getDateSearchParamValue(MEDICAL_RECORD_FILTERS.FROM_DATE, searchParams),
    getDateSearchParamValue(MEDICAL_RECORD_FILTERS.TO_DATE, searchParams),
  ]);

  const { authenticatedUser } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const isLoadingMedicalRecords = useIsLoadingMedicalRecords(authenticatedUser?.id, Object.fromEntries(searchParams));

  const isMobile = useIsMobile();
  const theme = useTheme();

  const clearFilters = () => {
    recordMedicalRecordFilterEvents(trackEventInFlow, {
      EventName: 'RecordsFilterRemoved',
    });
    setSelectedRecordTypeIds([]);
    setSelectedTags([]);
    setRangeDate([null, null]);
    setSelectedSortBy(SORT_BY_VALUES.RECENT);

    Array.from(searchParams.keys()).forEach((searchParamKey) => {
      if (Object.values(MEDICAL_RECORD_FILTERS).includes(searchParamKey as MEDICAL_RECORD_FILTERS)) {
        searchParams.delete(searchParamKey);
      }
    });
    setSearchParams(searchParams, {
      replace: true,
    });
  };

  const periodFilterValueText = dateRange
    .map((date) => {
      if (dayjs(date).isValid()) {
        return dayjs(date).format('D MMM.');
      }
      return null;
    })
    .filter((date) => date !== null)
    .join(' - ');

  return (
    <Container
      display="flex"
      flexDirection="column"
      background="linear-gradient(180deg, #F7F8FF 0%, #EBECFF 24.96%)"
      height="full"
      pb="24px"
    >
      <Flex
        justifyContent="space-between"
        py="4"
      >
        <Flex align="center">
          <Text fontSize="xl">Filters</Text>
          {!isMobile && (
            <Button
              variant="ghost"
              fontWeight="medium"
              color="fluentHealth.500"
              size="sm"
              ml="5"
              onClick={clearFilters}
            >
              Clear filters
            </Button>
          )}
        </Flex>
        <IconButton
          aria-label="Close Button"
          icon={<SidebarCloseButton />}
          variant="ghost"
          size="sm"
          onClick={onClose}
        />
      </Flex>
      <Accordion
        display="flex"
        flexDirection="column"
        gap="10px"
        defaultIndex={isMobile ? [0] : [0, 1, 2, 3, 4]}
        {...(isMobile ? {} : { index: [0, 1, 2, 3, 4] })}
        overflowY="auto"
        allowToggle
        sx={{ [`@media ${BREAKPOINTS.TABLET}`]: { gap: '16px' } }}
      >
        <SortByFilter
          selectedSortBy={selectedSortBy}
          setSelectedSortBy={setSelectedSortBy}
        />
        <Suspense fallback={<FilterCardSkeleton />}>
          <RecordTypeFilter
            selectedRecordTypeIds={selectedRecordTypeIds}
            setSelectedRecordTypeIds={setSelectedRecordTypeIds}
          />
        </Suspense>
        {isMobile && (
          <FilterPropertyCard
            title="Period"
            filterValueText={periodFilterValueText}
            filterValuePlaceholder="Select period"
          >
            <PeriodFilter
              dateRange={dateRange}
              setRangeDate={setRangeDate}
            />
          </FilterPropertyCard>
        )}
        {!isPublicMode && (
          <Suspense fallback={<FilterCardSkeleton />}>
            <TagsFilter
              selectedTags={selectedTags}
              setSelectedTags={setSelectedTags}
            />
          </Suspense>
        )}
      </Accordion>
      {isMobile && (
        <Flex
          py="12px"
          px="16px"
          mt="auto"
          ml="-16px"
          mr="-16px"
          bgColor="white"
          justify="space-between"
          align="center"
        >
          <Button
            variant="ghost"
            fontWeight="medium"
            color="fluentHealth.500"
            size="sm"
            px="0"
            onClick={clearFilters}
          >
            Clear all filters
          </Button>
          <Flex
            gap="4px"
            align="center"
          >
            {isLoadingMedicalRecords ? (
              <Spinner
                width="18px"
                height="18px"
                color="green.500"
              />
            ) : (
              <CheckIcon
                size={18}
                color={theme.colors.green[500]}
              />
            )}
            <Text
              as="span"
              color="green.500"
            >
              {isLoadingMedicalRecords ? 'Applying' : 'Filters applied'}
            </Text>
          </Flex>
        </Flex>
      )}
    </Container>
  );
}

export function SidebarFilter() {
  const [searchParams, setSearchParams] = useSearchParams();
  const filterDisclosure = useDisclosure();
  const isMobile = useIsMobile();

  const appliedFiltersCount = getAppliedFiltersCount(searchParams);
  const isFiltersApplied = appliedFiltersCount > 0;

  const clearFiltersHandler = async (event: React.MouseEvent<SVGElement | HTMLButtonElement>) => {
    event.preventDefault();
    searchParams.delete(MEDICAL_RECORD_FILTERS.REPORT_TYPE);
    searchParams.delete(MEDICAL_RECORD_FILTERS.TAGS);
    await setSearchParams(searchParams);
    filterDisclosure.onClose();
  };

  return (
    <>
      <Button
        py="8px"
        px={{ base: '8px', md: '4' }}
        variant="outline"
        minWidth="unset"
        color={isFiltersApplied ? 'royalBlue.500' : 'charcoal.100'}
        borderColor={isFiltersApplied ? 'fluentHealth.500' : 'iris.100'}
        boxShadow={isFiltersApplied ? '0 0 0 2px #DADCFF' : 'none'}
        iconSpacing="1"
        {...(isMobile && !isFiltersApplied
          ? null
          : {
              rightIcon: (
                <IconButton
                  as="span"
                  aria-label={isFiltersApplied ? 'Clear filters' : 'Filters'}
                  size="18px"
                  bgColor="transparent"
                  color={isFiltersApplied ? 'fluentHealthText.250' : 'charcoal.100'}
                  borderRadius="50%"
                  flexShrink="0"
                  icon={
                    isFiltersApplied ? (
                      <CloseIcon
                        width="18px"
                        height="18px"
                        onClick={clearFiltersHandler}
                      />
                    ) : (
                      <FilterIcon
                        width="18px"
                        height="18px"
                      />
                    )
                  }
                  _hover={{
                    bgColor: 'transparent',
                  }}
                />
              ),
            })}
        onClick={filterDisclosure.onOpen}
        _hover={{ borderColor: 'fluentHealth.500' }}
      >
        {isMobile ? (
          <FilterIcon
            width="18px"
            height="18px"
          />
        ) : (
          'Filters'
        )}
        {isFiltersApplied && (
          <Flex
            align="center"
            justify="center"
            pt="2px"
            pb="1px"
            px="2px"
            ml="4px"
            minW="20px"
            height="20px"
            borderRadius="full"
            bgColor="fluentHealthSecondary.400"
          >
            {appliedFiltersCount}
          </Flex>
        )}
      </Button>
      <Drawer
        isOpen={filterDisclosure.isOpen}
        placement="right"
        onClose={filterDisclosure.onClose}
        size="sm"
      >
        <DrawerOverlay />
        <DrawerContent>
          <FilterContainer onClose={filterDisclosure.onClose} />
        </DrawerContent>
      </Drawer>
    </>
  );
}
