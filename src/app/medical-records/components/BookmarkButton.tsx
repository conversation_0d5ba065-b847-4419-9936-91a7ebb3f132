// Package modules
import React from 'react';
import { IconButton, Skeleton, useTheme } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';
import { useSearchParams } from 'react-router-dom';
import { recordMedicalRecordFilterEvents } from '@user/lib/events-analytics-manager';

import { useAnalyticsService } from '@lib/state';
import { MEDICAL_RECORD_FILTERS } from '@lib/models/medical-record';

export function BookmarkButtonSkeleton() {
  return (
    <Skeleton
      width="36px"
      height="36px"
      borderRadius="full"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.500"
    />
  );
}

export function BookmarkButton() {
  const theme = useTheme();
  const [searchParams, setSearchParams] = useSearchParams();
  const { trackEventInFlow } = useAnalyticsService();
  const inBookmark = searchParams.get(MEDICAL_RECORD_FILTERS.STARRED) === 'mr-book';

  const filterByBookmarkHandler = async () => {
    if (inBookmark) {
      searchParams.delete(MEDICAL_RECORD_FILTERS.STARRED);
      await setSearchParams(searchParams);
    } else {
      searchParams.set(MEDICAL_RECORD_FILTERS.STARRED, 'mr-book');
      await setSearchParams(searchParams);
    }
    recordMedicalRecordFilterEvents(trackEventInFlow, {
      EventName: inBookmark ? 'RecordsFilterRemoved' : 'RecordsFilteredApplied',
      filter_type: 'Bookmark',
      filter_value: inBookmark ? 'Show all' : 'Show Bookmark',
    });
  };

  return (
    <IconButton
      minW="36px"
      h="36px"
      aria-label="Bookmark Button"
      variant="outline"
      borderColor={inBookmark ? 'fluentHealthSecondary.200' : 'iris.100'}
      icon={
        <BookmarkIcon
          size={18}
          stroke={inBookmark ? theme.colors.fluentHealth[500] : theme.colors.charcoal[60]}
          fill={inBookmark ? theme.colors.fluentHealth[500] : 'transparent'}
        />
      }
      _hover={{ borderColor: 'fluentHealth.500' }}
      onClick={filterByBookmarkHandler}
    />
  );
}
