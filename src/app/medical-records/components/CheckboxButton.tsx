// Package modules
import React from 'react';
import { CheckboxProps, chakra, useCheckbox } from '@chakra-ui/react';

interface ICheckboxButton {
  children: React.ReactElement;
  checkboxProps: CheckboxProps;
}
export function CheckboxButton({ children, checkboxProps }: ICheckboxButton) {
  const { state, getInputProps, htmlProps } = useCheckbox(checkboxProps);

  return (
    <chakra.label
      bg={state.isChecked ? 'fluentHealthSecondary.400' : 'fluentHealthSecondary.500'}
      border="1px solid"
      borderColor={state.isChecked ? 'fluentHealthSecondary.200' : 'transparent'}
      borderRadius="16px"
      transition="background 0.1s ease, border-color 0.1s ease"
      py="3"
      px="5"
      cursor="pointer"
      _hover={{ bg: 'fluentHealthSecondary.400' }}
      {...htmlProps}
    >
      <input
        {...getInputProps()}
        hidden
      />
      {children}
    </chakra.label>
  );
}
