import { Checkbox, Flex } from '@chakra-ui/react';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { SearchBar } from './SearchBar';
import { usePublicRecordSettings } from '@lib/state';

export function MedicalRecordListFilters({
  allSelected,
  handleSelectAll,
  fetchMedicalRecord,
  displayMedicalRecordList,
  isEmptyMedicalRecordList,
}: {
  allSelected: boolean;
  handleSelectAll: (isChecked: boolean) => void;
  fetchMedicalRecord: (docId: string[]) => void;
  displayMedicalRecordList: any;
  isEmptyMedicalRecordList: boolean;
}) {
  const isMobile = useIsMobile();
  const { isPublicRecordMode, isMultiRecord } = usePublicRecordSettings();
  return (
    <Flex
      justifyContent={isMobile ? 'space-between' : undefined}
      width={{ base: '100%', md: 'auto' }}
      gap="8px"
    >
      <Flex>
        {isMobile && !isPublicRecordMode && !!displayMedicalRecordList?.length && (
          <Checkbox
            mr="8px"
            isChecked={allSelected}
            onChange={(e: any) => handleSelectAll(e.target.checked)}
            size="md"
            borderColor="gray.700"
            sx={{
              '& .chakra-checkbox__control[data-checked]': {
                bg: 'royalBlue.500', // ✅ Background when checked
                borderColor: 'royalBlue.500',
              },
              '& .chakra-checkbox__control[data-checked]::before': {
                color: 'royalBlue.500', // ✅ Checkmark color
              },
            }}
          />
        )}
        {!isEmptyMedicalRecordList && (!isPublicRecordMode || isMultiRecord) && (
          <SearchBar fetchMedicalRecord={fetchMedicalRecord} />
        )}
      </Flex>
      {!isMobile && !isPublicRecordMode && !!displayMedicalRecordList?.length && (
        <Flex
          as="label"
          align="center"
          gap="8px"
          px="12px"
          py="4px"
          borderRadius="full"
          border="1px solid"
          color={allSelected ? 'royalBlue.500' : 'charcoal.100'}
          borderColor={allSelected ? 'fluentHealth.500' : 'iris.100'}
          boxShadow={allSelected ? '0 0 0 2px #DADCFF' : 'none'}
          cursor="pointer"
          transition="all 0.2s ease-in-out"
        >
          <Checkbox
            isChecked={allSelected}
            onChange={(e: any) => handleSelectAll(e.target.checked)}
            size="md"
            borderColor="gray.700"
            wordBreak="keep-all"
            sx={{
              '& .chakra-checkbox__control[data-checked]': {
                bg: 'royalBlue.500', // ✅ Background when checked
                borderColor: 'royalBlue.500',
              },
              '& .chakra-checkbox__control[data-checked]::before': {
                color: 'royalBlue.500', // ✅ Checkmark color
              },
            }}
          >
            Select All
          </Checkbox>
        </Flex>
      )}
    </Flex>
  );
}
