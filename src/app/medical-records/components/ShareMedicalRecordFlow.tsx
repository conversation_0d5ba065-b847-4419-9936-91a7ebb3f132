// Package modules
import { Button, useTheme } from '@chakra-ui/react';

// Local modules
import { ModalProvider, useModal } from '../../../components/Modal';
import { ShareLinkModal } from './ShareLinkModal';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';

function SvgComponent({ fillPath = 'white' }) {
  return (
    <svg
      width={16}
      height={16}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4346_14018)">
        <path
          d="M10.0607 0.46875V4.39152C6.49756 4.39163 0 4.43072 0 15.5346C0.736667 8.10928 4.12081 8.06259 10.0607 8.06251V12.3464L16 6.40711L10.0607 0.46875Z"
          fill={fillPath}
        />
      </g>
      <defs>
        <clipPath id="clip0_4346_14018">
          <rect
            width={16}
            height={16}
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ShareMedicalRecordFlow({ selectedRecords }: { selectedRecords: any }) {
  const theme = useTheme();
  const shareMedicalRecordModal = useModal();
  const isMobile = useIsMobile();
  const hasSelected = Object.values(selectedRecords).some((value) => value === true);

  const recordArray = Object.entries(selectedRecords)
    .filter(([id, value]) => value === true && id.length === 36)
    .map(([id]) => ({ id }));

  const onTrackShareEvent = () => {
    shareMedicalRecordModal.modalDisclosure.onOpen();
  };

  const isShareModalOpen = shareMedicalRecordModal.modalDisclosure.isOpen;

  return (
    <>
      <Button
        rightIcon={<SvgComponent fillPath={theme.colors.iris[500]} />}
        variant="ghost"
        justifyContent="center"
        // h="36px"
        padding={{ base: '6px 12px 6px 8px', md: '16px' }}
        border="1px solid"
        color="fluentHealth.500"
        borderColor={theme.colors.iris[500]}
        boxShadow={isShareModalOpen ? '0 0 0 2px #DADCFF' : 'none'}
        borderRadius={{ base: '50%', md: '55px' }}
        fontFamily="Apercu"
        fontWeight="medium"
        maxWidth={isMobile ? '36px' : '100%'}
        minWidth={{ base: '36px', md: '3rem' }}
        onClick={onTrackShareEvent}
        isDisabled={!hasSelected}
        _hover={{ borderColor: 'fluentHealth.500' }}
      >
        {!isMobile && 'Share record'}
      </Button>
      <ModalProvider {...shareMedicalRecordModal}>
        <ShareLinkModal records={recordArray} />
      </ModalProvider>
    </>
  );
}
