import { motion } from 'framer-motion';
import { ChangeEvent, KeyboardEvent, useCallback, useEffect, useRef, useState } from 'react';
import { ChakraProps, Flex, Tag, TagLabel, useOutsideClick, useTheme } from '@chakra-ui/react';
import { X as ClearIcon, IconProps, PlusCircle as PlusCircleIcon } from 'react-feather';

import { truncateLength } from '@lib/utils/utils';

export enum ADD_TAGS_MODE {
  DEFAULT,
  ADD_TAG,
}

function BaseXButton({ tagProps, ...props }: any) {
  return (
    <Flex
      position="relative"
      justify="center"
      alignItems="center"
      height="20px"
      borderRadius="full"
      flexShrink={0}
      cursor="pointer"
      overflow="hidden"
      sx={{
        '&:before': {
          content: `''`,
          width: '100%',
          height: '100%',
          opacity: '0.2',
          filter: 'brightness(140%)',
          position: 'absolute',
          top: 0,
          left: 0,
          bgColor: tagProps?.color ?? 'fluentHealthSecondary.100',
        },
      }}
      {...props}
    >
      <ClearIcon size={14} />
    </Flex>
  );
}

export function AddTagsFlow({
  initialTagList,
  maxLength = 40,
  onChange,
  onAdd,
  onRemove,
  tagProps,
  inputProps,
  addButtonProps,
  isAddTagModeByDefault = ADD_TAGS_MODE.DEFAULT,
  showAddButton = true,
}: {
  initialTagList: string[];
  maxLength?: number;
  onChange?: (tags: string[]) => void;
  onAdd?: (tag: string) => void;
  onRemove?: (tag: string) => void;
  tagProps?: ChakraProps;
  inputProps?: ChakraProps;
  addButtonProps?: IconProps;
  isAddTagModeByDefault?: ADD_TAGS_MODE;
  showAddButton?: Boolean;
}) {
  const theme = useTheme();

  const tagInputRef = useRef<HTMLInputElement>(null);
  const [tagInputValue, setTagInputValue] = useState<string>('');
  const [recordTags, setRecordTags] = useState<string[]>(initialTagList);
  const [addTagMode, setAddTagMode] = useState(isAddTagModeByDefault);
  const [showError, setShowError] = useState(false);

  const resetInput = () => {
    setAddTagMode(ADD_TAGS_MODE.DEFAULT);
    setShowError(false);
    setTagInputValue('');
  };

  const addTag = (tag: string) => {
    if (tag.trim().length > 0 && !recordTags.find((t) => t === tag) && !showError) {
      const updatedTagList = [...recordTags, tag.trim()];
      setRecordTags(updatedTagList);
      onChange?.(updatedTagList);
      onAdd?.(tag);
      resetInput();
    } else {
      resetInput();
    }
  };

  const addTagButtonHandler = useCallback(async () => {
    if (tagInputValue.length > 0) {
      addTag(tagInputValue.trim());
    } else {
      await setAddTagMode(ADD_TAGS_MODE.ADD_TAG);
      tagInputRef.current?.focus();
    }
  }, [tagInputValue]);

  const keyUpHandler = async (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      resetInput();
      return;
    }

    if (!event.shiftKey && event.key === 'Enter' && tagInputValue.length > 0) {
      addTag(tagInputValue.trim());

      // If the user presses Enter, then reopen the cleared input
      // so that the user enters a different tag without pressing the plus button.
      if (tagInputRef.current?.textContent?.length) {
        await setAddTagMode(ADD_TAGS_MODE.ADD_TAG);
        tagInputRef.current.focus();
        tagInputRef.current.textContent = '';
        // We also need to clear the html content of the editable element as the browser adds some div and br tags.
        // For some reason we can't clear it on the main thread. Because of this, we push it off the stack to microtasks.
        setTimeout(() => {
          if (tagInputRef.current) {
            tagInputRef.current.innerHTML = '';
          }
        });
      }
    }
  };

  const onBlurHandler = () => {
    if (tagInputValue.trim().length > 0) {
      addTag(tagInputValue.trim());
    } else {
      resetInput();
    }
  };

  const onContentEditableChange = (event: ChangeEvent<HTMLDivElement>) => {
    const { textContent } = event.currentTarget;

    if ((textContent && textContent.length > maxLength) || !!recordTags.find((tag) => tag === textContent)) {
      setShowError(true);
    } else {
      setTagInputValue(textContent!);
      setShowError(false);
    }
  };

  const removeTagHandler = (tag: string) => {
    const filteredTagList = recordTags.filter((t) => t !== tag);
    setRecordTags(filteredTagList);
    onChange?.(filteredTagList);
    onRemove?.(tag);
  };

  useOutsideClick({
    ref: tagInputRef,
    // On mobile Safari, the onBlur event does not work as expected.
    // [Crutch] We can use the OutsideClick event as a replacement for the onBlur event.
    // https://stackoverflow.com/a/13492999
    handler: onBlurHandler,
  });

  // Focus on input on first render if default mode is ADD_TAG.
  useEffect(() => {
    if (isAddTagModeByDefault === ADD_TAGS_MODE.ADD_TAG) {
      tagInputRef?.current?.focus();
    }
  }, []);

  return (
    <Flex
      gap="8px"
      wrap="wrap"
      align="center"
    >
      {recordTags.map((tag) => (
        <Tag
          key={tag}
          as={motion.div}
          bgColor="fluentHealthSecondary.500"
          color="fluentHealthSecondary.100"
          fontWeight="400"
          py="6px"
          height="auto"
          initial="initial"
          whileHover="hover"
          fontSize="md"
          maxWidth="100px"
          overflow="hidden"
          {...tagProps}
        >
          <TagLabel
            cursor="default"
            lineHeight={1}
            display="inline-block"
            textOverflow="unset"
            overflowWrap="anywhere"
            overflow="unset"
          >
            {truncateLength(tag, 6)}
            {tag.length > 6 && '...'}
          </TagLabel>
          <BaseXButton
            ml="4px"
            width="20px"
            tagProps={tagProps}
            onClick={() => removeTagHandler(tag)}
          >
            <ClearIcon size={14} />
          </BaseXButton>
        </Tag>
      ))}
      {addTagMode === ADD_TAGS_MODE.ADD_TAG && (
        <Flex
          ref={tagInputRef}
          className="hide-scrollbar"
          contentEditable="true"
          onInput={onContentEditableChange}
          onKeyUp={keyUpHandler}
          onBlur={onBlurHandler}
          align="center"
          maxW="100%"
          height="auto"
          color="fluentHealthSecondary.100"
          bgColor="fluentHealthSecondary.500"
          px="8px"
          py="3px"
          borderRadius="md"
          border="1px solid transparent"
          borderColor={showError ? 'salmon.500' : 'transparent'}
          overflowWrap="anywhere"
          sx={{
            '& br': {
              display: 'none',
            },
            '& *': {
              display: 'inline',
              whiteSpace: 'nowrap',
            },
          }}
          {...inputProps}
        />
      )}
      {showAddButton && (
        <PlusCircleIcon
          size={18}
          color={theme.colors.fluentHealthSecondary[200]}
          cursor="pointer"
          onClick={addTagButtonHandler}
          {...addButtonProps}
        />
      )}
    </Flex>
  );
}
