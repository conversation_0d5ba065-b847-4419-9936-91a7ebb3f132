import { useState } from 'react';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Badge,
  Box,
  Flex,
  Icon,
  Text,
  useBreakpointValue,
  useDisclosure,
} from '@chakra-ui/react';
import { LockIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES, enumStatus } from '@user/lib/constants';
import { recordHealthRecordHubEvents, recordUploadRecordEvents } from '@user/lib/events-analytics-manager';

import { RejectedFileModal } from './components/add-mr-flow/RejectedFileModal';
import { MedicalRecord } from './lib/state';
import { useAnalyticsService } from '@lib/state';
import { PasswordProtectedModal } from './components/add-mr-flow/PasswordProtectedModal';
import { isEmailRecord } from './MedicalRecordInReviewList';

import { ReactComponent as Mail } from '@assets/icons/email-review.svg';
import { ReactComponent as Settings } from '@assets/icons/settings.svg';

interface MedicalRecordActionNeededListProps {
  medicalRecordListActionNeeded: any[];
  fetchMedicalRecord: () => void;
}

export function MedicalRecordActionNeededList({
  medicalRecordListActionNeeded,
  fetchMedicalRecord,
}: MedicalRecordActionNeededListProps) {
  const rejectedModal = useDisclosure();
  const passwordModal = useDisclosure();
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);
  const navigate = useNavigate();
  const { trackEventInFlow } = useAnalyticsService();
  const { DOCUMENTS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  // Responsive values for font and icon sizes
  const fontSize = useBreakpointValue({ base: '16px', md: '20px' });
  const titleFontSize = useBreakpointValue({ base: '16px', md: '20px' });
  const emailFontSize = useBreakpointValue({ base: '14px', md: '16px' });
  const iconSize = useBreakpointValue({ base: '64px', md: '68px' });

  const records: MedicalRecord[] = medicalRecordListActionNeeded.map((record: any) => {
    const doc = record?.docref;
    const title = doc?.content?.[0]?.attachment?.title || 'Unknown Document';
    const status = record.actionStatus;
    const encrypted = false;
    const message =
      status === enumStatus.REJECTED
        ? 'File rejected in review. Tap to know more and take action.'
        : 'File is encrypted. Tap to enter password and unlock.';

    return {
      id: doc?.id,
      doc,
      title,
      status,
      encrypted,
      type: isEmailRecord(doc) ? 'email' : 'others',
      message,
      task: doc?.task,
    };
  });

  const handleOpenModal = (record: MedicalRecord) => {
    setSelectedRecord(record);
    recordHealthRecordHubEvents(trackEventInFlow, {
      EventName: 'RejectedRecordInteracted',
      reason_of_rejection: record?.task?.output?.[0]?.type?.coding?.[0]?.display,
      document_id: record?.id,
    });
    rejectedModal.onOpen();
  };

  const handleOpenPasswordModal = (record: MedicalRecord) => {
    setSelectedRecord(record);
    recordHealthRecordHubEvents(trackEventInFlow, {
      EventName: 'EncryptedRecordInteracted',
      document_id: record?.id,
    });
    passwordModal.onOpen();
  };

  const handleReupload = async (record: MedicalRecord) => {
    recordUploadRecordEvents(trackEventInFlow, { EventName: 'UploadRecordStarted' });
    recordHealthRecordHubEvents(trackEventInFlow, {
      EventName: 'RejectedRecordReuploadInteracted',
    });
    localStorage.setItem('supersededRecordId', record.id);
    navigate(`/${DOCUMENTS}/${ADD}`);
    rejectedModal.onClose();
  };

  if (!records.length) return null;

  return (
    <Accordion
      allowToggle
      defaultIndex={[0]}
      borderRadius="12px"
      background="#FFF"
      mt="24px"
    >
      <AccordionItem border="none">
        {({ isExpanded }) => (
          <>
            {/* Header Section */}
            <AccordionButton
              onClick={() => {
                recordHealthRecordHubEvents(trackEventInFlow, {
                  EventName: 'HealthRecordHubInteracted',
                  hrh_tab_name: 'action needed',
                });
              }}
              padding="10px 16px"
              borderRadius="12px"
              background="iris.100"
              color="iris.600"
              fontSize={fontSize}
              fontWeight="400"
              lineHeight="28px"
              letterSpacing="-0.4px"
              _hover={{ background: 'iris.100' }}
            >
              <Box
                flex="1"
                textAlign="left"
              >
                Action Needed ({records.length})
              </Box>
              <AccordionIcon
                width="24px"
                height="24px"
                transform={isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
              />
            </AccordionButton>

            {/* Records Section */}
            <AccordionPanel padding="12px">
              {records.map((record, index) => (
                <Flex
                  key={record.id}
                  direction="column"
                  marginBottom={index !== records.length - 1 ? '20px' : '0px'}
                  cursor="pointer"
                  onClick={() =>
                    record.status === enumStatus.REJECTED ? handleOpenModal(record) : handleOpenPasswordModal(record)
                  }
                >
                  <Box
                    border="1px solid"
                    borderColor="red.100"
                    borderRadius="12px"
                    padding="12px"
                    background="beige.100"
                  >
                    <Flex align="center">
                      <Flex align="center">
                        <Box
                          flexShrink={0}
                          width={iconSize}
                          height={iconSize}
                          display="flex"
                          alignItems="center"
                        >
                          {record.type === 'others' ? (
                            <Settings
                              width="100%"
                              height="100%"
                            />
                          ) : (
                            <Mail
                              width="100%"
                              height="100%"
                            />
                          )}
                        </Box>
                        <Text
                          fontSize={titleFontSize}
                          fontWeight="400"
                          ml="16px"
                          mr="16px"
                          lineHeight="28px"
                          letterSpacing="-0.48px"
                          color="gray.500"
                          wordBreak="break-word"
                          whiteSpace="normal"
                          overflowWrap="break-word"
                        >
                          {record.title}
                        </Text>
                      </Flex>
                      <Badge
                        background="papaya.100"
                        color="red.100"
                        borderRadius="8px"
                        padding="4px 8px"
                        fontSize="12px"
                      >
                        {record.status}
                      </Badge>
                    </Flex>
                  </Box>
                  {record.message && (
                    <Flex
                      marginTop="4px"
                      align="center"
                      color="red.100"
                    >
                      {record.status !== enumStatus.REJECTED && (
                        <Icon
                          as={LockIcon}
                          width="14px"
                          height="14px"
                          marginRight="4px"
                        />
                      )}
                      <Flex
                        align="center"
                        color="red.100"
                        marginTop="2px"
                        fontSize={emailFontSize}
                      >
                        {record.message}
                      </Flex>
                    </Flex>
                  )}
                </Flex>
              ))}
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
      {selectedRecord && (
        <>
          <RejectedFileModal
            isOpen={rejectedModal.isOpen}
            onClose={rejectedModal.onClose}
            onReupload={handleReupload}
            selectedRecord={selectedRecord}
          />
          <PasswordProtectedModal
            isOpen={passwordModal.isOpen}
            onClose={passwordModal.onClose}
            selectedRecord={selectedRecord}
            fetchMedicalRecordList={fetchMedicalRecord}
          />
        </>
      )}
    </Accordion>
  );
}
