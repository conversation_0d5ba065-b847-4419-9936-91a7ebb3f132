// Package modules
import { Box, Container } from '@chakra-ui/react';

import { MedicalRecordList } from './MedicalRecordList';
import UploadStatusBar from './components/add-mr-flow/UploadStatusBar';
import { useFileUploadStore } from './lib/uploadState';

export function MedicalRecordsPage() {
  const { files } = useFileUploadStore();

  // Convert object to array
  const fileEntries = Object.values(files);

  // Calculate total files
  const total = fileEntries.length;

  // Count uploaded, failed, and success files
  const uploaded = fileEntries.filter(({ progress }) => progress === 100).length;
  const failed = fileEntries.filter(({ error }) => error).length;

  // Determine the overall status
  let status: 'uploading' | 'failed' | 'success' = 'success';

  if (failed > 0) {
    status = 'failed';
  } else if (uploaded < total) {
    status = 'uploading';
  }

  return (
    <>
      <Box py="5">
        {/* <HalfCircleWaveDecoration />
      <BigCircleDecoration />
      <Container
        position="relative"
        zIndex={1}
      >
        <QuizBannerSlider />
      </Container> */}
        <Container
          position="relative"
          zIndex={1}
          padding={{ base: '0px', md: '16px' }}
        >
          <MedicalRecordList />
        </Container>
      </Box>
      {total !== 0 && (
        <UploadStatusBar
          status={status}
          uploaded={uploaded}
          total={total}
        />
      )}
    </>
  );
}
