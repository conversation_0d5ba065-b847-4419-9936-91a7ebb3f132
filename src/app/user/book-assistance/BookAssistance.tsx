import { Box, Fade } from '@chakra-ui/react';

import { BOOK_ASSISTANCE_STEPS } from './models';
import { PersonalDetailsStep } from './components/PersonalDetailsStep';
import { ConfirmationStep } from './components/ConfirmationStep';
import { useStepper } from 'src/app/medical-records/lib/utils';

function BookAssistance() {
  const stepper = useStepper(Object.keys(BOOK_ASSISTANCE_STEPS).length, {});

  return (
    <Box>
      {stepper.currentStep === BOOK_ASSISTANCE_STEPS.PERSONAL_DETAILS && (
        <Fade in>
          <PersonalDetailsStep goToNextStep={stepper.goToNextStep} />
        </Fade>
      )}
      {stepper.currentStep === BOOK_ASSISTANCE_STEPS.CONFIRMATION && (
        <Fade in>
          <ConfirmationStep />
        </Fade>
      )}
    </Box>
  );
}

export default BookAssistance;
