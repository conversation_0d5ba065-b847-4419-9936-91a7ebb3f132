import { Button, <PERSON>lex, <PERSON><PERSON>, <PERSON>, Text } from '@chakra-ui/react';
import { NavLink } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { useAnalyticsService } from '@lib/state';
import {
  AnalyticsEventName,
  EventClickedElement,
  EventFlowNames,
  EventItemText,
  EventPropsNames,
} from '@lib/analyticsService';

import { ReactComponent as BookAssistanceConfirmationBackground } from '@assets/objects/book-assistance-confirmation-background.svg';
import { ReactComponent as BookAssistancePatternMobile } from '@assets/objects/book-assistance-mobile.svg';

export function ConfirmationStep() {
  const { trackEvent } = useAnalyticsService();
  const onTrackEvent = () => {
    trackEvent(AnalyticsEventName.DashboardItemInteracted, {
      [EventPropsNames.FlowName]: EventFlowNames.Dashboard,
      [EventPropsNames.ClickedElement]: EventClickedElement.CTA,
      [EventPropsNames.ClickedItem]: EventItemText.BackToHome,
    });
  };

  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  return (
    <Flex
      height="100vh"
      align="center"
      justify="center"
      mx={{ base: '16px', md: 'auto' }}
    >
      <Flex
        width="1040px"
        bg="salmon.500"
        borderRadius="40px"
        overflow="hidden"
        position="relative"
        py={{ base: '67px', md: '0' }}
        justifyContent="center"
      >
        <Flex display={{ base: 'flex', md: 'none' }}>
          <BookAssistancePatternMobile
            style={{
              position: 'absolute',
              inset: 0,
            }}
          />
        </Flex>
        <Flex display={{ base: 'none', md: 'flex' }}>
          <BookAssistanceConfirmationBackground
            style={{
              position: 'absolute',
              inset: 0,
            }}
          />
        </Flex>
        <Flex
          maxWidth={{ base: 'auto', md: '580px' }}
          gap={{ base: '32px', md: '40px' }}
          bg="salmon.500"
          direction="column"
          alignItems="center"
          py="125px"
          px={{ base: '20px', md: '90px' }}
          zIndex={2}
        >
          <Heading
            color="white"
            textAlign="center"
            maxWidth="400px"
          >
            Thank you for booking our asistance service!
          </Heading>
          <Text
            fontSize="lg"
            color="white"
            textAlign="center"
            maxWidth="380px"
          >
            Request was sent successfully. Our team will get back to you shortly.
          </Text>
          <Link
            as={NavLink}
            to={`/${DASHBOARD}/${VIEW}`}
          >
            <Button
              variant="ghost"
              bg="white"
              textDecoration="none"
              color="iris.500"
              fontSize="lg"
              py="16px"
              onClick={onTrackEvent}
            >
              Back To Home
            </Button>
          </Link>
        </Flex>
      </Flex>
    </Flex>
  );
}
