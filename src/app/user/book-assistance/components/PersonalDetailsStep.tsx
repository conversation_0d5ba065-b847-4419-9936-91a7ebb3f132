import {
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  GridItem,
  Heading,
  IconButton,
  Input,
  Link,
  Text,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { NavLink } from 'react-router-dom';
import { X as CloseIcon } from 'react-feather';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { PatientTelecom } from '@lib/models/patient';
import { IPersonalDetailsStep } from '../models';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import {
  AnalyticsEventName,
  AnalyticsFlow,
  EventClickedElement,
  EventFlowNames,
  EventItemText,
  EventPropsNames,
} from '@lib/analyticsService';

import { ReactComponent as BookAssistancePattern } from '@assets/objects/book-assistance.svg';
import { ReactComponent as BookAssistancePatternMobile } from '@assets/objects/book-assistance-mobile.svg';
import { ReactComponent as FluentHealthLogo } from '@assets/icons/fh-logo.svg';
import { ReactComponent as BookingScreenRightDecoration } from '@assets/objects/booking-screen-right-decoration.svg';
import { ReactComponent as BookingScreenLeftDecoration } from '@assets/objects/booking-screen-left-decoration.svg';
import { ReactComponent as BookingScreenBackground } from '@assets/objects/booking-screen-background.svg';

type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
};

export function PersonalDetailsStep({ goToNextStep }: IPersonalDetailsStep) {
  const { authenticatedUser } = useAuthService();
  const isMobile = useIsMobile();
  const { trackEvent, trackEventInFlow } = useAnalyticsService();
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  // eslint-disable-next-line no-nested-ternary
  const patientName = authenticatedUser?.name?.length
    ? authenticatedUser.name[0].text?.length
      ? authenticatedUser.name[0].text
      : authenticatedUser.name[0].given!.join(' ')
    : 'No name';

  const patientEmail = authenticatedUser?.telecom
    ? authenticatedUser.telecom.find((telecom: PatientTelecom) => telecom.system === 'email')?.value
    : 'No email';

  const patientPhoneNumber = authenticatedUser?.telecom
    ? authenticatedUser.telecom.find(
        (telecom: PatientTelecom) => telecom.system === 'phone' && telecom.use === 'mobile'
      )!.value
    : 'No phone number';

  const [firstName = '', lastName = ''] = patientName.split(' ');

  const form = useForm({
    mode: 'onChange',
    defaultValues: {
      firstName,
      lastName,
      email: patientEmail,
      phoneNumber: patientPhoneNumber,
    },
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1).max(40).optional(),
        lastName: z.string().min(1).max(40).optional(),
        email: z.string().email('Invalid email'),
        phoneNumber: z.string().min(1),
      })
    ),
  });

  const onSubmit = (formValues: FormValues) => {
    // TODO: Add assistance booking logic here
    goToNextStep();
    trackEvent(AnalyticsEventName.DashboardItemInteracted, {
      [EventPropsNames.FlowName]: EventFlowNames.Dashboard,
      [EventPropsNames.ClickedElement]: EventClickedElement.Button,
      [EventPropsNames.ClickedItem]: EventItemText.RequestAssistance,
    });
    trackEventInFlow(AnalyticsFlow.BookOffline, AnalyticsEventName.BookOfflineCompleted, {
      [EventPropsNames.FlowName]: EventFlowNames.BookAssistance,
      [EventPropsNames.Name]: `${formValues.firstName} ${formValues.lastName}`,
      [EventPropsNames.Email]: formValues.email,
      [EventPropsNames.PhoneNumber]: formValues.phoneNumber,
    });
  };

  return (
    <>
      <Link
        as={NavLink}
        to={`/${DASHBOARD}/${VIEW}`}
      >
        <IconButton
          aria-label="Close modal"
          zIndex="var(--chakra-zIndices-popover)"
          position="fixed"
          top="42px"
          right="52px"
          width="40px"
          minW="unset"
          height="40px"
          borderRadius="full"
          bgColor="transparent"
          color="fluentHealthText.100"
          opacity={0.6}
          _hover={{
            bgColor: 'white',
            opacity: 1,
          }}
          icon={
            <CloseIcon
              size={36}
              strokeWidth={1}
            />
          }
        />
      </Link>

      {!isMobile && (
        <>
          <BookingScreenRightDecoration
            style={{
              position: 'absolute',
              right: 0,
              top: '120px',
            }}
          />
          <BookingScreenLeftDecoration
            style={{
              position: 'absolute',
              left: 0,
              top: '600px',
            }}
          />
          <BookingScreenBackground
            style={{
              position: 'absolute',
              top: 0,
              left: '50%',
              transform: 'translate(-50%, 0)',
              zIndex: 1,
            }}
          />
        </>
      )}
      <Flex
        mx={{ base: '16px', xl: 'auto' }}
        direction="column"
        alignItems="center"
        gap={{ base: '40px', xl: '60px' }}
        mt="32px"
        zIndex="2"
      >
        <FluentHealthLogo width="100px" />
        <Flex
          direction="column"
          alignItems="center"
          gap="32px"
        >
          <Heading
            mt="28px"
            textAlign={{ base: 'center', xl: 'start' }}
          >
            Book our assistance service
          </Heading>
          <Text
            maxWidth="720px"
            textAlign="center"
            fontSize="lg"
          >
            Sit back and let our team do all the work for you. If you feel like you have to many records laying around
            or you just don&apos;t have the time, our assistance service is precisely what you need.
          </Text>
        </Flex>
        <Flex px={{ xl: '300px' }}>
          <Grid
            templateColumns={{ base: 'repeat(1, 1fr)', xl: 'repeat(2, 1fr)' }}
            borderRadius="40px"
            overflow="hidden"
            bg="salmon.500"
            py={{ base: '67px', xl: '0' }}
            my={{ base: '24px', xl: 'auto' }}
            position="relative"
            maxWidth={{ base: '716px', xl: '100%' }}
            zIndex={2}
          >
            <GridItem display={{ base: 'block', xl: 'none' }}>
              <BookAssistancePatternMobile
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  zIndex: 1,
                }}
              />
              <Flex display={{ base: 'none', md: 'block' }}>
                <BookAssistancePatternMobile
                  style={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    zIndex: 1,
                    transform: 'scaleX(-1)',
                  }}
                />
              </Flex>
            </GridItem>
            <GridItem display={{ base: 'none', xl: 'block' }}>
              <BookAssistancePattern style={{ flex: 1, flexShrink: 0 }} />
            </GridItem>
            <Flex
              as={GridItem}
              bg="#FFF0DA"
              direction="column"
              padding={{ base: '40px 20px', xl: '35px 52px' }}
              width={{ base: 'auto', xl: '520px' }}
              gap="40px"
              zIndex={2}
            >
              <Text
                fontSize="lg"
                textAlign="center"
              >
                Add your details bellow and our team will contact you via Whatsapp to schedule an appointment.
              </Text>
              <Flex
                direction="column"
                gap="62px"
                height="100%"
              >
                <Flex gap="16px">
                  <FormControl
                    variant="floating"
                    isInvalid={!!form.formState.errors.firstName}
                  >
                    <Input
                      placeholder=" "
                      id="name"
                      {...form.register('firstName')}
                    />
                    <FormLabel>First name</FormLabel>
                    <FormErrorMessage>Name is required</FormErrorMessage>
                  </FormControl>
                  <FormControl
                    variant="floating"
                    isInvalid={!!form.formState.errors.lastName}
                  >
                    <Input
                      placeholder=" "
                      id="name"
                      {...form.register('lastName')}
                    />
                    <FormLabel>Last name</FormLabel>
                    <FormErrorMessage>Name is required</FormErrorMessage>
                  </FormControl>
                </Flex>

                <FormControl
                  variant="floating"
                  isInvalid={!!form.formState.errors.email}
                >
                  <Input
                    placeholder=" "
                    id="email"
                    type="email"
                    {...form.register('email')}
                  />
                  <FormLabel>Email</FormLabel>
                  <FormErrorMessage>Email is required</FormErrorMessage>
                </FormControl>
                <FormControl
                  variant="floating"
                  isInvalid={!!form.formState.errors.phoneNumber}
                >
                  <Input
                    placeholder=" "
                    id="phoneNumber"
                    {...form.register('phoneNumber')}
                  />
                  <FormLabel>Phone number</FormLabel>
                  <FormErrorMessage>Phone number is required</FormErrorMessage>
                </FormControl>
              </Flex>

              <Button
                py="17px"
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                isDisabled={!form.formState.isValid}
              >
                Request assistance
              </Button>
            </Flex>
          </Grid>
        </Flex>
        <Text
          maxWidth="720px"
          textAlign="center"
          fontSize="lg"
          opacity="70%"
          mb={{ base: '80px', lg: '147px' }}
        >
          Donec sed ipsum erat. Integer quis ligula blandit, tempus ex vel, pharetra sem. Etiam porttitor libero purus,
          non pulvinar nunc congue at. Integer hendrerit leo lorem, non mollis eros condimentum dictum. Phasellus mollis
          orci a nulla egestas, sit amet vulputate metus egestas. Praesent sit amet velit sit amet nisi elementum
          volutpat.
        </Text>
      </Flex>
    </>
  );
}
