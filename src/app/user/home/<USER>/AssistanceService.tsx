import { Box, Container, Flex, Heading, Text, useTheme } from '@chakra-ui/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode, Mousewheel } from 'swiper';
import { useReadLocalStorage } from 'usehooks-ts';

import { usePatient } from '../../lib/medplum-state';
import { useAuthService } from '@lib/state';
import { LOCAL_STORAGE_KEYS } from '@lib/constants';

import { ReactComponent as HomePageHelpfulTipsLeftDecoration } from '@assets/objects/home-page-helpful-tips-left-decoration.svg';
import { ReactComponent as HomePageLeftDecoration } from '@assets/objects/home-page-left-decoration.svg';
import { ReactComponent as AssistanceServiceCirclePattern } from '@assets/objects/assistance-service-circle-pattern.svg';
// import { ReactComponent as AssistanceServiceGreenCirclePattern } from '@assets/objects/assistance-service-green-circle-pattern.svg';
import { ReactComponent as AssistanceServiceStarPattern } from '@assets/objects/assistance-service-star-pattern.svg';
import { ReactComponent as AssistanceServiceTriangularPattern } from '@assets/objects/assistance-service-triangular-pattern.svg';

// import { ReactComponent as AssistanceServiceHeaderIcon } from '@assets/objects/assistance-service-header-icon.svg';

const newUserSteps = [
  {
    id: 1,
    text: (
      <>
        <b>Send your reports</b> to this Fluent email to be converted into health records and added to your health
        profile.
      </>
    ),
    bgColor: '#FFF2DF',
    colorName: 'fluentHealthComplementary.Salmon3',
    asset: (
      <AssistanceServiceCirclePattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
        }}
      />
    ),
  },
  {
    id: 2,
    text: (
      <>
        <b>Ask your clinics and doctors</b> to directly send your records to this Fluent email so it can be added to
        your health profile.
      </>
    ),
    bgColor: '#FFE5DA',
    colorName: 'salmon.500',
    asset: (
      <AssistanceServiceStarPattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
          stroke: '#FF824D',
        }}
      />
    ),
  },
  {
    id: 3,
    text: (
      <>
        This Fluent email is only a forwarding address for your records and <b>NOT an actual email account</b> that can
        be accessed.
      </>
    ),
    bgColor: '#D6E8FF',
    colorName: 'royalBlue.500',
    asset: (
      <AssistanceServiceTriangularPattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
        }}
      />
    ),
  },
  // {
  //   id: 4,
  //   text: 'You get notified when your records are ready',
  //   bgColor: '#A4E3BD',
  //   colorName: 'seaGreen.500',
  //   asset: (
  //     <AssistanceServiceGreenCirclePattern
  //       style={{
  //         position: 'absolute',
  //         top: 0,
  //         right: 0,
  //       }}
  //     />
  //   ),
  // },
];

const steps = [
  {
    id: 1,
    text: <>Share your Fluent email with clinics and doctors to easily import your health records.</>,
    bgColor: '#FFF2DF',
    colorName: 'fluentHealthComplementary.Salmon3',
    asset: (
      <AssistanceServiceCirclePattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
        }}
      />
    ),
  },
  {
    id: 2,
    text: <>Attachments sent to this email will be converted into digital records and added to your health profile.</>,
    bgColor: '#FFE5DA',
    colorName: 'salmon.500',
    asset: (
      <AssistanceServiceStarPattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
          stroke: '#FF824D',
        }}
      />
    ),
  },
  {
    id: 3,
    text: <>This email is only a forwarding address and not an actual email account that can be accessed.</>,
    bgColor: '#D6E8FF',
    colorName: 'royalBlue.500',
    asset: (
      <AssistanceServiceTriangularPattern
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
        }}
      />
    ),
  },
  // {
  //   id: 4,
  //   text: 'You get notified when your records are ready',
  //   bgColor: '#A4E3BD',
  //   colorName: 'seaGreen.500',
  //   asset: (
  //     <AssistanceServiceGreenCirclePattern
  //       style={{
  //         position: 'absolute',
  //         top: 0,
  //         right: 0,
  //       }}
  //     />
  //   ),
  // },
];

interface IStepCard {
  id: number;
  text: React.ReactNode;
  asset: React.ReactNode;
  bgColor?: string;
  colorName: string;
}
function StepCard({ id, text, asset, bgColor, colorName }: IStepCard) {
  const theme = useTheme();
  const colorNameParts = colorName.split('.');
  // Use reduce to access the nested property in the theme
  const color = colorNameParts.reduce((themeObj, key) => themeObj[key], theme.colors);

  return (
    <Flex
      alignItems="center"
      ml={steps.at(0)!.id === id ? { base: '24px', md: 0 } : 0}
      mr={steps.at(-1)!.id === id ? { base: '24px', md: 0 } : 0}
    >
      <Flex
        bg={bgColor || 'white'}
        maxW={{ base: '280px', md: '360px' }}
        minW={{ base: 'auto', md: '360px' }}
        height={{ base: '360px', md: '440px' }}
        padding="24px 32px"
        direction="column"
        alignItems="start"
        gap="24px"
        position="relative"
        borderRadius="40px"
        overflow="hidden"
        display="flex"
        justifyContent="flex-end"
      >
        {asset}
        <Flex
          display="flex"
          direction="column"
          justifyContent="flex-start"
          minHeight="40%"
          gap="1rem"
          marginBottom={{ base: '16px', md: '0' }}
        >
          <Heading
            fontSize="28px"
            color="gray.500"
            borderRadius="50%"
            border="1px solid"
            borderColor={color}
            width="60px"
            height="60px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            {id}
          </Heading>
          <Text
            fontSize="20px"
            height="90px"
            marginBottom={{ base: '24px' }}
          >
            {text}
          </Text>
        </Flex>
      </Flex>
      {id !== steps.length && (
        <Flex
          width="40px"
          height="0px"
        />
      )}
    </Flex>
  );
}

export function AssistanceService() {
  const { authenticatedUser } = useAuthService();
  const { patient } = usePatient(authenticatedUser?.id);
  const patientProgress = useReadLocalStorage<any>(`[${LOCAL_STORAGE_KEYS.PATIENT_PROGRESS}, ${patient.id}]`);

  const profileProgress = patientProgress
    ? Object.values(patientProgress).reduce<number>((total, value: any) => total + value, 0)
    : 0;

  const newUserFlag = !(patient?.recordsInReview?.length || patient?.reminders?.length || profileProgress);

  return (
    <Container
      maxWidth="100%"
      px={0}
      position="relative"
      overflow="visible"
    >
      <Box display={{ base: 'none', md: 'block' }}>
        <HomePageHelpfulTipsLeftDecoration
          style={{
            position: 'absolute',
            left: 0,
            top: '-650px',
            zIndex: 0,
          }}
        />
        <HomePageLeftDecoration
          style={{
            position: 'absolute',
            left: 0,
            top: '50px',
            zIndex: 0,
          }}
        />
      </Box>
      <Flex
        direction="column"
        mt={{ base: '80px' }}
        gap="60px"
      >
        <Flex
          direction="column"
          alignItems="center"
          gap="30px"
        >
          <Heading
            textAlign="center"
            maxWidth="584px"
          >
            {newUserFlag
              ? 'Ways in which your Fluent forwarding email address works'
              : 'How does your Fluent forwarding email address work?'}
          </Heading>
        </Flex>
        <Flex width="100%">
          <Swiper
            slidesPerView="auto"
            freeMode
            mousewheel
            modules={[Mousewheel, FreeMode]}
          >
            {newUserFlag
              ? newUserSteps.map((step) => (
                  <SwiperSlide
                    key={step.id}
                    style={{ width: 'auto' }}
                  >
                    <StepCard
                      id={step.id}
                      text={step.text}
                      asset={step.asset}
                      bgColor={step.bgColor}
                      colorName={step.colorName}
                    />
                  </SwiperSlide>
                ))
              : steps.map((step) => (
                  <SwiperSlide
                    key={step.id}
                    style={{ width: 'auto' }}
                  >
                    <StepCard
                      id={step.id}
                      text={step.text}
                      asset={step.asset}
                      bgColor={step.bgColor}
                      colorName={step.colorName}
                    />
                  </SwiperSlide>
                ))}
          </Swiper>
        </Flex>
      </Flex>
    </Container>
  );
}
