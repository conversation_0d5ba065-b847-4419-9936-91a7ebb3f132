import { Container, Flex, Heading, Text, useClipboard, useDisclosure } from '@chakra-ui/react';
import { Copy as CopyIcon } from 'react-feather';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { InfoModal } from 'src/components/InfoModal';
import { useAuthService } from '@lib/state';

import { ReactComponent as EmailCardFolder } from '@assets/icons/email-icon.svg';
import { ReactComponent as EmailCardPattern } from '@assets/objects/email-card-pattern.svg';

export function EmailCard() {
  const isMobile = useIsMobile();
  const infoModal = useDisclosure();
  const { authenticatedUser } = useAuthService();
  const { onCopy, hasCopied } = useClipboard(
    authenticatedUser?.gatewayUser?.additionalInformation?.mg_route_email || ''
  );

  return (
    <Container
      maxWidth="1240px"
      zIndex="1"
    >
      <Flex
        mt="100px"
        borderRadius="16px"
        bg="iris.500"
        gap="24px"
        width="100%"
        position="relative"
        overflow="hidden"
      >
        {!isMobile && (
          <EmailCardPattern
            style={{
              position: 'absolute',
              top: 0,
              right: -120,
              zIndex: 0,
            }}
            height="100%"
          />
        )}
        <Flex
          direction="column"
          alignItems="start"
          zIndex="1"
          px={{ base: '24px', md: '64px' }}
          py={{ base: '24px', md: '62px' }}
          bg="linear-gradient(90deg, #4956E4 51.9%, rgba(73, 86, 228, 0) 95.88%)"
          w={isMobile ? '100%' : '90%'}
        >
          <Heading
            color="white"
            maxWidth="850px"
            fontSize="32px"
          >
            No time to organise your records? Add them easily using your personalised Fluent forwarding email address:
          </Heading>
          <InfoModal
            title="How does it work?"
            isCentered
            description={
              <>
                <Text marginBottom="20px">Your Fluent email address exists to protect your privacy and security.</Text>
                <Text marginBottom="20px">
                  Save your unique mail address in your contact book to access later. Any attachment sent here will be
                  turned into digital records for you.
                </Text>
                <Text>
                  Next time you provide your email address to a hospital, clinic, or lab, share your Fluent email
                  address for swift import of your health records.
                </Text>
              </>
            }
            onSecondaryButtonClick={infoModal.onClose}
            {...infoModal}
          />
          {/* <Flex
            gap="8px"
            alignItems="center"
            cursor="pointer"
            mt={{ base: '24px', md: '40px' }}
            onClick={infoModal.onOpen}
          >
            <Text color="white">How does it work?</Text>
            <InfoIcon
              size={14}
              stroke="white"
            />
          </Flex> */}
          <Flex
            gap="40px"
            alignItems={{ base: 'start', md: 'center' }}
            direction={{ base: 'column-reverse', md: 'row' }}
            mt={{ base: '32px', md: '40px' }}
          >
            <Flex
              position="relative"
              minHeight="64px"
              minWidth="50px"
            >
              <EmailCardFolder />
            </Flex>
            <Flex
              background="white"
              borderRadius="16px"
              py="12px"
              px="16px"
              marginTop="auto"
              mt={{ base: '8px', md: 0 }}
              gap={{ base: '16px' }}
            >
              <Text
                color="iris.500"
                wordBreak="break-all"
              >
                {authenticatedUser?.gatewayUser?.additionalInformation?.mg_route_email}
              </Text>
              <Flex
                gap="6px"
                alignItems="center"
                cursor="pointer"
                userSelect="none"
                onClick={onCopy}
                color="iris.500"
              >
                {!isMobile && <Text fontWeight="500">{hasCopied ? 'Copied !' : 'Copy'}</Text>}
                {!hasCopied && <CopyIcon size={18} />}
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Container>
  );
}
