import { But<PERSON>, Container, Flex, Heading, Image, Link, Text } from '@chakra-ui/react';
import { NavLink } from 'react-router-dom';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { useAnalyticsService } from '@lib/state';
import {
  AnalyticsEventName,
  AnalyticsFlow,
  EventClickedElement,
  EventFlowNames,
  EventItemText,
  EventPropsNames,
} from '@lib/analyticsService';

import { ReactComponent as BookAssistanceBackgroundCircle } from '@assets/objects/book-assistance-background-circle.svg';
import { ReactComponent as BookAssistanceBottomLeftDecoration } from '@assets/objects/book-assistance-bottom-left-decoration.svg';

export function BookAssistance() {
  const isMobile = useIsMobile();
  const { trackEvent, trackEventInFlow } = useAnalyticsService();
  const onTrackEvent = () => {
    trackEvent(AnalyticsEventName.DashboardItemInteracted, {
      [EventPropsNames.FlowName]: EventFlowNames.Dashboard,
      [EventPropsNames.ClickedElement]: EventClickedElement.Button,
      [EventPropsNames.ClickedItem]: EventItemText.BookAssistance,
    });

    trackEventInFlow(AnalyticsFlow.BookOffline, AnalyticsEventName.BookOfflineStarted, {
      [EventPropsNames.FlowName]: EventFlowNames.BookAssistance,
    });
  };

  return (
    <Flex
      position="relative"
      width="100%"
      direction="column"
      gap="32px"
    >
      <BookAssistanceBackgroundCircle
        style={{
          position: 'absolute',
          top: isMobile ? -150 : 100,
          zIndex: 1,
          left: isMobile ? -450 : 0,
          transform: isMobile ? 'scale(0.5)' : 'none',
        }}
      />
      {!isMobile && (
        <BookAssistanceBottomLeftDecoration
          style={{
            position: 'absolute',
            left: 0,
            bottom: -250,
          }}
        />
      )}
      <Image
        src="home/book-assistance-cover.png"
        height={{ base: 'auto', md: '550px' }}
        zIndex="1"
        position={{ base: 'static', md: 'absolute' }}
        right="0"
        top="200px"
        mt={{ base: '100px', md: 0 }}
        transform={{ base: 'scale(1.2)', md: 'none' }}
      />
      <Container maxWidth="1240px">
        <Flex
          alignItems="center"
          mt={{ base: '32px', md: '330px' }}
          gap="100px"
          position="relative"
        >
          <Flex
            direction="column"
            gap="32px"
            maxWidth="475px"
            zIndex="1"
          >
            <Heading fontSize={{ base: '28px', md: '36px' }}>
              Have too many <i>Health Records</i> lying around? <br />
              We can handle that for you.
            </Heading>
            <Flex
              direction="column"
              gap="48px"
              alignItems="start"
            >
              <Text fontSize="lg">
                When you&apos;re drowning in records or short on time, reach out, book an appointment, and let our crew
                come to the rescue.
              </Text>

              <Link
                as={NavLink}
                to="/book-assistance"
              >
                <Button
                  zIndex="2"
                  height="50px"
                  onClick={onTrackEvent}
                >
                  Book an appointment
                </Button>
              </Link>
            </Flex>
          </Flex>
        </Flex>
      </Container>
    </Flex>
  );
}
