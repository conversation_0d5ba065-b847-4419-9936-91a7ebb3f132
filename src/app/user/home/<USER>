// Package modules
import { useEffect, useState } from 'react';
import { Flex } from '@chakra-ui/react';

// Local modules
import { EmailCard } from './components/EmailCard';
import { PatientCard } from './components/PatientCard';
// import { HelpfulTips } from './components/HelpfulTips/HelpfulTips';
import { Summary } from './components/Summary';
// import { AssistanceService } from './components/AssistanceService';
// import { BookAssistance } from './components/BookAssistance/BookAssistance';
import { SummaryCardType } from '../lib/constants';
import { AnimatedGradient, BG_VARIANTS_HOME_PAGE } from './components/AnimatedGradient';
import UploadDocsBanner from './UploadDocsBanner';
import DownloadAppBanner from './DownloadAppBanner';
import { AssistanceService } from './components/AssistanceService';
import 'swiper/css';
import { AnalyticsEventName, EventPropsNames } from '@lib/analyticsService';
import { useAnalyticsService } from '@lib/state';

function HomePage() {
  const [hoverVariant, setHoverVariant] = useState<SummaryCardType | null>(null);
  const { trackEvent } = useAnalyticsService();

  useEffect(() => {
    trackEvent(AnalyticsEventName.ScreenOpened, {
      [EventPropsNames.ScreenName]: 'Dashboard',
    });
  }, []);
  return (
    <Flex
      position="relative"
      direction="column"
      alignItems="center"
      // pb="200px"
      bgImage="linear-gradient(180deg, #FFF2DF 0%, #DADCFF 45.8%, #DADCDD 54.81%, #FFF2DF 100%)"
    >
      <AnimatedGradient
        variant={hoverVariant}
        backgroundVariants={BG_VARIANTS_HOME_PAGE}
        top={{ base: '-64px', md: 0 }}
        left="0"
        width="100%"
        height="1024px"
        userSelect="none"
        pointerEvents="none"
      />
      <PatientCard variant={hoverVariant} />
      <Summary
        hoverType={hoverVariant}
        setHoverType={setHoverVariant}
      />
      {/* <BookAssistance /> */}
      <UploadDocsBanner />
      <EmailCard />
      <AssistanceService />
      <DownloadAppBanner />
      {/* TODO: hide recommendation teaser card for launch as it's not in scope */}
      {/* <HelpfulTips /> */}
    </Flex>
  );
}

export default HomePage;
