import { Box, Container } from '@chakra-ui/react';
import { ImageTitleBanner } from '@components/ImageTitleBanner';
import { waitlistUrl } from '@user/lib/constants';

import { ReactComponent as HomePageHelpfulTipsRightDecoration } from '@assets/objects/home-page-helpful-tips-right-decoration.svg';

function DownloadAppBanner() {
  return (
    <Container
      paddingInlineStart="0 !important"
      paddingInlineEnd="0 !important"
      padding={{ base: '4rem 4rem 4rem 0' }}
      margin="0px"
      marginInlineStart="0px"
      marginInlineEnd="0px"
      maxWidth="100%"
      position="relative"
      minHeight={{ md: '750px' }}
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <ImageTitleBanner
        title="Struggling to manage your paper records?"
        description={
          <p>
            Sit back and relax while our team digitises your entire health history in one go. No scanning, no typing, no
            hassle!
          </p>
        }
        image="/download-app-cover.png" // TODO - Check it's come's from cms
        cta={{
          text: 'Join our waitlist',
          href: waitlistUrl,
          isExternal: true,
        }}
        mainContainerStyles={{
          position: 'static',
          maxWidth: '1200px',
          justifyContent: 'flex-end',
        }}
        containerStyles={{
          width: { md: 'max-content' },
          display: { md: 'flex' },
          justifyContent: { md: 'flex-end' },
          margin: 0,
        }}
        imageStyles={{
          position: { base: 'static', md: 'absolute' },
          top: 100,
          left: 0,
        }}
      />
      <Box display={{ base: 'none', md: 'block' }}>
        <HomePageHelpfulTipsRightDecoration
          style={{
            position: 'absolute',
            right: 0,
            top: 60,
          }}
        />
      </Box>
    </Container>
  );
}

export default DownloadAppBanner;
