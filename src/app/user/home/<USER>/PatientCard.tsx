import { Avatar, Flex, Heading } from '@chakra-ui/react';
import { NavLink } from 'react-router-dom';
import { recordProfileEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { usePatient } from '../../lib/medplum-state';
import { AnimatedGradient, BG_VARIANTS_AVATAR } from './AnimatedGradient';

import { ReactComponent as HomeAvatarCover } from '@assets/objects/home-avatar-cover.svg';

export function PatientCard({ variant }: { variant: string | null }) {
  const { authenticatedUser } = useAuthService();
  const { patient } = usePatient(authenticatedUser?.id);
  const isMobile = useIsMobile();
  const { trackEventInFlow } = useAnalyticsService();
  const { PROFILE, BASIC } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  const avatarUrl = patient?.photo?.length ? patient.photo[0].url : undefined;

  return (
    <Flex
      position="relative"
      justify="center"
      width="100%"
    >
      <HomeAvatarCover
        style={{
          position: 'absolute',
          width: isMobile ? '1122px' : 'auto',
          left: '50%',
          zIndex: 0,
          transform: 'translate(-50%, 0)',
          pointerEvents: 'none',
          userSelect: 'none',
          top: isMobile ? '-153px' : '-50px',
        }}
      />
      <Flex
        direction="column"
        alignItems="center"
        gap="32px"
        mt={{ base: '40px', md: '50px' }}
        zIndex="1"
      >
        <NavLink
          to={`/${PROFILE}/${BASIC}/${VIEW}`}
          onClickCapture={() =>
            recordProfileEvents(trackEventInFlow, {
              EventName: 'MyProfileInteracted',
            })
          }
        >
          <Flex
            borderRadius="50%"
            bgImage="linear-gradient(180deg, #CED1FF 9.33%, #FFEFD8 86.18%)"
            outline="1px solid rgba(73, 86, 228, 0.16)"
            padding={{ base: '10px', md: '15px' }}
            minHeight={{ base: '120px', md: '170px' }}
            minWidth={{ base: '120px', md: '170px' }}
            position="relative"
          >
            <AnimatedGradient
              variant={variant}
              backgroundVariants={BG_VARIANTS_AVATAR}
              inset="0"
              borderRadius="50%"
              outline="1px solid rgba(73, 86, 228, 0.16)"
              padding="15px"
            />
            <Avatar
              src={avatarUrl}
              rounded="full"
              size="3xl"
              width="100%"
              maxW={{ base: '100px', md: '140px' }}
              maxH={{ base: '100px', md: '140px' }}
              loading="lazy"
              bgColor="fluentHealthSecondary.300"
            />
          </Flex>
        </NavLink>
        <Heading
          fontSize={{ base: '36', md: '44' }}
          color="gray.500"
        >
          Hello, {patient.name?.[0]?.given!.join(' ')}
        </Heading>
      </Flex>
    </Flex>
  );
}
