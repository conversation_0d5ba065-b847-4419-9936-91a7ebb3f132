/* eslint-disable no-nested-ternary */
import {
  CircularProgress,
  CircularProgressLabel,
  Container,
  Flex,
  Heading,
  Link,
  Text,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import React, { ReactNode, useEffect, useState } from 'react';
import { Info as InfoIcon } from 'react-feather';
import { AnimatePresence, motion } from 'framer-motion';
import { NavLink } from 'react-router-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode, Mousewheel } from 'swiper';
import { useReadLocalStorage } from 'usehooks-ts';
import shallow from 'zustand/shallow';
import { ChevronRightIcon } from '@chakra-ui/icons';

import { ReactComponent as BriefcaseIcon } from '../../../../assets/icons/briefcase.svg';
import { ReactComponent as ClockIcon } from '../../../../assets/icons/clock.svg';
import { ROUTE_ACTIONS, ROUTE_VARIABLES, SUMMARY_CARD_TYPES, SummaryCardType } from '../../lib/constants';
import { LOCAL_STORAGE_KEYS } from '@lib/constants';
import { usePatient, useRecordsCompleted } from '../../lib/medplum-state';
import { useAnalyticsService, useAuthService } from '@lib/state';
import {
  AnalyticsEventName,
  EventClickedElement,
  EventFlowNames,
  EventItemText,
  EventPropsNames,
} from '@lib/analyticsService';
import { InfoModal } from 'src/components/InfoModal';
import { useNotificationDisclosure } from '@lib/utils/utils';

// eslint-disable-next-line @typescript-eslint/naming-convention
const MotionFlex = motion(Flex);

function AvatarIcon() {
  const theme = useTheme();

  return (
    <Flex
      gap="3px"
      direction="column"
      alignItems="center"
    >
      <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="7.3716"
          cy="7.44777"
          r="7.30519"
          fill={theme.colors.iris[500]}
        />
      </svg>
      <svg
        width="30"
        height="16"
        viewBox="0 0 30 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.3702 0.675781C7.30106 0.675781 0.759766 7.21708 0.759766 15.2862H29.9805C29.9805 7.21708 23.4393 0.675781 15.3702 0.675781Z"
          fill={theme.colors.iris[500]}
        />
      </svg>
    </Flex>
  );
}

const { PROFILE, BASIC, DOCUMENTS, REVIEW } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;

const SUMMARY_CARD_ATTRIBUTES = {
  [SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW]: {
    link: {
      to: `/${DOCUMENTS}/${REVIEW}/${VIEW}`,
      text: 'View records',
      // getText: (props: any) => (props.isZero ? 'Add records' : 'View records'),
    },
    primaryColor: 'papaya.500',
    textColor: 'papaya.600',
    secondaryColor: 'papaya.100',
    hoverBackgroundColor: 'papaya.100',
    title: 'Records processed',
    tooltipTitle: 'Records being processed',
    tooltipDescription: 'Records in review',
    icon: <BriefcaseIcon />,
  },
  [SUMMARY_CARD_TYPES.REMINDERS]: {
    link: {
      to: '',
      text: 'Add new',
      // getText: (props: any) => (props.isZero ? 'Add New' : 'Check status'),
    },
    primaryColor: 'mintGreen.400',
    textColor: 'fluentHealthComplementary.Green',
    secondaryColor: 'mintGreen.100',
    hoverBackgroundColor: 'mintGreen.50',
    title: 'Upcoming reminders',
    tooltipTitle: 'Reminders',
    tooltipDescription: 'Reminders',
    icon: <ClockIcon />,
  },
  [SUMMARY_CARD_TYPES.PROFILE_COMPLETION]: {
    link: {
      to: `/${PROFILE}/${BASIC}/${VIEW}`,
      text: 'Start now',
      getText: (props: any) => (props.isZero ? 'Start now' : 'Add more details'),
    },
    primaryColor: 'iris.500',
    textColor: 'iris.600',
    secondaryColor: 'transparent',
    hoverBackgroundColor: 'iris.10',
    title: 'Profile completion',
    tooltipTitle: 'Profile completion',
    tooltipDescription: 'Profile completion',
  },
};

interface ISummaryCard {
  link?: { to: string; text: string; getText?: (props: any) => string };
  numericalRepresentation?: string | null;
  type: SummaryCardType;
  hoverType: SummaryCardType | null;
  setHoverType: (type: SummaryCardType | null) => void;
  primaryColor: string;
  textColor: string;
  secondaryColor: string;
  hoverBackgroundColor: string;
  title: string;
  tooltipTitle: string;
  tooltipDescription: string;
  icon?: ReactNode;
  onClickEvent?: () => void;
  showInfoIcon?: boolean;
}
function SummaryCard({
  link,
  numericalRepresentation,
  type,
  hoverType,
  setHoverType,
  primaryColor,
  textColor,
  secondaryColor,
  hoverBackgroundColor,
  title,
  tooltipTitle,
  tooltipDescription,
  icon,
  onClickEvent,
  showInfoIcon = false,
}: ISummaryCard) {
  const theme = useTheme();
  const infoModal = useDisclosure();

  const mouseEnterHandler = () => {
    setHoverType(type);
  };

  const mouseLeaveHandler = () => {
    setHoverType(null);
  };

  return (
    <Flex
      direction="column"
      padding="16px 20px"
      borderColor={primaryColor}
      borderWidth="1px"
      borderStyle="solid"
      borderRadius="12px"
      gap="18px"
      flex="1"
      transition="background 0.2s ease-in-out"
      bg={hoverType === type ? hoverBackgroundColor : 'transparent'}
      backdropFilter="blur(4px)"
      minWidth={{ base: '300px', md: '370px' }}
      onMouseEnter={mouseEnterHandler}
      onMouseLeave={mouseLeaveHandler}
    >
      <InfoModal
        title={tooltipTitle}
        isCentered
        description={tooltipDescription}
        onSecondaryButtonClick={infoModal.onClose}
        {...infoModal}
      />
      <Flex
        gap="8px"
        alignItems="center"
        position="relative"
        cursor="pointer"
        // onClick={infoModal.onOpen}
      >
        <Text>{title}</Text>
        <Flex
          justifyContent="space-between"
          alignItems="center"
          flex="1"
        >
          {showInfoIcon && (
            <Flex width="93px">
              <InfoIcon
                size={14}
                stroke={theme.colors.fluentHealthText[300]}
              />
            </Flex>
          )}
          <AnimatePresence>
            {hoverType === type && (
              <MotionFlex
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              />
            )}
          </AnimatePresence>
        </Flex>
      </Flex>
      <Flex
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Flex direction="column">
          <Heading
            fontSize="48"
            mt="auto"
            color={textColor}
          >
            {numericalRepresentation}
          </Heading>
          <Link
            as={NavLink}
            color="iris.500"
            marginLeft="0"
            to={link?.to}
            onClick={onClickEvent}
          >
            {link?.getText
              ? link.getText({
                  isZero: numericalRepresentation?.charAt?.(0)?.includes('0') || numericalRepresentation === null,
                })
              : link?.text}
            <ChevronRightIcon
              width="16px"
              height="16px"
            />
          </Link>
        </Flex>
        <Flex
          width="80px"
          height="80px"
          borderRadius="50%"
          bg={secondaryColor}
          alignItems="center"
          justifyContent="center"
        >
          {icon}
        </Flex>
      </Flex>
    </Flex>
  );
}

const SUMMARY_CARDS = [
  {
    type: SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW,
  },
  {
    type: SUMMARY_CARD_TYPES.REMINDERS,
  },
];

function ProfileCompletionCard({
  hoverType,
  setHoverType,
}: {
  hoverType: SummaryCardType | null;
  setHoverType: (type: SummaryCardType | null) => void;
}) {
  const { patient } = usePatient();
  const { trackEvent } = useAnalyticsService();
  const patientProgress = useReadLocalStorage<any>(`[${LOCAL_STORAGE_KEYS.PATIENT_PROGRESS}, ${patient.id}]`);

  const profileProgress = patientProgress
    ? Object.values(patientProgress).reduce<number>((total, value: any) => total + value, 0)
    : 0;

  const onTrackEventClick = () => {
    trackEvent(AnalyticsEventName.DashboardItemInteracted, {
      [EventPropsNames.FlowName]: EventFlowNames.Dashboard,
      [EventPropsNames.ClickedElement]: EventClickedElement.Link,
      [EventPropsNames.ClickedItem]: EventItemText.ProfileCompletion,
    });
  };

  return (
    <SummaryCard
      numericalRepresentation={`${profileProgress}%`}
      type={SUMMARY_CARD_TYPES.PROFILE_COMPLETION}
      hoverType={hoverType}
      setHoverType={setHoverType}
      {...SUMMARY_CARD_ATTRIBUTES[SUMMARY_CARD_TYPES.PROFILE_COMPLETION]}
      onClickEvent={onTrackEventClick}
      icon={
        <CircularProgress
          value={profileProgress}
          size="94px"
          thickness="3px"
          color="fluentHealth.500"
          trackColor="fluentHealthSecondary.300"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <CircularProgressLabel
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Flex
              bg="fluentHealthComplementary.Salmon2"
              display="flex"
              alignItems="center"
              justifyContent="center"
              borderRadius="50%"
              width="64px"
              height="64px"
            >
              <AvatarIcon />
            </Flex>
          </CircularProgressLabel>
        </CircularProgress>
      }
    />
  );
}

interface ISummary {
  hoverType: SummaryCardType | null;
  setHoverType: (type: SummaryCardType | null) => void;
}

export function Summary({ hoverType, setHoverType }: ISummary) {
  const [remindersCount, setRemindersCount] = useState<number | null>(0);

  const { authenticatedUser } = useAuthService();
  const { patient } = usePatient(authenticatedUser?.id);
  const { trackEvent } = useAnalyticsService();
  const disclosure = useNotificationDisclosure((state) => state, shallow);

  const { data } = useRecordsCompleted(authenticatedUser?.id);

  useEffect(() => {
    if (patient?.reminders && patient.reminders.length > 0) {
      const remindersLength = patient.reminders.filter((reminder: any) => {
        const dateTime = dayjs(reminder?.occurrenceDateTime ?? '');
        const isActiveOrOnHold = reminder.status === 'active' || reminder.status === 'on-hold';
        return isActiveOrOnHold && dateTime.isAfter(Date.now());
      }).length;
      setRemindersCount(remindersLength);
    }
  }, [patient?.reminders]);

  const onTrackEvent = (type: string) => {
    if (type === SUMMARY_CARD_TYPES.REMINDERS) {
      // TODO: change setTabIndex to 1 when notification feature is done
      disclosure.setTabIndex(0);
      disclosure.onOpen();
    }

    trackEvent(AnalyticsEventName.DashboardItemInteracted, {
      [EventPropsNames.FlowName]: EventFlowNames.Dashboard,
      [EventPropsNames.ClickedElement]: EventClickedElement.Link,
      [EventPropsNames.ClickedItem]:
        hoverType === SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW ? EventItemText.RecordsInReview : EventItemText.Reminders,
    });
  };
  return (
    <Container maxWidth="1240px">
      <Flex
        mt={{ base: '60px', md: '80px' }}
        width="100%"
      >
        <Swiper
          slidesPerView="auto"
          freeMode
          mousewheel
          modules={[Mousewheel, FreeMode]}
          spaceBetween={16}
          breakpoints={{
            767: {
              spaceBetween: 40,
            },
          }}
          style={{
            margin: 0,
          }}
        >
          {SUMMARY_CARDS.map(({ type }) => (
            <SwiperSlide
              key={type}
              style={{ width: 'auto' }}
            >
              <SummaryCard
                numericalRepresentation={
                  type === SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW
                    ? data
                      ? `${data.length}`
                      : null
                    : patient.reminders
                    ? `${remindersCount ?? 0}`
                    : null
                }
                type={type}
                hoverType={hoverType}
                setHoverType={setHoverType}
                onClickEvent={() => onTrackEvent(type)}
                {...SUMMARY_CARD_ATTRIBUTES[type]}
              />
            </SwiperSlide>
          ))}
          <SwiperSlide style={{ width: 'auto' }}>
            <ProfileCompletionCard
              hoverType={hoverType}
              setHoverType={setHoverType}
            />
          </SwiperSlide>
        </Swiper>
      </Flex>
    </Container>
  );
}
