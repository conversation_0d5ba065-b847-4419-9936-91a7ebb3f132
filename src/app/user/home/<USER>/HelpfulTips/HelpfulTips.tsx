// Package modules
import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Container, Flex, Heading, useTheme } from '@chakra-ui/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Swiper as ISwiper, Pagination } from 'swiper';
import { shuffleArray } from '@utils/utils';
import { useIsMobile } from '@components/ui/hooks/device.hook';

// Local modules
import { SliderNavigationButton } from 'src/components/ui/Slider';
import { HelpfulTip1 } from './HelpfulTip1';
import { HelpfulTip2 } from './HelpfulTip2';
import { HelpfulTip3 } from './HelpfulTip3';
import { HelpfulTip4 } from './HelpfulTip4';
import { HelpfulTip5 } from './HelpfulTip5';
import { HelpfulTip6 } from './HelpfulTip6';
import contentArticles from '@lib/contentLibrary/articles';
import { ArticleDetail, ArticleInfo } from '@lib/contentLibrary/types';

import { ReactComponent as HomePageBottomBgImage } from '@assets/objects/home-page-bottom-bg-image.svg';
// Assets
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const aritcleCardComponents = [HelpfulTip1, HelpfulTip2, HelpfulTip3, HelpfulTip4, HelpfulTip5, HelpfulTip6];
export function HelpfulTips() {
  const theme = useTheme();
  const [swiper, setSwiper] = useState<ISwiper>();
  const paginationElRef = useRef<HTMLDivElement>(null);
  const [swiperActiveIndex, setSwiperActiveIndex] = useState(0);
  const [showNavigationButton, setShowNavigationButton] = useState(false);
  const [articleList, setArticleList] = useState<ArticleInfo[]>([]);
  const [articles, setArticles] = useState<ArticleDetail[]>([]);
  const isMobile = useIsMobile();

  const slideChangeHandler = (slideChangePayload: any) => {
    setSwiperActiveIndex(slideChangePayload.activeIndex);
  };

  const fetchArticleDetails = (ids: string[]) => {
    return contentArticles.fetchArticlesDetails(ids);
  };

  useEffect(() => {
    if (articleList.length) {
      return;
    }
    contentArticles.fetchRecommendedArticles().then((articleListRes) => {
      if (!articleListRes) {
        return;
      }
      setArticleList(articleListRes);
      fetchArticleDetails(articleListRes.map((article) => article.articleId)).then((articlesRes) => {
        setArticles(articlesRes?.data?.articles ?? []);
      });
    });
  }, []);

  useEffect(() => {
    if (!swiper) {
      return () => {};
    }

    swiper.on('slideChange', slideChangeHandler);
    if (swiper?.pagination?.render) swiper.pagination?.render?.();
    if (swiper?.pagination) {
      swiper.pagination.el = paginationElRef.current as HTMLElement;
    }

    return () => swiper.off('slideChange', slideChangeHandler);
  }, [swiper]);

  useEffect(() => {
    if (!paginationElRef.current) {
      return;
    }
    if (swiper?.pagination) {
      swiper.pagination.el = paginationElRef.current as HTMLElement;
    }
  }, [paginationElRef.current]);

  const shuffledArticles = useMemo(() => shuffleArray(articles), [articles]);
  const shuffledCardComponents = useMemo(() => shuffleArray(aritcleCardComponents), []);

  const bookmarkArticle = async (articleId: string) => {
    await contentArticles.bookMarkArticle(articleId);
  };
  const unbookmarkedArticle = async (interactionId: string) => {
    await contentArticles.unBookMarkArticle(interactionId);
  };
  const handleBookMarkClick = async (article: ArticleDetail) => {
    const articleInfo = articleList.find((item) => item.articleId === article.id);
    if (!articleInfo) {
      return;
    }
    if (articleInfo?.article?.isBookmarked) {
      const interactionId = articleInfo?.article?.interactions?.find(
        (interaction) => interaction.type === 'ARTICLE_BOOKMARK'
      )?.id;
      await unbookmarkedArticle(interactionId as string);
    } else {
      await bookmarkArticle(article.id);
    }
    contentArticles.fetchRecommendedArticles().then((articleListRes) => {
      if (!articleListRes) {
        return;
      }
      setArticleList(articleListRes);
    });
  };

  if (!articles.length) return null;

  return (
    <Flex
      mt={{ base: '40px', md: '80px' }}
      width="100%"
      position="relative"
      display="flex"
      direction="column"
      alignItems="center"
      gap={{ base: '24px', md: '32px' }}
    >
      <Heading
        fontSize={{ base: '24px', md: '32px' }}
        textAlign="center"
      >
        Get on the Fluent app for personalised content
      </Heading>
      <Container maxWidth="1240px">
        <Box
          position="relative"
          onMouseEnter={() => setShowNavigationButton(true)}
          onMouseLeave={() => setShowNavigationButton(false)}
        >
          <Swiper
            spaceBetween={20}
            slidesPerView={1}
            onInit={(sw) => {
              setSwiper(sw);
              sw.pagination.render();
            }}
            onAfterInit={(sw) => {
              sw.pagination.render();
            }}
            modules={[Pagination, Autoplay]}
            autoplay={{
              delay: 5000,
            }}
            pagination={{
              el: paginationElRef.current,
              clickable: true,
            }}
          >
            {shuffledArticles.map((article, idx) => {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              const CardComponent = shuffledCardComponents[idx % shuffledCardComponents.length];
              return (
                <SwiperSlide key={article.id}>
                  <CardComponent
                    article={article}
                    onBookMarkClick={() => handleBookMarkClick(article)}
                    isBookMarked={articleList.find((item) => item.articleId === article.id)?.article?.isBookmarked}
                  />
                </SwiperSlide>
              );
            })}
          </Swiper>
          <Flex
            justify="center"
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            zIndex="1"
            pb="16px"
          >
            <Flex justifyContent="center">
              <Flex
                ref={paginationElRef}
                justifyContent="center"
                padding="8px"
                borderRadius="36px"
                bg={swiperActiveIndex === 0 ? 'transparent' : 'white'}
                gap="3px"
                sx={{
                  transform: 'translateX(0) !important',
                  '--swiper-pagination-color': swiperActiveIndex === 0 ? 'white' : theme.colors.fluentHealthText[100],
                  '--swiper-pagination-bullet-inactive-color':
                    swiperActiveIndex === 0 ? 'white' : theme.colors.fluentHealthText[100],
                  '--swiper-pagination-bullet-inactive-opacity': '0.3',
                  '--swiper-pagination-bullet-size': '6px',
                  '--swiper-pagination-bullet-width': '6px',
                  '--swiper-pagination-bullet-height': '6px',
                  '--swiper-pagination-bullet-horizontal-gap': '0px',
                  '& .swiper-pagination-bullet-active': {
                    width: '20px',
                    borderRadius: '4px',
                  },
                }}
              />
            </Flex>
          </Flex>
          <Box display={{ base: 'none', md: 'block' }}>
            <SliderNavigationButton
              onClick={() => swiper?.slidePrev()}
              side="left"
              left={0}
              top="50%"
              height={{ base: 'max-content' }}
              transform="translateY(-50%)"
              paddingLeft="12px"
              justifyContent="flex-start"
              showButton={showNavigationButton}
            />
            <SliderNavigationButton
              onClick={() => swiper?.slideNext()}
              side="right"
              right={0}
              top="50%"
              transform="translateY(-50%)"
              height={{ base: 'max-content' }}
              paddingRight="12px"
              justifyContent="flex-end"
              showButton={showNavigationButton}
            />
          </Box>
        </Box>
      </Container>
      {!isMobile && (
        <HomePageBottomBgImage
          style={{
            position: 'absolute',
            top: -300,
            left: 0,
            zIndex: 0,
          }}
        />
      )}
    </Flex>
  );
}
