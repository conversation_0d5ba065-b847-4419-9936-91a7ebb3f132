import { useEffect, useState } from 'react';
import { Flex, Heading, IconButton, Image, Text } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';

import { ArticleDetail } from '@lib/contentLibrary/types';
import contentArticles from '@lib/contentLibrary/articles';

import { ReactComponent as HelpfulTip5BottomRight } from '@assets/objects/helpful-tip-5-bottom-right.svg';
import { ReactComponent as HelpfulTip5TopLeft } from '@assets/objects/helpful-tip-5-top-left.svg';

interface IHelpfulTip5Props {
  article: ArticleDetail;
  onBookMarkClick?: () => void;
  isBookMarked?: boolean;
}
export function HelpfulTip5({ article, onBookMarkClick, isBookMarked }: IHelpfulTip5Props) {
  const [image, setImage] = useState<string>();
  const {
    article_title: title,
    article_teaser: description,
    primary_topic: primaryTopic,
    article_hero_image: articleHeroImage,
  } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  const { id: assetId } = articleHeroImage;
  const fetchImage = async () => {
    if (!assetId) return;
    const imageRes = await contentArticles.fetchAsset(assetId);
    setImage(imageRes);
  };
  useEffect(() => {
    fetchImage();
  }, [assetId]);
  return (
    <Flex
      borderRadius="40px"
      width={{ base: 'auto', md: '100%' }}
      position="relative"
      overflow="hidden"
      minHeight={{ base: '560px', md: '550px' }}
      bg="#EBF2E1"
    >
      <Flex
        height="100%"
        width={{ base: '100%', md: '50%' }}
        bg="#EBF2E1"
        direction="column"
        alignItems={{ base: 'center', md: 'start' }}
        padding={{ base: '116px 22px', md: '100px' }}
        textAlign={{ base: 'center', md: 'start' }}
        gap="20px"
        borderTopRightRadius="40px"
        position="absolute"
        left="0"
        zIndex="1"
      >
        {!!eyebrowText && <Text zIndex="1">{eyebrowText}</Text>}
        {!!title && (
          <Heading
            fontSize={{ base: '24px', md: '32px' }}
            zIndex="1"
          >
            {title}
          </Heading>
        )}
        {!!description && (
          <Text
            fontSize={{ base: 'md', md: 'lg' }}
            zIndex="1"
          >
            {description}
          </Text>
        )}
        <Flex
          mt="12px"
          gap="10px"
          alignItems="center"
          direction={{ base: 'column', md: 'row' }}
          zIndex="1"
        >
          <IconButton
            aria-label="Bookmark Button"
            py="8px"
            px="8px"
            minW="auto"
            variant="outline"
            borderColor="gray.500"
            icon={
              <BookmarkIcon
                size={36}
                strokeWidth={1}
                fill={isBookMarked ? '#20785C' : 'transparent'}
                color="black"
              />
            }
            _hover={{ borderColor: 'black' }}
            onClick={onBookMarkClick}
          />
          <Text
            fontSize="sm"
            color="fluentHealthText.100"
            maxWidth={{ base: '150px', md: 'auto' }}
            minW={{ base: '150px', md: '267px' }}
            textAlign="center"
          >
            Save to read the full article on the mobile app
          </Text>
        </Flex>
        <HelpfulTip5TopLeft
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
          }}
        />
        <HelpfulTip5BottomRight
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
          }}
        />
      </Flex>
      <Image
        position="absolute"
        right="0"
        width="60%"
        height="100%"
        objectFit="cover"
        src={image || 'home/helpful-tip-5.png'}
        display={{ base: 'none', md: 'block' }}
      />
    </Flex>
  );
}
