import { Flex, Heading, IconButton, Image, Text } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';

import { ArticleDetail } from '@lib/contentLibrary/types';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';

interface IHelpfulTip4Props {
  article: ArticleDetail;
  onBookMarkClick?: () => void;
  isBookMarked?: boolean;
}
export function HelpfulTip4({ article, onBookMarkClick, isBookMarked }: IHelpfulTip4Props) {
  const isMobile = useIsMobile();
  const { article_title: title, article_teaser: description, primary_topic: primaryTopic } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  return (
    <Flex
      padding={{ base: 0, md: '0px 180px' }}
      bg="#144037"
      borderRadius="40px"
      width={{ base: 'auto', md: '100%' }}
      position="relative"
      overflow="hidden"
      justifyContent="center"
      alignItems={{ base: 'center', md: 'auto' }}
      minHeight={{ base: '560px', md: '550px' }}
    >
      <Flex
        bg="mintGreen.100"
        width={{ base: '100%', md: '640px' }}
        height={{ base: '390px', md: '570px' }}
        padding="46px 22px"
        direction="column"
        alignItems="center"
        justifyContent="center"
        gap="20px"
        zIndex="1"
      >
        {!!eyebrowText && <Text zIndex="1">{eyebrowText}</Text>}
        {!!title && (
          <Heading
            fontSize={{ base: '24px', md: '32px' }}
            textAlign="center"
            maxWidth="380px"
            zIndex="1"
          >
            {title}
          </Heading>
        )}
        {!!description && (
          <Text
            fontSize={{ base: 'md', md: 'lg' }}
            maxWidth="350px"
            textAlign="center"
            zIndex="1"
          >
            {description}
          </Text>
        )}
        <Flex
          mt="12px"
          gap="10px"
          direction="column"
          alignItems="center"
          zIndex="1"
        >
          <IconButton
            aria-label="Bookmark Button"
            py="8px"
            px="8px"
            minW="auto"
            variant="outline"
            borderColor="gray.500"
            icon={
              <BookmarkIcon
                size={36}
                strokeWidth={1}
                fill={isBookMarked ? '#20785C' : 'transparent'}
                color="black"
              />
            }
            _hover={{ borderColor: 'black' }}
            onClick={onBookMarkClick}
          />
          <Text
            fontSize="sm"
            color="fluentHealthText.100"
            maxWidth={isMobile ? '150px' : 'auto'}
            textAlign="center"
          >
            Save to read the full article on the mobile app
          </Text>
        </Flex>
      </Flex>
      <Image
        src={`home/helpful-tip-4${isMobile ? '-mobile' : ''}.png`}
        position="absolute"
        inset="0"
      />
    </Flex>
  );
}
