import { Flex, Heading, IconButton, Text } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { ArticleDetail } from '@lib/contentLibrary/types';

import { ReactComponent as HelpfulTip2Background } from '@assets/objects/helpful-tip-2-background.svg';
import { ReactComponent as HelpfulTip2BackgroundMobile } from '@assets/objects/helpful-tip-2-background-mobile.svg';

interface IHelpfulTip2Props {
  article: ArticleDetail;
  onBookMarkClick?: () => void;
  isBookMarked?: boolean;
}
export function HelpfulTip2({ article, onBookMarkClick, isBookMarked }: IHelpfulTip2Props) {
  const isMobile = useIsMobile();
  const { article_title: title, article_teaser: description, primary_topic: primaryTopic } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  return (
    <Flex
      padding={{ base: '94px 47px', md: '80px 180px' }}
      bg="#144037"
      borderRadius="40px"
      width={{ base: 'auto', md: '100%' }}
      position="relative"
      overflow="hidden"
      direction="column"
      alignItems="center"
      gap="20px"
      minHeight={{ base: '560px', md: '550px' }}
    >
      {!!eyebrowText && (
        <Text
          fontSize="sm"
          color="white"
          zIndex="1"
        >
          {eyebrowText}
        </Text>
      )}
      {!!title && (
        <Heading
          color="white"
          fontSize={{ base: '20px', md: '32px' }}
          textAlign="center"
          maxWidth="390px"
          zIndex="1"
        >
          {title}
        </Heading>
      )}
      {!!description && (
        <Text
          fontSize="lg"
          color="white"
          maxWidth="350px"
          textAlign="center"
          zIndex="1"
        >
          {description}
        </Text>
      )}
      <Flex
        mt="12px"
        gap="10px"
        direction="column"
        alignItems="center"
        zIndex="1"
      >
        <IconButton
          aria-label="Bookmark Button"
          variant="outline"
          width="48px"
          height="48px"
          borderColor="rgba(255,255,255,0.2)"
          borderRadius="50%"
          backgroundColor={isBookMarked ? '#7EC0AB' : 'transparent'}
          icon={
            <BookmarkIcon
              size={36}
              fill={isBookMarked ? '#20785C' : 'transparent'}
              strokeWidth={1}
              color={isBookMarked ? 'transparent' : 'white'}
            />
          }
          _hover={{ borderColor: 'white' }}
          onClick={onBookMarkClick}
        />
        <Text
          fontSize="sm"
          color="rgba(255, 255, 255, 0.3)"
          maxWidth={{ base: '150px', md: 'auto' }}
          textAlign="center"
        >
          Save to read the full article on the mobile app
        </Text>
      </Flex>
      {isMobile ? (
        <HelpfulTip2BackgroundMobile
          style={{
            position: 'absolute',
            top: 0,
            transform: 'scale(1.3)',
          }}
        />
      ) : (
        <HelpfulTip2Background
          style={{
            position: 'absolute',
            top: 0,
          }}
        />
      )}
    </Flex>
  );
}
