import { Flex, Heading, Text } from '@chakra-ui/react';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { ArticleDetail } from '@lib/contentLibrary/types';

import { ReactComponent as HelpfulTip1Background } from '@assets/objects/helpful-tip-1-background.svg';

interface IHelpfulTip1Props {
  article: ArticleDetail;
}
export function HelpfulTip1({ article }: IHelpfulTip1Props) {
  const isMobile = useIsMobile();
  const { article_title: title, primary_topic: primaryTopic } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  return (
    <Flex
      padding={{ base: '145px 12px', lg: '140px 180px' }}
      bg="#144037"
      borderRadius="40px"
      width={{ base: 'auto', md: '100%' }}
      position="relative"
      overflow="hidden"
      minHeight={{ base: '560px', md: '550px' }}
    >
      <HelpfulTip1Background
        style={{
          position: 'absolute',
          inset: 0,
          transform: isMobile ? 'scale(1.3)' : 'none',
        }}
      />
      <Flex
        zIndex="1"
        direction="column"
        alignItems="center"
        width="100%"
        mb={{ base: '51', md: '57px' }}
        gap="12px"
        padding={{ base: '50px 16px', md: '50px' }}
        borderRadius="40px"
        bg="linear-gradient(90deg, #BEF0E4 0%, #FFF2DF 100%)"
      >
        {eyebrowText && <Text fontSize={{ base: '16px', md: '17px' }}>{eyebrowText}</Text>}
        {title && (
          <Heading
            fontSize={{ base: '24', md: '28' }}
            textAlign="center"
            maxWidth="650px"
          >
            {title}
          </Heading>
        )}
      </Flex>
    </Flex>
  );
}
