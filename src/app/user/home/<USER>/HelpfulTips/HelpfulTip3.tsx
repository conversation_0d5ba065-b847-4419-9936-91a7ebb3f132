import { Flex, Heading, IconButton, Text } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { ArticleDetail } from '@lib/contentLibrary/types';

import { ReactComponent as HelpfulTip3TopRight } from '@assets/objects/helpful-tip-3-top-right.svg';
import { ReactComponent as HelpfulTip3BottomLeft } from '@assets/objects/helpful-tip-3-bottom-left.svg';

interface IHelpfulTip3Props {
  article: ArticleDetail;
  onBookMarkClick?: () => void;
  isBookMarked?: boolean;
}
export function HelpfulTip3({ article, onBookMarkClick, isBookMarked }: IHelpfulTip3Props) {
  const isMobile = useIsMobile();
  const { article_title: title, primary_topic: primaryTopic } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  return (
    <Flex
      padding={{ base: '126px 22px', md: '80px 180px' }}
      bg="mintGreen.100"
      borderRadius="40px"
      width={{ base: 'auto', md: '100%' }}
      position="relative"
      overflow="hidden"
      direction="column"
      alignItems="center"
      gap={{ base: '32px', md: '20px' }}
      minHeight={{ base: '560px', md: '550px' }}
    >
      <Flex
        direction="column"
        alignItems="center"
        gap={{ base: '12px', md: '20px' }}
      >
        {!!eyebrowText && <Text zIndex="2">{eyebrowText}</Text>}
        {!!title && (
          <Heading
            fontSize={{ base: '22px', md: '32px' }}
            textAlign="center"
            maxWidth="510px"
            zIndex="1"
          >
            {title}
          </Heading>
        )}
      </Flex>

      <Flex
        mt="12px"
        gap={{ base: '16px', md: '10px' }}
        direction="column"
        alignItems="center"
        zIndex="1"
      >
        <IconButton
          aria-label="Bookmark Button"
          py="8px"
          px="8px"
          minW="auto"
          variant="outline"
          borderColor="gray.500"
          icon={
            <BookmarkIcon
              size={36}
              strokeWidth={1}
              fill={isBookMarked ? '#20785C' : 'transparent'}
              color="black"
            />
          }
          _hover={{ borderColor: 'black' }}
          onClick={onBookMarkClick}
        />
        <Text
          fontSize="sm"
          color="fluentHealthText.100"
          maxWidth={{ base: '150px', md: 'auto' }}
          textAlign="center"
        >
          Save to read the full article on the mobile app
        </Text>
      </Flex>
      <HelpfulTip3TopRight
        style={{
          position: 'absolute',
          top: isMobile ? '-150px' : 0,
          right: isMobile ? '-250px' : 0,
        }}
      />
      <HelpfulTip3BottomLeft
        style={{
          position: 'absolute',
          bottom: isMobile ? '-150px' : 0,
          left: isMobile ? '-200px' : 0,
        }}
      />
    </Flex>
  );
}
