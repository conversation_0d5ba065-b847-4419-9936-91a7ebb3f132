import { useEffect, useState } from 'react';
import { Box, Flex, Heading, IconButton, Image, Text } from '@chakra-ui/react';
import { Bookmark as BookmarkIcon } from 'react-feather';

import { ArticleDetail } from '@lib/contentLibrary/types';
import contentArticles from '@lib/contentLibrary/articles';

import { ReactComponent as HelpfulTip6BottomRight } from '@assets/objects/helpful-tip-6-bottom-right.svg';
import { ReactComponent as HelpfulTip6Left } from '@assets/objects/helpful-tip-6-left.svg';

interface IHelpfulTip5Props {
  article: ArticleDetail;
  onBookMarkClick?: () => void;
  isBookMarked?: boolean;
}
export function HelpfulTip6({ article, onBookMarkClick, isBookMarked }: IHelpfulTip5Props) {
  const [image, setImage] = useState<string>();
  const {
    article_title: title,
    article_teaser: description,
    primary_topic: primaryTopic,
    article_hero_image: articleHeroImage,
  } = article;
  const eyebrowText = primaryTopic?.fact_code?.fact_display;
  const { id: assetId } = articleHeroImage;
  const fetchImage = async () => {
    if (!assetId) return;
    const imageRes = await contentArticles.fetchAsset(assetId);
    setImage(imageRes);
  };
  useEffect(() => {
    fetchImage();
  }, [assetId]);
  return (
    <Flex
      borderRadius="40px"
      width={{ base: 'auto', xl: '100%' }}
      position="relative"
      overflow="hidden"
      minHeight={{ base: '560px', xl: '550px' }}
      alignItems="center"
      bg="mintGreen.50"
    >
      <Flex
        height="100%"
        width={{ base: '100%', xl: '60%' }}
        direction="column"
        padding={{ base: '86px 21px', xl: '45px 100px' }}
        gap="20px"
        borderTopRightRadius="40px"
        zIndex="1"
      >
        {!!eyebrowText && (
          <Text
            zIndex="1"
            mb="-8px"
          >
            {eyebrowText}
          </Text>
        )}
        {!!title && (
          <Heading
            fontSize={{ base: '24px', xl: '32px' }}
            zIndex="1"
          >
            {title}
          </Heading>
        )}
        <Flex
          direction="column"
          gap="12px"
        >
          {!!description && (
            <Text
              fontSize={{ base: 'sm', xl: 'lg' }}
              color="gray.500"
              opacity="80%"
            >
              {description}
            </Text>
          )}
        </Flex>
        <Flex
          gap="10px"
          alignItems="center"
          zIndex="1"
          display={{ base: 'none', xl: 'flex' }}
        >
          <IconButton
            aria-label="Bookmark Button"
            py="8px"
            px="8px"
            minW="auto"
            variant="outline"
            borderColor="gray.500"
            icon={
              <BookmarkIcon
                size={36}
                strokeWidth={1}
                fill={isBookMarked ? '#20785C' : 'transparent'}
                color="black"
              />
            }
            _hover={{ borderColor: 'black' }}
            onClick={onBookMarkClick}
          />
          <Text
            fontSize="sm"
            color="gray.500"
            opacity="80%"
          >
            Save to read the full article on the mobile app
          </Text>
        </Flex>
      </Flex>
      <HelpfulTip6Left
        style={{
          position: 'absolute',
          left: 0,
          height: '100%',
        }}
      />
      <Box
        display={{ base: 'none', xl: 'block' }}
        borderRadius="50%"
        overflow="hidden"
      >
        <HelpfulTip6BottomRight
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
          }}
        />
        <Image
          src={image || 'home/helpful-tip-5.png'}
          width="400px"
          height="400px"
        />
      </Box>
    </Flex>
  );
}
