// Package modules
import { Box, ChakraProps } from '@chakra-ui/react';
import { AnimatePresence, motion } from 'framer-motion';

// Local modules
import { SUMMARY_CARD_TYPES } from '../../lib/constants';

// eslint-disable-next-line @typescript-eslint/naming-convention
const MotionGradient = motion(Box);

const GRADIENT_TRANSITION = {
  duration: 0.2,
  ease: 'easeIn',
};

const GRADIENT_ANIMATION_VARIANTS = {
  initial: {
    opacity: 0,
  },
  animate: {
    opacity: 1,
  },
  exit: {
    opacity: 0,
  },
};

export const BG_VARIANTS_HOME_PAGE: Record<string, string> = {
  [SUMMARY_CARD_TYPES.PROFILE_COMPLETION]: 'linear-gradient(180deg, #DADCFF 0%, rgba(218, 220, 255, 0) 59.9%)',
  [SUMMARY_CARD_TYPES.REMINDERS]: 'linear-gradient(180deg, #E5F9F4 10.11%, rgba(255, 255, 255, 0) 99.1%)',
  [SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW]:
    'linear-gradient(179.62deg, rgba(255, 193, 173, 0.8) 0.33%, rgba(255, 255, 255, 0) 69.14%)',
};

export const BG_VARIANTS_AVATAR: Record<string, string> = {
  [SUMMARY_CARD_TYPES.PROFILE_COMPLETION]: 'linear-gradient(352.87deg, #DADCFF 5.56%, #FFF2DF 94.44%)',
  [SUMMARY_CARD_TYPES.REMINDERS]: 'linear-gradient(352.87deg, #BEF0E4 5.56%, #FFF2DF 94.44%)',
  [SUMMARY_CARD_TYPES.RECORDS_IN_REVIEW]: 'linear-gradient(352.87deg, #FFC1AD 5.56%, #FFF2DF 94.44%)',
};

interface IAnimatedGradient {
  variant: string | null;
  backgroundVariants: Record<string, string>;
}
export function AnimatedGradient({ variant, backgroundVariants, ...props }: IAnimatedGradient & ChakraProps) {
  return (
    <AnimatePresence>
      {variant && Object.keys(backgroundVariants).includes(variant) && (
        <MotionGradient
          key={variant}
          animate="animate"
          initial="initial"
          exit="exit"
          variants={GRADIENT_ANIMATION_VARIANTS}
          transition={GRADIENT_TRANSITION}
          bgImage={backgroundVariants[variant]}
          position="absolute"
          {...props}
        />
      )}
    </AnimatePresence>
  );
}
