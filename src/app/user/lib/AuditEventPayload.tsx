import { generateISO8601DateTime } from '@lib/utils/utils';
import { FHIR_CODE_SYSTEM_URL } from '@lib/constants';

type AuditEventInput = {
  patientId: string;
  observationId?: string;
  display: string;
  currentValue: string | undefined;
  previousValue?: string | object;
  code: any;
  urnId?: string;
};

export const createAuditEventPayload = ({
  patientId,
  observationId,
  display,
  currentValue,
  previousValue,
  code,
  urnId,
}: AuditEventInput) => {
  if (currentValue?.toString() === previousValue?.toString()) {
    return {};
  }
  const detailCurrent = [
    {
      type: 'current',
      valueString: typeof currentValue === 'string' ? currentValue : JSON.stringify(currentValue),
    },
  ];
  const entity = [];
  if (currentValue) {
    entity.push({
      what: {
        reference: observationId ? `Observation/${observationId}` : `${urnId}`,
        display,
      },
      type: {
        system: `${FHIR_CODE_SYSTEM_URL}/FluentHealthUI`,
        code: 'audit-event-types:current',
        display: 'Current',
      },
      detail: detailCurrent,
    });
  }
  if (previousValue) {
    const detailPrevious = [
      {
        type: 'previous',
        valueString: typeof previousValue === 'string' ? previousValue : JSON.stringify(previousValue),
      },
    ];

    entity.push({
      what: {
        reference: observationId ? `Observation/${observationId}` : `Patient/${patientId}`,
        display: 'Observation',
      },
      type: {
        system: `${FHIR_CODE_SYSTEM_URL}/FluentHealthUI`,
        code: 'audit-event-types:previous',
        display: 'Previous',
      },
      detail: detailPrevious,
    });
  }

  return {
    resourceType: 'AuditEvent',
    recorded: generateISO8601DateTime(),
    type: {
      system: 'http://dicom.nema.org/resources/ontology/DCM',
      code: '110110',
      display: 'Patient Record',
    },
    subtype: [
      {
        system: 'http://dicom.nema.org/resources/ontology/DCM',
        code,
        display,
      },
    ],
    source: {
      observer: {
        reference: `Patient/${patientId}`,
        display: 'System',
      },
    },
    agent: [
      {
        type: {
          coding: [
            {
              system: 'http://terminology.hl7.org/CodeSystem/v3-RoleClass',
              code: 'PAT',
              display: 'patient',
            },
          ],
        },
        who: {
          reference: `Patient/${patientId}`,
          display: 'System',
        },
        requestor: true,
      },
    ],
    entity,
  };
};
