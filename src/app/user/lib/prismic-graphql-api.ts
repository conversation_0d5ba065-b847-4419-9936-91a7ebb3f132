import UniversalCookie from 'universal-cookie';

import { API_GATEWAY_URL, API_REPO_NAME } from '@lib/constants';
import { allContentFaqs } from './prismic-graphql-query';
import { AuthService } from '@lib/authService';

const BASE_URL = `${API_GATEWAY_URL}/tpp/prismic/v2`;

export const prismicRefCheck = async () => {
  const cookies = new UniversalCookie();
  let prismicRef = cookies.get('prismic-ref') || null;
  if (!prismicRef || prismicRef === undefined || prismicRef === 'undefined') {
    const repoJson = await fetch(`${BASE_URL}/api/v2?repository=${API_REPO_NAME}`, {
      method: 'GET',
      headers: AuthService.instance.withAuthHeader(),
    });

    const repoResp = await repoJson.json();

    const { refs } = repoResp || {};
    const { ref } = refs?.[0] || {};
    prismicRef = ref;
    cookies.set('prismic-ref', prismicRef);
  }
  return prismicRef;
};

const mapPrismicFaq = (arr: any) => {
  const groups: any = [];
  if (arr && arr.allContent_faqs && arr.allContent_faqs.edges) {
    arr.allContent_faqs.edges.forEach((obj: any) => {
      const topic = obj.node.faq_topic;
      const groupIndex = groups.findIndex((group: any) => group.label === topic);
      if (groupIndex !== -1) {
        groups[groupIndex].value.push({ ...obj.node, id: obj.node._meta.id });
      } else {
        groups.push({ label: topic, value: [{ ...obj.node, id: obj.node._meta.id }] });
      }
    });
  }

  return groups;
};

export const getGraphQlFaqs = async () => {
  const prismicRef = await prismicRefCheck();
  const query = encodeURIComponent(`${allContentFaqs}`);

  const repoJson = await fetch(`${BASE_URL}/graphql?query=${query}`, {
    method: 'GET',
    headers: { ...AuthService.instance.withAuthHeader(), 'Prismic-Ref': prismicRef },
  });
  const { data } = await repoJson.json();
  return mapPrismicFaq(data);
};
