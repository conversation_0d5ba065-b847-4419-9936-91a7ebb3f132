import dayjs from 'dayjs';

import { YesOrNoAnswer } from '@lib/models/misc';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import {
  RelatedPerson,
  RelatedPersonConditionPayload,
  RelatedPersonProcedurePayload,
} from '@lib/models/related-person';
import { QuestionnaireResponse } from 'src/gql/graphql';

export const getLastScreeningWithUpdatedAnswer = (
  screeningList: QuestionnaireResponse[],
  answer: YesOrNoAnswer | null
) => {
  const screening = screeningList[screeningList.length - 1];
  if (answer !== null && screening) {
    return {
      ...screening,
      family_history: answer === YesOrNoAnswer.Yes ? YesOrNoAnswer.No : YesOrNoAnswer.Yes,
    };
  }
  return null;
};

/**
 * @param value Example: 12:30
 */
export const isTimeFieldHasValueFromThePast = (value: any) => {
  const valueUnix = dayjs()
    .set('hour', Number(value.split(':')[0]))
    .set('minutes', Number(value.split(':')[1]))
    .unix();
  const nowUnix = dayjs().unix() + 60; // + 60 means + 1 min to include the current minute

  return valueUnix < nowUnix;
};

/**
 * Create an updated structure of Related Person procedures and conditions for POST/PUT payloads.
 * @param relatedPerson
 * @return [conditions, procedures]
 */
export const formatRelatePersonConditionsAndProceduresForPayload = (
  relatedPerson?: RelatedPerson | null
): [RelatedPersonConditionPayload[], RelatedPersonProcedurePayload[]] => {
  if (!relatedPerson) {
    return [[], []];
  }

  const relatedPersonDuplicate = { ...relatedPerson };

  const prevProcedureList: RelatedPersonProcedurePayload[] = [...relatedPersonDuplicate.procedures].map((p) => ({
    ...p,
    external_reports: p.external_reports.map((report: AttachedMedicalRecord) => report.id),
  }));

  const prevConditionList: RelatedPersonConditionPayload[] = [...relatedPersonDuplicate.conditions].map((c) => ({
    ...c,
    external_reports: c?.external_reports ? c.external_reports.map((report: AttachedMedicalRecord) => report.id) : [], // Fallback to an empty array if external_reports is undefined
  }));

  return [prevConditionList, prevProcedureList];
};
export function getISTDate(): Date {
  const now = new Date();
  const offsetIST = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  return new Date(now.getTime() + offsetIST);
}
