export type HealthcareProxyContactPayLoad = {
  firstName?: string;
  lastName?: string;
  relationLabel?: string;
  relationCode?: string;
  phoneNumber?: string;
  file?:
    | Array<{
        attachment: {
          contentType: string;
          url: string;
          title: string;
          size: number;
          id: string;
        };
      }>
    | any;
};

export type HealthCareProxyDeleteFilePayload = {};
