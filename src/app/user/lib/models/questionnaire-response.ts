export type QuestionnaireResponsePayload = {
  id?: any;
  item?: any;
  questionnaire: any;
  linkageToList?: any;
  status?: string;
  resourceType?: string;
  authored?: string;
};

export type DeleteQuestionnaireResponseTaskPayload = {
  questionnaireId?: any;
  deleteTask?: string;
  identifier?: string;
};

export type ObservationVitalResponsePayload = {
  item?: any;
  code?: object;
  valueQuantity?: object;
  method?: object;
  id?: any;
  observationId?: any;
  component?: object;
  recordingValue?: any;
  recordingDateTime?: string;
  positionRecorded?: string;
  positionRecordedText?: string;
  deleteTask?: string;
  identifier?: string;
};
