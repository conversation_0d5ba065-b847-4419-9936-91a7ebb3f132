import { ObservationNames } from '@src/types/observation';

import { Communication, Coverage, Patient, RelatedPerson } from 'src/gql/graphql';

export interface PatientData {
  data: Patient;
}

export type PatientPayload = {
  resourceType?: string;
  id?: Patient['id'];
  name?: Patient['name']; // A name associated with the individual.\
  telecom?: Patient['telecom'];
  gender?: Patient['gender'];
  birthDate?: string; // A date or partial date. See Patient schema
  address?: Patient['address'];
  preffered_language?: string;
  ethnicity?: string;
  photo?: ProfilePhotoType[] | null;
};

type ProfilePhotoType = {
  url: string;
};

export type PatientDetails = Patient & {
  age: number;
  recordsInReview: any;
  reminders: RecordsInReview[];
  ethnicity: any[];
  genderIdentity: any[];
  bloodType: any[];
  preferred_language: string | undefined;
};

interface Extension {
  url: string;
  valueCode?: string;
}

export type UpdatePatientPayload = {
  patientGender?: string;
  extension?: Extension[];
  communication?: Communication[];
};

export type ObservationPayload = {
  name: ObservationNames;
  status: string;
  system: string;
  code: string;
  codeDisplay: string;
  categoryCode: string;
  categoryDisplay: string;
  unit?: string;
  value: string | number;
  unitSystem?: string;
  valueCodeUrl?: string;
  valueCode?: string;
  valueDisplay?: string;
  valueText?: string;
  previousValue?: string;
};

export type BloodTypeObservationPayload = {
  valueCodeableConceptSystem?: string;
  valueCodeableConceptCode: string;
  valueCodeableConceptDisplay: string;
  valueCodeableConceptText: string;
};

export type HealthcareProxyData = {
  data: HealthcareProxy;
};

export type HealthcareProxy = RelatedPerson & {
  id: string;
  file: any;
};

export interface RecordsInReview {
  id: string;
  status: string;
}

export type HealthInsurance = Coverage & {
  contract: any;
  file: any;
  item: any;
};
