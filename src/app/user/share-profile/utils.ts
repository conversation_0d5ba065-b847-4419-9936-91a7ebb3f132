import { format, isValid, parseISO } from 'date-fns';

// Vital reference ranges for status calculation
export const vitalReferenceRanges: Record<
  string,
  {
    low: { min: number; max: number; label: string; color: string };
    optimal: { min: number; max: number; label: string; color: string };
    high: { min: number; max: number; label: string; color: string };
  }
> = {
  'vi:blood-pressure': {
    low: { min: 0, max: 89, label: 'Low', color: 'blue' },
    optimal: { min: 90, max: 129, label: 'Optimal', color: 'green' },
    high: { min: 130, max: 200, label: 'High', color: 'red' },
  },
  'vi:oxygen-saturation-level': {
    low: { min: 0, max: 94, label: 'Low', color: 'red' },
    optimal: { min: 95, max: 100, label: 'Optimal', color: 'green' },
    high: { min: 101, max: 100, label: 'High', color: 'red' },
  },
  'vi:pulse-rate': {
    low: { min: 0, max: 59, label: 'Low', color: 'blue' },
    optimal: { min: 60, max: 100, label: 'Optimal', color: 'green' },
    high: { min: 101, max: 200, label: 'High', color: 'red' },
  },
  'vi:respiratory-rate': {
    low: { min: 0, max: 11, label: 'Low', color: 'blue' },
    optimal: { min: 12, max: 20, label: 'Optimal', color: 'green' },
    high: { min: 21, max: 60, label: 'High', color: 'red' },
  },
  'vi:body-temperature': {
    low: { min: 0, max: 36.4, label: 'Low', color: 'blue' },
    optimal: { min: 36.5, max: 37.5, label: 'Optimal', color: 'green' },
    high: { min: 37.6, max: 42, label: 'High', color: 'red' },
  },
};

// Gets the status of a vital sign based on its value and reference ranges
export function getVitalStatus(vitalType: string, value: number) {
  const ranges = vitalReferenceRanges[vitalType];
  if (!ranges) return { min: 0, max: 0, label: 'Unknown', color: 'gray' };

  if (value >= ranges.low.min && value <= ranges.low.max) {
    return ranges.low;
  }
  if (value >= ranges.optimal.min && value <= ranges.optimal.max) {
    return ranges.optimal;
  }
  return ranges.high;
}

// Badge style utilities
export function getBadgeStyles(color: string, badgeType: string = 'vital') {
  if (badgeType === 'vital') {
    switch (color) {
      case 'green': // Optimal
        return { bg: '#EDEEFC', color: '#3F51E3' };
      case 'red': // High
        return { bg: '#FFE0D6', color: '#CF0A0A' };
      case 'blue': // Low
        return { bg: '#EDEEFC', color: '#CF0A0A' };
      default:
        return { bg: 'gray.100', color: 'gray.800' };
    }
  }
  // Other status types use standard Chakra colors
  switch (color) {
    case 'Acute':
      return { bg: '#EDEEFC', color: '#3F51E3' };
    case 'Chronic':
      return { bg: '#FFF2DF', color: '#FF6333' };
    case 'Hold':
      return { bg: '#E7E8E9', color: '#14181A' };
    case 'Complete':
      return { bg: '#CBF3E9', color: '#20785C' };
    case 'green':
      return { colorScheme: 'green' };
    case 'red':
      return { colorScheme: 'red' };
    case 'yellow':
      return { colorScheme: 'yellow' };
    case 'blue':
      return { colorScheme: 'blue' };
    case 'gray':
      return { colorScheme: 'gray' };
    default:
      return { colorScheme: 'gray' };
  }
}

// Parses a date string and returns a valid Date object or null
export const parseToValidDate = (dateString?: string): Date | null => {
  if (!dateString) return null;
  try {
    const d = parseISO(dateString);
    return isValid(d) ? d : null;
  } catch {
    return null;
  }
};

// Formats date fields for timeline resources
export const formatDateFields = (resource: any) => {
  const effectiveString =
    resource.effectiveDateString ??
    resource.effectiveDateTime ??
    resource.performedDateTime ??
    resource.issued ??
    resource.meta?.lastUpdated ??
    undefined;

  const dateObj = parseToValidDate(effectiveString);

  return {
    effectiveDateString: effectiveString,
    dateObject: dateObj,
    fullDate: dateObj ? format(dateObj, 'yyyy-MM-dd') : undefined,
    displayDate: dateObj ? format(dateObj, 'dd MMM yyyy') : undefined,
    year: dateObj ? format(dateObj, 'yyyy') : undefined,
    sortKey: dateObj ? dateObj.getTime() : undefined,
  };
};

// Safely formats a date string with fallback
export const formatDateSafe = (date?: string, formatStr: string = 'dd MMM yyyy') => {
  if (!date) return undefined;
  try {
    const parsed = parseISO(date);
    return isValid(parsed) ? format(parsed, formatStr) : undefined;
  } catch {
    return undefined;
  }
};

//  Gets a nested property safely with lodash-like get functionality
export const safeGet = (obj: any, path: string | string[], defaultValue?: any): any => {
  if (!obj || !path) return defaultValue;

  const keys = Array.isArray(path) ? path : path.split('.');
  let result = obj;

  for (let i = 0; i < keys.length; i++) {
    if (result == null || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[keys[i]];
  }

  return result !== undefined ? result : defaultValue;
};
