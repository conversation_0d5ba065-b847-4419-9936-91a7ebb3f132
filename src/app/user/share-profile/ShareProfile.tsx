import React, { useEffect, useMemo, useState } from 'react';
import { Box, Container, Grid, GridItem } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { useSharePatient } from '@user/lib/state';
import { SuspenseLoader } from '@components/SuspenseLoader';

import ProfileCard from './components/ProfileCard';
import CardWrapper from './components/CardWrapper';
import ProfileTabs from './components/ProfileTabs';
import { LAYOUT_HEIGHTS, SmoothTransition, calculateContentHeight } from './components/ShareTimeline/constants';

function MobileShareProfilePage({ patient }: { patient: any }) {
  return (
    <Box>
      <ProfileCard patient={patient} />
      <SuspenseLoader component={() => import('./components/ProfileTabs')} />
    </Box>
  );
}

function useScrollDirection() {
  const [scrollUp, setScrollUp] = useState(false);
  const isTablet = useIsTablet();

  useEffect(() => {
    if (isTablet) {
      setScrollUp(false);
      return () => {};
    }

    const handleScroll = () => {
      const isAtTop = window.scrollY === 0;
      setScrollUp(!isAtTop);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isTablet]);

  return scrollUp;
}

function ShareProfilePage() {
  const isTablet = useIsTablet();
  const scrollDirection = useScrollDirection();
  const scrollUp = !isTablet ? scrollDirection : false;
  const { patient } = useSharePatient();

  const healthSnapshotLoader = useMemo(
    () => <SuspenseLoader component={() => import('./components/HealthSnapshot')} />,
    []
  );

  const healthTimelineLoader = useMemo(
    () => <SuspenseLoader component={() => import('./components/HealthTimeline')} />,
    []
  );

  if (isTablet) {
    return <MobileShareProfilePage patient={patient} />;
  }

  return (
    <Box>
      <Container
        maxW="1200px"
        centerContent
      >
        <ProfileCard
          scrollUp={scrollUp}
          patient={patient}
        />
        <Grid
          templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
          gap={4}
          w="full"
          alignItems="stretch"
          mb={4}
        >
          <GridItem
            h={scrollUp ? LAYOUT_HEIGHTS.CONTAINER_AT_TOP : LAYOUT_HEIGHTS.CONTAINER_SCROLLED}
            transition={SmoothTransition}
          >
            <CardWrapper title="Snapshot">{healthSnapshotLoader}</CardWrapper>
          </GridItem>
          <GridItem
            h={scrollUp ? LAYOUT_HEIGHTS.CONTAINER_AT_TOP : LAYOUT_HEIGHTS.CONTAINER_SCROLLED}
            transition={SmoothTransition}
          >
            <CardWrapper title="Timeline">{healthTimelineLoader}</CardWrapper>
          </GridItem>
          <GridItem
            h={scrollUp ? LAYOUT_HEIGHTS.CONTAINER_AT_TOP : LAYOUT_HEIGHTS.CONTAINER_SCROLLED}
            transition={SmoothTransition}
          >
            <ProfileTabs contentHeight={calculateContentHeight(scrollUp)} />
          </GridItem>
        </Grid>
      </Container>
    </Box>
  );
}

export default ShareProfilePage;
