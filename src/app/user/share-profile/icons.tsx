import * as React from 'react';

// ————————————————————————————————————————————————————————
// SVG Icons as React components
// ————————————————————————————————————————————————————————

export function TimelineDotIcon() {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16 8.5C12.6232 10.3468 9.8468 13.1232 8 16.5C6.1532 13.1232 3.37681 10.3468 0 8.5C3.37681 6.6532 6.1532 3.87681 8 0.5C9.8468 3.87681 12.6232 6.6532 16 8.5Z"
        fill="#5B5D5F"
      />
    </svg>
  );
}

export function KeyHealthTestIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
    >
      <g clipPath="url(#A)">
        <path
          d="M5.109 0v1.289h6.152V0h.908v1.289h1.201V15H3V1.289h1.201V0h.908zm3.066 2.61c-.704.701-1.819 1.651-1.167 2.743.724 1.213 2.663.605 2.555-.798-.055-.713-.885-1.491-1.388-1.945zm3.79 5.271v-.835l-.044-.044H4.45l-.044.044v.835h7.559zm-.703 1.025H5.109v.879h6.152v-.879zm-2.637 1.846h-.879v.967h-.967v.879h.967v.996h.879v-.996h.967v-.879h-.967v-.967z"
          fill="#2c3ec4"
        />
        <path
          d="M8.176 3.867c.077-.013.468.569.495.671.124.479-.513.784-.85.441-.356-.363.114-.829.356-1.111v-.001z"
          fill="#c1dcff"
        />
        <path
          fill="#2c3ec4"
          d="M4 2h8v12H4z"
        />
        <g fill="#fff2df">
          <path d="M8.211 5c.89.803 2.359 2.179 2.456 3.439.19 2.481-3.238 3.555-4.519 1.411C4.994 7.919 6.965 6.24 8.212 5h-.001zm0 2.228c-.428.499-1.259 1.323-.629 1.965.597.607 1.724.068 1.503-.779-.047-.179-.739-1.209-.875-1.186v.001z" />
          <circle
            cx="8.45"
            cy="8.534"
            r="1.768"
          />
        </g>
      </g>
      <defs>
        <clipPath id="A">
          <path
            fill="#fff"
            transform="translate(3)"
            d="M0 0h10.371v15H0z"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function SymptomIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <g clipPath="url(#clip0_22138_90)">
        <path
          d="M6.48373 1C3.27462 1.2359 1.04709 4.13644 1.62698 7.31925C1.86872 8.64591 2.62389 9.78085 3.66462 10.6138L2.82108 12.4528L7.94696 14.9925L8.75289 13.3551H12.1964V9.86995L14 8.9406L12.2088 6.22192C12.1077 3.46781 9.98351 1.24174 7.2378 1.0252L7.19435 1H6.48373ZM6.45634 5.19579V2.8861L10.0642 6.48082L7.27651 7.46458V9.77428L3.66864 6.17956L6.45634 5.19579Z"
          fill="#7EC0AB"
        />
        <path
          d="M6.45658 5.20032L3.66888 6.18408L7.27675 9.77881V7.46911L10.0645 6.48535L6.45658 2.89062V5.20032ZM7.27639 5.81526V4.88591L8.56105 6.17021L6.45622 6.8538V7.78316L5.17155 6.49886L7.27639 5.81526Z"
          fill="#7EC0AB"
        />
        <path
          d="M7.27784 5.81217L5.17337 6.49576L6.45803 7.78043V6.85107L8.5625 6.16748L7.27784 4.88281V5.81217Z"
          fill="#7EC0AB"
        />
        <g clipPath="url(#clip1_22138_90)">
          <path
            d="M7.41942 3.00689C7.97909 2.9433 8.49273 3.3266 8.59567 3.87643C8.64661 5.78626 8.6048 7.70416 8.6171 9.6168L8.62729 9.65931C9.54953 10.7495 8.47165 12.3638 7.09795 11.9264C6.1195 11.6145 5.82649 10.3809 6.47575 9.6168L6.47399 4.06404C6.48488 3.53705 6.89137 3.06697 7.41942 3.00689ZM7.89793 4.30189H7.19527V5.02212H7.89793V4.30189ZM7.89793 5.72477H7.19527V6.445H7.89793V5.72477ZM7.89793 7.14766H7.19527V9.94951C6.4768 10.3985 6.93599 11.4978 7.77426 11.2487C8.35325 11.0766 8.41649 10.2408 7.89793 9.94951V7.14766Z"
            fill="#495AE4"
          />
          <path
            d="M7.89629 7.14844V9.95029C8.41485 10.2415 8.35161 11.0774 7.77262 11.2495C6.93435 11.4986 6.47551 10.3993 7.19363 9.95029V7.14844H7.89629Z"
            fill="#FFF2DF"
          />
          <path
            d="M7.89797 4.30469H7.19531V5.02491H7.89797V4.30469Z"
            fill="#FFF2DF"
          />
          <path
            d="M7.89992 5.72656H7.19727V6.44679H7.89992V5.72656Z"
            fill="#FFF2DF"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_22138_90">
          <rect
            width="12.465"
            height="13.9925"
            fill="white"
            transform="matrix(-1 0 0 1 14 1)"
          />
        </clipPath>
        <clipPath id="clip1_22138_90">
          <rect
            width="2.8419"
            height="9"
            fill="white"
            transform="translate(6.12891 3)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function RespiratoryRateIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <g clipPath="url(#clip0_22138_3039)">
        <path
          d="M3.69336 11.1601V10.3132H4.8226L5.38723 9.74855L4.25798 8.61931L4.8226 8.05469L6.23416 9.18393L7.08109 8.05469V9.18393L5.38723 10.8778V12.007H4.54029V11.1601H3.69336Z"
          fill="#B6BDF4"
          stroke="#B6BDF4"
          strokeWidth="0.564622"
        />
        <path
          d="M13.291 11.1601V10.3132H12.1618L11.5971 9.74855L12.7264 8.61931L12.1618 8.05469L10.7502 9.18393L9.90328 8.05469V9.18393L11.5971 10.8778V12.007H12.4441V11.1601H13.291Z"
          fill="#B6BDF4"
          stroke="#B6BDF4"
          strokeWidth="0.564622"
        />
        <path
          d="M9.68555 5.00684C10.3017 5.00713 10.9071 4.98275 11.5117 5.09082C13.3479 5.41848 14.7575 7.03691 14.9473 8.87402L14.9619 9.05273L14.96 13.958C14.5656 13.9417 14.1619 13.9535 13.7598 13.9639C13.348 13.9745 12.9375 13.9832 12.5391 13.96C11.6864 13.9102 10.8749 13.5015 10.3408 12.8389C9.91001 12.3043 9.75147 11.7287 9.69824 11.125C9.67164 10.823 9.67071 10.5139 9.67578 10.1992C9.68056 9.90267 9.68907 9.60061 9.68555 9.2959L11.5879 10.9141V12.0312H12.3994V11.1436H13.2871V10.332H12.1426L11.4717 9.75781L12.541 8.65723L12.543 8.65918L12.5469 8.64844C12.5549 8.62658 12.5541 8.60773 12.5469 8.59082C12.5408 8.57648 12.529 8.56306 12.5225 8.55371H12.5234C12.5186 8.54647 12.5079 8.53455 12.4951 8.52051C12.4818 8.50587 12.4642 8.48759 12.4443 8.4668C12.4045 8.42519 12.3538 8.37334 12.3027 8.32227C12.2518 8.27129 12.2007 8.22037 12.1592 8.18066C12.1386 8.161 12.12 8.14404 12.1055 8.13086C12.0916 8.11831 12.0795 8.10746 12.0723 8.10254C12.069 8.10036 12.0655 8.09777 12.0615 8.09473C12.0577 8.09179 12.0521 8.08819 12.0469 8.08496C12.036 8.07822 12.0218 8.07202 12.0039 8.07324L11.9951 8.07422L11.9883 8.08105L10.8379 9.2002L9.68555 8.23926V5.00684ZM6.43457 5.00195C6.72179 5.00221 7.01275 5.01168 7.30078 5.00488V8.23926L6.14844 9.19922L4.99902 8.08008L4.99219 8.07324H4.9834C4.96537 8.07201 4.95049 8.07717 4.93945 8.08398C4.9343 8.08718 4.92964 8.09079 4.92578 8.09375C4.92165 8.09692 4.91832 8.09929 4.91504 8.10156C4.90781 8.10643 4.8958 8.11721 4.88184 8.12988C4.86729 8.14309 4.84878 8.16089 4.82812 8.18066C4.78665 8.22037 4.73548 8.27135 4.68457 8.32227C4.63363 8.3732 4.58273 8.42429 4.54297 8.46582C4.52315 8.48653 4.50545 8.50493 4.49219 8.51953C4.47951 8.5335 4.4688 8.54547 4.46387 8.55273V8.55371C4.45964 8.56011 4.45695 8.56318 4.45215 8.57031C4.44829 8.57607 4.44467 8.5833 4.44141 8.59082C4.43416 8.60761 4.43226 8.6264 4.44043 8.64844L4.4375 8.64941L4.44531 8.65723L5.5127 9.75586L4.84473 10.3311H3.7002V11.1426H4.58789V12.0303H5.39941V10.9004L7.2998 9.29492C7.30024 9.63595 7.30125 9.97797 7.2998 10.3193C7.29752 10.9655 7.26805 11.5361 7.09766 12.0449C6.92761 12.5526 6.6168 13 6.05176 13.3994C5.44903 13.8253 4.78841 13.9458 4.10156 13.9688C3.75785 13.9802 3.40761 13.9667 3.05566 13.9561C2.71281 13.9457 2.36776 13.9376 2.02539 13.9561V9C2.15201 7.08954 3.66616 5.39584 5.55078 5.06348C5.84175 5.01213 6.13753 5.0017 6.43457 5.00195ZM8.84863 1.02539V6.82031C8.84869 6.87643 8.87112 6.97884 8.89746 7.07812C8.92417 7.17876 8.95621 7.281 8.97656 7.33691C9.09753 7.6702 9.34012 8.03273 9.63477 8.23926V9.19043C9.63371 9.18985 9.63211 9.18951 9.63086 9.18848C9.62754 9.18571 9.62449 9.18169 9.61914 9.17578L9.59961 9.15723L9.3418 8.94922C9.25274 8.87329 9.16594 8.79446 9.09473 8.72754C8.87522 8.52166 8.67676 8.29739 8.52832 8.03613L8.50977 8.00293L8.48633 8.0332L8.15918 8.46191C7.94893 8.67267 7.73175 8.88101 7.50098 9.06836C7.47319 9.09112 7.44315 9.11774 7.41113 9.1416C7.39126 9.15639 7.37105 9.16759 7.35254 9.17676V8.2373C7.67436 8.00789 7.92946 7.61229 8.04199 7.23828C8.05601 7.19164 8.08023 7.10894 8.10059 7.02734C8.12063 6.947 8.1377 6.86366 8.1377 6.81836V1.02539H8.84863Z"
          fill="#20785C"
          stroke="#B6BDF4"
          strokeWidth="0.0507198"
        />
      </g>
      <defs>
        <clipPath id="clip0_22138_3039">
          <rect
            width="12.9863"
            height="13"
            fill="white"
            transform="translate(2 1)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ProcedureIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M11.7978 8.49182V1H5.25072L4.58911 1.8993C4.4374 1.8533 4.3317 1.76676 4.17013 1.72514C3.7873 1.6271 3.42856 1.65777 3.07092 1.82865C2.59991 2.05375 2.36057 2.57406 2.06811 2.9788L5.15214 5.12792C5.38052 4.70292 5.80936 4.3294 5.86194 3.82717C5.9189 3.28331 5.73761 2.71591 5.26441 2.42125L5.66039 1.82044H10.9773V8.27385H8.40759H7.58716H5.60507C4.77697 7.71575 3.98611 7.09248 3.15088 6.54534C2.96796 6.4254 2.81899 6.30491 2.6021 6.2463C1.29367 5.89414 0.416825 7.51311 1.46291 8.42446L5.03055 10.9258H6.49288V12.4845H7.58661V14.1796H5.09901V15H10.8946V14.1796H8.40705V12.4845H9.50078V10.9258H10.9363C11.6318 11.3743 12.3017 11.9565 13.0027 12.3859C14.4398 13.2666 15.7368 11.4351 14.5017 10.3671L11.7978 8.49182Z"
        fill="#FF6333"
      />
      <path
        d="M11.7941 8.49182V1H5.24707L4.58546 1.8993C4.43375 1.8533 4.32805 1.76676 4.16648 1.72514C3.78365 1.6271 3.42491 1.65777 3.06727 1.82865C2.59626 2.05375 2.35692 2.57405 2.06445 2.9788L5.14848 5.12792C5.37687 4.70292 5.80571 4.3294 5.85829 3.82717C5.91525 3.28331 5.73396 2.71591 5.26076 2.42125L5.65674 1.82044H10.9737V8.27384L11.7941 8.49182Z"
        fill="#495AE4"
      />
    </svg>
  );
}

export function ImmunizationIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <g clipPath="url(#clip0_22138_3505)">
        <path
          d="M6.95984 1L6.97265 4.34643C7.07395 4.94947 7.64673 5.09917 7.94159 5.58626C8.04327 5.75418 8.22855 6.20676 8.22855 6.39562V11.5H4.8457V6.39562C4.8457 6.20676 5.03098 5.75418 5.13266 5.58626C5.42828 5.09801 6.00106 4.95296 6.10161 4.34643L6.11441 1H6.96022L6.95984 1Z"
          fill="#495AE4"
        />
        <path
          d="M4.84581 15.0038V14.1312H6.11414V12.357H4V11.4844H9.07408V12.357H6.95994V14.1312H8.22828V15.0038H4.84581Z"
          fill="#495AE4"
        />
        <path
          d="M9.69926 5.40147C12.1105 5.19787 13.7603 7.90324 12.4792 10.044C11.1088 12.3344 7.72785 11.8038 7.09142 9.20939C6.64517 7.39094 7.88338 5.55504 9.69926 5.40147ZM11.0391 7.38978C10.9906 7.34363 10.9781 7.22651 10.8735 7.31066L9.54599 8.67731L8.98978 8.15803L8.41022 8.77737L9.54599 9.928L11.4948 7.92302L11.5027 7.84119C11.3442 7.69576 11.1958 7.5387 11.0391 7.38978Z"
          fill="#289673"
        />
        <path
          d="M11.0391 7.39C11.1957 7.53892 11.3437 7.69598 11.5027 7.84141L11.4948 7.92324L9.54593 9.92822L8.41016 8.77759L8.98972 8.15825L9.54593 8.67753L10.8734 7.31088C10.9781 7.22673 10.9909 7.34385 11.0391 7.39Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_22138_3505">
          <rect
            width="11"
            height="14"
            fill="white"
            transform="translate(4 1)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function LabResultsIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <g clipPath="url(#clip0_22138_7173)">
        <path
          d="M6.78753 4.30857V3.7617H10.0888V6.03123H6.78753V5.12889H5.58473C5.45268 5.57404 5.13637 5.87264 4.74022 6.0449V14.1797H12.52V15H3V14.1797H3.97247V6.00389C2.91913 5.67029 2.77531 4.09912 3.74164 3.52873C4.47969 3.09342 5.31295 3.50084 5.59753 4.30857H6.78753Z"
          fill="#FF6333"
        />
        <path
          d="M10.0884 6.85156V11.5957C10.0884 12.5024 9.12002 13.2577 8.31031 13.1948C7.626 13.1417 6.78711 12.422 6.78711 11.6504V6.85156H10.0884Z"
          fill="#FF6333"
        />
        <path
          d="M8.80876 1H8.04102V2.33984H8.80876V1Z"
          fill="#6D7BE9"
        />
        <path
          d="M7.55485 1.82031H6.78711V3.16016H7.55485V1.82031Z"
          fill="#6D7BE9"
        />
        <path
          d="M10.0881 1.82031H9.32031V3.16016H10.0881V1.82031Z"
          fill="#6D7BE9"
        />
      </g>
      <defs>
        <clipPath id="clip0_22138_7173">
          <rect
            width="9.52"
            height="14"
            fill="white"
            transform="translate(3 1)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AppointmentIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <path
        d="M12.309 6.715a3.11 3.11 0 0 1-2.215-.917c-.593-.592-.921-1.381-.921-2.219 0-.623.183-1.227.53-1.744l.028-.038H1v12.888h12.888V6.287c-.479.279-1.02.428-1.579.428z"
        fill="#20785c"
      />
      <g fill="#fff">
        <path d="M3.423 9.307a.43.43 0 1 0 0-.861.43.43 0 0 0 0 .861z" />
        <path d="M7.442 9.307a.43.43 0 1 0 0-.861.43.43 0 0 0 0 .861z" />
        <path d="M9.452 9.307a.431.431 0 0 0 0-.861.431.431 0 1 0 0 .861z" />
        <path d="M11.464 9.307a.431.431 0 1 0 0-.861.431.431 0 0 0 0 .861z" />
        <path d="M3.423 7.549a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M7.442 7.549a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M5.435 7.549a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M9.454 7.549a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M11.466 7.549a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M3.423 11.072a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M5.435 11.072a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M7.442 11.072a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M9.454 11.072a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M11.466 11.072a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M3.423 12.838a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M5.435 12.838a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M7.442 12.838a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M9.454 12.838a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
        <path d="M11.466 12.838a.43.43 0 1 0 0-.861.43.43 0 1 0 0 .861z" />
      </g>
      <g fill="#b6bdf4">
        <path d="M6.106 8.656H4.764v.447h1.342v-.447z" />
        <path d="M5.658 9.553V8.211h-.447v1.342h.447z" />
      </g>
      <path
        d="M12.309 6.152a2.58 2.58 0 0 0 1.431-.434c.424-.283.754-.686.948-1.156s.246-.988.146-1.489-.345-.959-.705-1.319-.82-.606-1.319-.705-1.018-.048-1.489.146a2.58 2.58 0 0 0-1.156.948c-.283.424-.434.922-.434 1.431 0 .683.271 1.338.754 1.821s1.139.754 1.821.754l.001.001z"
        fill="#929cef"
      />
      <path
        d="M11.761 5.025l-1.329-1.131.436-.512.856.728 1.987-1.986.475.475-2.425 2.425z"
        fill="#fff"
      />
    </svg>
  );
}

export function ConditionIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      fill="none"
      viewBox="0 0 20 20"
    >
      <path
        d="M13.763 4c.57 0 1.156.127 1.743.36 1.411.561 2.249 1.644 2.453 3.17s-.344 2.826-1.246 4.005c-1.039 1.357-2.354 2.412-3.769 3.336l-2.724 1.612c-.061.035-.153.057-.236.057a.3.3 0 0 1-.137-.029c-2.109-1.105-4.11-2.367-5.782-4.091-.959-.988-1.701-2.099-1.982-3.488-.472-2.345 1.102-4.556 3.45-4.858.226-.029.443-.045.656-.045 1.571 0 2.73.803 3.606 2.182l.153.258c.487-.542.908-1.137 1.45-1.587.733-.612 1.536-.882 2.367-.882"
        fill="#ff6333"
      />
      <g fill="#fff">
        <path d="M13 9.5H7v2.001h6V9.5z" />
        <path d="M11.001 13.5v-6H9v6h2.001z" />
      </g>
    </svg>
  );
}

export function OxygenSaturationIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <path
        d="M5.638 2l3.283 3.283a4.64 4.64 0 0 1 1.007 5.058 4.64 4.64 0 0 1-4.288 2.866 4.64 4.64 0 0 1-4.288-2.866A4.64 4.64 0 0 1 2.36 5.283L5.638 2z"
        fill="#289673"
      />
      <g fill="#d4eae3">
        <path d="M6.961 10.497v-.169l.7-.854c.101-.121.13-.227.13-.328 0-.13-.077-.198-.183-.198s-.208.053-.246.256l-.386-.111c.039-.323.299-.545.632-.545.372 0 .632.222.632.589 0 .198-.087.348-.193.478l-.396.483h.618v.401H6.961z" />
        <path d="M5.093 9.956c-.854 0-1.593-.555-1.593-1.728S4.239 6.5 5.093 6.5s1.593.555 1.593 1.728-.739 1.728-1.593 1.728zm0-.579c.507 0 .956-.348.956-1.149S5.6 7.079 5.093 7.079s-.956.348-.956 1.149.449 1.149.956 1.149z" />
      </g>
      <g fill="#144037">
        <circle
          cx="11"
          cy="9"
          r="2"
        />
        <circle
          cx="9"
          cy="4"
          r="2"
        />
        <circle
          cx="13.5"
          cy="4.5"
          r="1.5"
        />
      </g>
    </svg>
  );
}

export function BloodPressureIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <g clipPath="url(#clip0)">
        <path
          d="M5.661 9.972l.003 2.603c.074.661.883.916 1.289.409.343-.43.064-1.414.221-1.981a1.09 1.09 0 0 1 1.741-.548c.859.728-.044 2.251.776 2.699.485.265 1.028-.043 1.089-.602l.002-3.176c-.82-.188-1.413-.905-1.465-1.78-.039-.659-.076-1.956.216-2.523.759-1.47 2.883-1.265 3.356.322.139.467.12 1.704.086 2.226-.055.835-.664 1.601-1.463 1.741l-.002 3.265c-.187 1.843-2.787 1.82-2.926-.024-.028-.375.068-1.039-.011-1.362a.37.37 0 0 0-.714.019c-.073.376.031.978-.008 1.39-.171 1.782-2.701 1.805-2.916.021l-.007-2.7c-1.565-.136-2.844-1.559-2.927-3.19-.062-1.217.638-2.498 1.297-3.454C3.884 2.484 4.569 1.72 5.287 1c.637.611 1.244 1.289 1.774 2.009.747 1.016 1.594 2.445 1.527 3.774-.082 1.629-1.361 3.055-2.926 3.19v-.001zm.918-2.017c.888-.909.648-2.494-.463-3.072-1.565-.814-3.239.932-2.459 2.564.547 1.144 2.04 1.409 2.921.508v-.001z"
          fill="#144037"
        />
        <path
          d="M6.58 7.958c-.88.902-2.374.636-2.921-.508-.781-1.632.894-3.378 2.459-2.564 1.111.578 1.35 2.163.463 3.072h0zm-.966-2.457c-1.138-.366-1.941 1.206-1.011 1.982.98.818 2.272-.422 1.62-1.525l-.646.863-.572-.469.608-.851z"
          fill="#d4eae3"
        />
        <path
          d="M5.614 5.501l-.608.851.572.469.646-.863c.651 1.103-.641 2.343-1.62 1.525s-.127-2.349 1.011-1.982z"
          fill="#929cef"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <path
            fill="#fff"
            transform="translate(2 1)"
            d="M0 0h11v13H0z"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function PulseRateIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <path
        d="M11.319 2.5c.5 0 1.015.112 1.529.316 1.238.492 1.973 1.442 2.152 2.781s-.302 2.479-1.093 3.513c-.911 1.191-2.065 2.116-3.306 2.926L8.211 13.45c-.053.031-.134.05-.207.05-.045 0-.089-.008-.12-.025-1.85-.97-3.605-2.076-5.072-3.588-.841-.866-1.492-1.842-1.738-3.06C.66 4.769 2.04 2.83 4.1 2.564c.198-.025.388-.039.576-.039 1.378 0 2.395.704 3.164 1.914l.134.226c.428-.475.796-.998 1.272-1.392.643-.537 1.347-.774 2.076-.774"
        fill="#20785c"
      />
      <mask
        id="pulseMask"
        maskUnits="userSpaceOnUse"
        x="1"
        y="2"
        width="14"
        height="12"
      >
        <path
          d="M11.292 2.5c.499 0 1.012.112 1.525.316 1.235.492 1.968 1.442 2.146 2.781s-.301 2.479-1.09 3.513c-.909 1.191-2.06 2.116-3.298 2.926L8.193 13.45c-.053.031-.134.05-.206.05-.045 0-.089-.008-.12-.025-1.845-.97-3.596-2.076-5.059-3.588-.839-.866-1.489-1.842-1.734-3.06-.413-2.057.965-3.996 3.019-4.262a4.54 4.54 0 0 1 .574-.039c1.374 0 2.389.704 3.156 1.914l.134.226c.426-.475.794-.998 1.268-1.392.641-.537 1.344-.774 2.071-.774"
          fill="#ff6333"
        />
      </mask>
      <g mask="url(#pulseMask)">
        <path
          d="M-1 7.602h7.922V6.5l1.653 2.756.551-1.653h7.643"
          stroke="#dadcff"
        />
      </g>
    </svg>
  );
}

export function BodyTemperatureIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <g clipPath="url(#bodyTempClip)">
        <path
          d="M8.353 3.579c.711-.081 1.363.406 1.493 1.104l.027 7.288.013.054c1.171 1.384-.198 3.434-1.942 2.878-1.242-.396-1.614-1.962-.79-2.932l-.002-7.05c.014-.669.53-1.266 1.2-1.342zm.608 1.644h-.892v.914h.892v-.914zm0 1.807h-.892v.914h.892V7.03zm0 1.807h-.892v3.557c-.912.57-.329 1.966.735 1.65.735-.219.815-1.28.157-1.65V8.836z"
          fill="#289673"
        />
        <g fill="#b6bdf4">
          <path d="M8.961 8.836v3.557c.658.37.578 1.431-.157 1.649-1.064.316-1.647-1.079-.735-1.649V8.836h.892z" />
          <path d="M8.96 5.227h-.892v.914h.892v-.914zm0 1.804h-.892v.914h.892v-.914z" />
        </g>
      </g>
      <path
        d="M6.263 2.556l.467-.401.794.923-.467.401-.794-.923zm2.218-1.312l.404 1.113-.552.212-.431-1.118c.007-.03.553-.229.578-.207h0zM5.039 4.132l.293-.511 1.039.597-.294.512-1.039-.599z"
        fill="#b6bdf4"
      />
      <defs>
        <clipPath id="bodyTempClip">
          <path
            fill="#fff"
            transform="translate(6.715 3.57)"
            d="M0 0h3.608v11.427H0z"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
