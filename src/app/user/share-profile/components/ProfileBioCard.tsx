// Package modules
import { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import { ChakraProps, Flex, Grid, GridItem, Heading, Skeleton } from '@chakra-ui/react';
// Local modules
import { ActiveCard } from '@src/types/profile';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { enumUnit } from '@user/lib/constants';
import dayjs from 'dayjs';
import { getAge } from '@utils/utils';
import { medplumApi } from '@user/lib/medplum-api';
import { SelectOptionProps } from '@components/ui/Select';
import { useSearchParams } from 'react-router-dom';

import { FACT_CODE_SYSTEM } from '@lib/constants';
import { convertCmToFeetAndInches, convertWeightKgToLbs } from '@lib/utils/observationsUtils';
import { ProfileBioCardItem, ProfileBioCardItemNoneValue } from '../../profile/components/ProfileBioCard';
import { SmoothTransition } from './ShareTimeline/constants';

export function ProfileBioCardSkeleton() {
  const skeletons = Array.from({ length: 6 }, (_, index) => (
    <Skeleton
      key={index}
      width="calc(50% - 1px)"
      height="62px"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.500"
    />
  ));

  return (
    <Flex
      wrap="wrap"
      gap="2px"
      bgColor="white"
      border="1px solid"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      px="4px"
      py="4px"
    >
      {skeletons}
    </Flex>
  );
}

function ProfileBioCardItemValue(props: PropsWithChildren<ChakraProps>) {
  return (
    <Heading
      fontSize={{ base: 'md', md: 'xl' }}
      lineHeight="short"
      color="white"
      {...props}
    />
  );
}
function MobileProfileBioCard(props: any) {
  return (
    <Grid
      margin="20px 0px"
      borderRadius="16px"
      bg="fluentHealth.500"
      templateColumns="repeat(2, 1fr)"
      templateRows="repeat(3, 88px)"
    >
      <ProfileBioCardItem
        title="Height"
        label={props?.heightCm || ''}
        borderBottom="1px solid"
        borderRight="1px solid"
        cardType={ActiveCard.Height}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          <Heading
            fontSize={{ base: 'md', md: 'xl' }}
            lineHeight="short"
            color="white"
          >
            {props?.heightInches ? (
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <Heading
                  fontSize={{ base: 'md', md: 'xl' }}
                  lineHeight="short"
                  color="white"
                >
                  {props?.heightInches}
                </Heading>
              </Flex>
            ) : (
              <ProfileBioCardItemNoneValue />
            )}
          </Heading>
        </Flex>
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Weight"
        label={props?.weightLbs || ''}
        borderBottom="1px solid"
        borderRight="1px solid"
        isInputField
        showClearButton
        cardType={ActiveCard.Weight}
      >
        {props?.weightKg ? (
          <Flex
            direction="row"
            paddingRight="6"
            height="14"
            justifyContent="space-between"
          >
            <Heading
              fontSize={{ base: 'md', md: 'xl' }}
              lineHeight="short"
              color="white"
            >
              {props?.weightKg}
            </Heading>
          </Flex>
        ) : (
          <ProfileBioCardItemNoneValue />
        )}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Age"
        label={props?.patient?.birthDate ? dayjs(props?.patient.birthDate).format('DD/MM/YYYY') : undefined}
        borderRight="1px solid"
        borderBottom="1px solid"
      >
        {props?.patient?.birthDate ? (
          <ProfileBioCardItemValue>{getAge(props?.patient?.birthDate)}</ProfileBioCardItemValue>
        ) : (
          <ProfileBioCardItemNoneValue />
        )}
      </ProfileBioCardItem>
      {/* Scroll hide */}

      <ProfileBioCardItem
        title="Blood group"
        borderBottom="1px solid"
        borderRight="1px solid"
        cardType={ActiveCard.BloodType}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {props?.patient?.bloodType?.[0]?.valueCodeableConcept?.text ? (
            <ProfileBioCardItemValue textTransform="uppercase">
              {props?.patient?.bloodType?.[0]?.valueCodeableConcept?.text}
            </ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Gender"
        borderRight="1px solid"
        borderBottom="1px solid"
        cardType={ActiveCard.Gender}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {props?.genderIdentity ? (
            <ProfileBioCardItemValue
              isTruncated
              maxWidth="120px"
              textTransform="capitalize"
            >
              {props?.genderIdentity}
            </ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Sex assigned at birth"
        borderBottom="1px solid"
        cardType={ActiveCard.PatientGenderAtBirth}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {props?.patientGenderAtBirth ? (
            <ProfileBioCardItemValue isTruncated>{props?.patientGenderAtBirth}</ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <GridItem colSpan={2}>
        <ProfileBioCardItem
          height="88px"
          title="Ethnicity"
          cardType={ActiveCard.Ethnicity}
        >
          <Flex
            direction="row"
            paddingRight="6"
            height="14"
            justifyContent="space-between"
          >
            {props?.ethnicity ? (
              <ProfileBioCardItemValue isTruncated>{props?.ethnicity}</ProfileBioCardItemValue>
            ) : (
              <ProfileBioCardItemNoneValue />
            )}
          </Flex>
        </ProfileBioCardItem>
      </GridItem>
    </Grid>
  );
}

// eslint-disable-next-line complexity
export function ProfileBioCard({ scrollUp, patient }: { scrollUp?: boolean; patient: any }) {
  const [ethnicityGrp, setEthnicityGrp] = useState<SelectOptionProps[]>([]);
  const [genderGrp, setGenderGrp] = useState<SelectOptionProps[]>([]);
  const [sexAssigned, setSexAssigned] = useState<SelectOptionProps[]>([]);
  const [searchParams] = useSearchParams();

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromShareDirectus(
        true,
        FACT_CODE_SYSTEM.ETHNICITY,
        searchParams.get('access_token')
      ),
      medplumApi.valueSetList.getAllValueSetFromShareDirectus(
        true,
        FACT_CODE_SYSTEM.GENDER,
        searchParams.get('access_token')
      ),
      medplumApi.valueSetList.getAllValueSetFromShareDirectus(
        true,
        FACT_CODE_SYSTEM.SEX_ASSIGNED,
        searchParams.get('access_token')
      ),
    ]).then(([inEthnicity, Gender, Sex_Assigned]) => {
      setEthnicityGrp(inEthnicity.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setGenderGrp(Gender.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setSexAssigned(Sex_Assigned.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
    });
  }, []);
  const heightCm = patient?.height?.[0]?.valueQuantity?.value
    ? `${parseFloat(patient?.height?.[0]?.valueQuantity.value).toFixed(2)} ${patient?.height?.[0]?.valueQuantity?.unit}`
    : null;
  const weightKg = patient?.weight?.[0]?.valueQuantity.value
    ? `${parseFloat(patient?.weight?.[0]?.valueQuantity.value).toFixed(2)} ${patient?.weight?.[0]?.valueQuantity?.unit}`
    : null;
  const heightInches = patient?.height?.[0]?.valueQuantity.value
    ? convertCmToFeetAndInches(patient?.height?.[0]?.valueQuantity.value)
    : null;
  const weightLbs = patient?.weight?.[0]?.valueQuantity.value
    ? `${convertWeightKgToLbs(patient?.weight?.[0]?.valueQuantity.value)} ${enumUnit.POUNDS_TEXT}`
    : null;
  const genderIdentity = genderGrp.find((item) => item.value === patient?.genderIdentity?.[0]?.valueCode)?.label;
  const patientGenderAtBirth: string = useMemo(() => {
    const found = sexAssigned.find((item) => item.value === patient?.gender)?.label;
    return typeof found === 'string' ? found : found !== undefined ? String(found) : '';
  }, [patient?.gender, sexAssigned]);
  const ethnicity = ethnicityGrp.find((item) => item.value === patient?.ethnicity?.[0]?.valueCode)?.label;

  const isTablet = useIsTablet();
  if (isTablet) {
    return (
      <MobileProfileBioCard
        heightCm={heightCm}
        weightKg={weightKg}
        heightInches={heightInches}
        weightLbs={weightLbs}
        genderIdentity={genderIdentity}
        patientGenderAtBirth={patientGenderAtBirth}
        ethnicity={ethnicity}
        patient={patient}
      />
    );
  }
  return (
    <Grid
      width="70%"
      templateColumns="repeat(3, 1fr)"
      templateRows={scrollUp ? 'repeat(1, 88px)' : 'repeat(3, 88px)'}
    >
      <ProfileBioCardItem
        title="Height"
        label={heightCm || ''}
        borderBottom={scrollUp ? 'none' : '1px solid'}
        borderRight="1px solid"
        cardType={ActiveCard.Height}
        transition={SmoothTransition}
      >
        {heightInches ? (
          <Flex
            direction="row"
            paddingRight="6"
            height="14"
            justifyContent="space-between"
          >
            <Heading
              fontSize={{ base: 'md', md: 'xl' }}
              lineHeight="short"
              color="white"
            >
              {heightInches}
            </Heading>
          </Flex>
        ) : (
          <ProfileBioCardItemNoneValue />
        )}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Weight"
        label={weightLbs || ''}
        borderBottom={scrollUp ? 'none' : '1px solid'}
        borderRight="1px solid"
        isInputField
        showClearButton
        cardType={ActiveCard.Weight}
        transition={SmoothTransition}
      >
        {weightKg ? (
          <Flex
            direction="row"
            paddingRight="6"
            height="14"
            justifyContent="space-between"
          >
            <Heading
              fontSize={{ base: 'md', md: 'xl' }}
              lineHeight="short"
              color="white"
            >
              {weightKg}
            </Heading>
          </Flex>
        ) : (
          <ProfileBioCardItemNoneValue />
        )}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Age"
        label={patient?.birthDate ? dayjs(patient.birthDate).format('DD/MM/YYYY') : undefined}
        borderBottom={scrollUp ? 'none' : '1px solid'}
        transition={SmoothTransition}
      >
        {patient?.birthDate ? (
          <ProfileBioCardItemValue>{getAge(patient?.birthDate)}</ProfileBioCardItemValue>
        ) : (
          <ProfileBioCardItemNoneValue />
        )}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Blood group"
        borderBottom={scrollUp ? 'none' : '1px solid'}
        borderRight={scrollUp ? 'none' : '1px solid'}
        cardType={ActiveCard.BloodType}
        height={scrollUp ? '0px' : '88px'}
        visibility={scrollUp ? 'hidden' : 'visible'}
        opacity={scrollUp ? 0 : 1}
        transition={SmoothTransition}
        overflow="hidden"
        padding={scrollUp ? '0' : undefined}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {patient?.bloodType?.[0]?.valueCodeableConcept?.text ? (
            <ProfileBioCardItemValue textTransform="uppercase">
              {patient?.bloodType?.[0]?.valueCodeableConcept?.text}
            </ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Gender"
        borderRight={scrollUp ? 'none' : '1px solid'}
        borderBottom={scrollUp ? 'none' : '1px solid'}
        cardType={ActiveCard.Gender}
        height={scrollUp ? '0px' : '88px'}
        visibility={scrollUp ? 'hidden' : 'visible'}
        opacity={scrollUp ? 0 : 1}
        transition={SmoothTransition}
        overflow="hidden"
        padding={scrollUp ? '0' : undefined}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {genderIdentity ? (
            <ProfileBioCardItemValue
              isTruncated
              maxWidth="120px"
              textTransform="capitalize"
            >
              {genderIdentity}
            </ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Sex assigned at birth"
        borderBottom={scrollUp ? 'none' : '1px solid'}
        cardType={ActiveCard.PatientGenderAtBirth}
        height={scrollUp ? '0px' : '88px'}
        visibility={scrollUp ? 'hidden' : 'visible'}
        opacity={scrollUp ? 0 : 1}
        transition={SmoothTransition}
        overflow="hidden"
        padding={scrollUp ? '0' : undefined}
      >
        <Flex
          direction="row"
          paddingRight="6"
          height="14"
          justifyContent="space-between"
        >
          {patientGenderAtBirth ? (
            <ProfileBioCardItemValue isTruncated>{patientGenderAtBirth}</ProfileBioCardItemValue>
          ) : (
            <ProfileBioCardItemNoneValue />
          )}
        </Flex>
      </ProfileBioCardItem>
      <GridItem
        colSpan={3}
        height={scrollUp ? '0px' : '88px'}
        visibility={scrollUp ? 'hidden' : 'visible'}
        opacity={scrollUp ? 0 : 1}
        transition={SmoothTransition}
        overflow="hidden"
        padding={scrollUp ? '0' : undefined}
      >
        <ProfileBioCardItem
          height="88px"
          title="Ethnicity"
          cardType={ActiveCard.Ethnicity}
        >
          <Flex
            direction="row"
            paddingRight="6"
            height="14"
            justifyContent="space-between"
          >
            {ethnicity ? (
              <ProfileBioCardItemValue isTruncated>{ethnicity}</ProfileBioCardItemValue>
            ) : (
              <ProfileBioCardItemNoneValue />
            )}
          </Flex>
        </ProfileBioCardItem>
      </GridItem>
    </Grid>
  );
}
