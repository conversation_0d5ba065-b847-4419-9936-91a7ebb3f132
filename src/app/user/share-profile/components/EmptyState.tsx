import React from 'react';
import { Text, VStack } from '@chakra-ui/react';

interface EmptyStateProps {
  message: string;
  margin?: string;
}

function EmptyState({ message, margin }: EmptyStateProps) {
  return (
    <VStack
      bg="white"
      borderRadius="4px"
      p={6}
      spacing={4}
      boxShadow="sm"
      margin={margin}
    >
      <Text
        fontSize="20px"
        fontFamily="P22 Mackinac"
        color="charcoal.70"
      >
        {message}
      </Text>
    </VStack>
  );
}

export default EmptyState;
