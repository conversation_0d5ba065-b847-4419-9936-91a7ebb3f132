import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

import { ShareTimeline } from './ShareTimeline';

export default function HealthTimeline() {
  return (
    <Flex direction="column">
      <Text
        textAlign="left"
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mb="8px"
      >
        My Health Timeline
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pb="24px"
      >
        Connect doctor visits, symptoms, and reports chronologically to uncover patterns and track your progress.
      </Text>
      <ShareTimeline />
    </Flex>
  );
}
