import React, { useCallback } from 'react';
import { Box, Flex, Text, VStack } from '@chakra-ui/react';
import { useSharePatient } from '@user/lib/state';
import _ from 'lodash';

import ResourceGroup from './components/ResourceGroup';
import { useTimelineData } from './hooks/useTimelineData';
import { ResourceTypeKey } from './constants';
import { TimelineDotIcon } from '../../icons';
import { DayEntry, YearGroup } from './types';
import EmptyState from '../EmptyState';

function ShareTimeline(): JSX.Element {
  const { patient } = useSharePatient();
  const timelineData = useTimelineData(patient?.id);

  const renderOrderedResourceGroups = useCallback((dayEntry: DayEntry) => {
    return _(dayEntry.orderedResourceTypes)
      .map((resourceType) => {
        const entries = _.get(dayEntry, ['resources', resourceType], []);
        if (!entries.length) return null;

        return (
          <ResourceGroup
            key={resourceType}
            resourceType={resourceType as ResourceTypeKey}
            entries={entries}
            dayDateText={dayEntry.date}
          />
        );
      })
      .compact()
      .value();
  }, []);

  if (!timelineData || timelineData.length === 0)
    return (
      <Flex direction="column">
        <EmptyState message="No timeline entries yet" />
      </Flex>
    );

  return (
    <Box>
      <VStack
        spacing={0}
        align="stretch"
        textAlign="left"
      >
        {timelineData.map((yearGroup: YearGroup) => (
          <Box key={yearGroup.year}>
            <Text
              fontSize="md"
              fontWeight="bold"
              bg="charcoal.70"
              display="inline-block"
              p="2px 8px"
              borderRadius="17px"
              letterSpacing="-0.32px"
              color="white"
              lineHeight="1"
            >
              {yearGroup.year}
            </Text>

            <VStack
              spacing={0}
              align="stretch"
            >
              {yearGroup.entries.map((dayEntry: DayEntry) => (
                <Box
                  key={dayEntry.fullDate}
                  position="relative"
                >
                  <Box
                    position="absolute"
                    left="10px"
                    top="0"
                    bottom="0"
                    border="1px dashed"
                    borderColor="charcoal.70"
                  />
                  <Box
                    position="absolute"
                    left="2.5px"
                    top="20px"
                    zIndex={1}
                  >
                    <TimelineDotIcon />
                  </Box>

                  <Box pl={6}>
                    <Text
                      fontSize="md"
                      fontWeight={500}
                      color="charcoal.70"
                      my={4}
                      letterSpacing="-0.32px"
                    >
                      {dayEntry.date}
                    </Text>

                    <VStack
                      spacing={4}
                      align="stretch"
                      my={2}
                    >
                      {renderOrderedResourceGroups(dayEntry)}
                    </VStack>
                  </Box>
                </Box>
              ))}
            </VStack>
          </Box>
        ))}
      </VStack>
    </Box>
  );
}

export default React.memo(ShareTimeline);
