import { useMemo } from 'react';
import { find, get, groupBy, has, isArray, map, mapValues, orderBy, some, uniq } from 'lodash';
import { useGetShareTimelineData } from '@user/lib/medplum-state';

import { RESOURCE_TYPE_ORDER, SEVERITY_ORDER, VITAL_TYPE_MAPPING } from '../constants';
import { formatDateFields } from '../../../utils';

// Optimized native JavaScript alternatives for performance-critical operations
const nativeGroupBy = <T, K extends string | number | symbol>(array: T[], keyFn: (item: T) => K): Record<K, T[]> => {
  return array.reduce((acc, item) => {
    const key = keyFn(item);
    if (!acc[key]) acc[key] = [];
    acc[key].push(item);
    return acc;
  }, {} as Record<K, T[]>);
};

const nativeSortBy = <T>(array: T[], iteratee: (item: T) => any): T[] => {
  return [...array].sort((a, b) => {
    const aVal = iteratee(a);
    const bVal = iteratee(b);
    if (aVal < bVal) return -1;
    if (aVal > bVal) return 1;
    return 0;
  });
};

type RawResource = any;
type NormalizedResource = RawResource & {
  resourceType: string;
  effectiveDateString?: string;
  dateObject?: Date | null;
  fullDate?: string; // yyyy-MM-dd
  displayDate?: string; // dd MMM yyyy
  year?: string; // yyyy
  sortKey?: number; // timestamp (ms)
  severity?: string;
};

export function useTimelineData(patientId: string) {
  const { shareTimelineData } = useGetShareTimelineData(patientId);

  // Memoize raw data extraction to avoid repeated computations
  const rawData = useMemo(
    () => ({
      masterVitalList: shareTimelineData?.data?.Observations || [],
      immunizationList: shareTimelineData?.data?.ImmunizationList || [],
      procedureList: shareTimelineData?.data?.ProcedureList || [],
      conditionList: shareTimelineData?.data?.Conditions || [],
    }),
    [shareTimelineData?.data]
  );

  return useMemo(() => {
    try {
      // Check for missing data
      if (!shareTimelineData?.data) {
        return [];
      }

      // Use imported vital type mapping
      const normalizedVitals: NormalizedResource[] = (
        isArray(rawData.masterVitalList) ? rawData.masterVitalList : []
      ).reduce<NormalizedResource[]>((acc: NormalizedResource[], o: any) => {
        const identifier = get(o, 'identifier[0].value');
        if (!identifier || !has(VITAL_TYPE_MAPPING, identifier)) return acc;

        acc.push({
          ...o,
          resourceType: VITAL_TYPE_MAPPING[identifier] ?? 'VitalSigns',
          severity: get(o, 'severity', 'normal'),
        });

        return acc;
      }, []);

      const immunizationsRaw = get(rawData.immunizationList, 'ImmunizationList') ?? rawData.immunizationList ?? [];
      const normalizedImmunizations: NormalizedResource[] = (isArray(immunizationsRaw) ? immunizationsRaw : []).map(
        (im: any) => {
          const coding: any[] = isArray(get(im, 'vaccineCode.coding')) ? get(im, 'vaccineCode.coding') : [];
          const fact = find(coding, (c) => typeof c?.system === 'string' && c.system.includes('FHIR/ValueSet/FACT'));
          const cvx = find(coding, { system: 'http://hl7.org/fhir/sid/cvx' });
          return {
            ...im,
            resourceType: 'Immunization',
            effectiveDateString: get(im, 'occurrenceDateTime') ?? get(im, 'recorded') ?? get(im, 'meta.lastUpdated'),
            severity: get(im, 'severity', 'normal'),
            title:
              get(fact, 'display') ||
              get(cvx, 'display') ||
              get(im, 'vaccineCode.text') ||
              get(im, 'identifier[0].value'),
            doseNumber:
              get(im, 'protocolApplied[0].doseNumberPositiveInt') ?? get(im, 'protocolApplied[0].doseNumberString'),
            source: get(im, 'reportOrigin.coding[0].display'),
          };
        }
      );

      const normalizedProcedures: NormalizedResource[] = map(
        isArray(get(rawData.procedureList, 'ProcedureList'))
          ? get(rawData.procedureList, 'ProcedureList')
          : rawData.procedureList ?? [],
        (p: any) => {
          const identifiers = get(p, 'identifier', []);
          const isFactPro = some(identifiers, (id) => id?.value?.includes('FACT-pro'));
          return {
            ...p,
            resourceType: isFactPro ? 'Procedure' : 'KeyHealthTest',
            effectiveDateString:
              p.performedDateTime ??
              get(p, 'performedPeriod.start') ??
              get(p, 'performedPeriod.end') ??
              get(p, 'meta.lastUpdated'),
            severity: p.severity ?? 'normal',
            title: get(p, 'code.text') ?? get(p, 'code.coding[0].display') ?? get(p, 'identifier[0].value'),
          };
        }
      );

      const normalizedConditions: NormalizedResource[] = (
        isArray(rawData.conditionList) ? rawData.conditionList : []
      ).map((c: any) => {
        const coding = get(c, 'code.coding', []);
        const fact = find(coding, (cd) => typeof cd?.system === 'string' && cd.system.includes('FHIR/ValueSet/FACT'));
        return {
          ...c,
          resourceType: 'Condition',
          effectiveDateString: get(c, 'onsetDateTime') ?? get(c, 'onsetPeriod.start') ?? get(c, 'meta.lastUpdated'),
          severity: get(c, 'severity', 'normal'),
          title: get(fact, 'display') ?? get(c, 'code.text') ?? get(c, 'code.coding[0].display'),
        };
      });

      const normalizedSymptoms: NormalizedResource[] = (isArray(rawData.masterVitalList) ? rawData.masterVitalList : [])
        .filter((o: any) => get(o, 'identifier[0].value') === 'FACT-sym')
        .map((sym: any) => {
          const coding = get(sym, 'code.coding', []);
          return {
            ...sym,
            resourceType: 'Symptom',
            effectiveDateString:
              get(sym, 'effectiveDateTime') ?? get(sym, 'effectivePeriod.start') ?? get(sym, 'meta.lastUpdated'),
            severity: get(sym, 'severity', 'normal'),
            title: get(coding, '[0].display') ?? get(sym, 'identifier[0].value'),
          };
        });

      const allResources: NormalizedResource[] = [
        ...normalizedVitals,
        ...normalizedImmunizations,
        ...normalizedProcedures,
        ...normalizedConditions,
        ...normalizedSymptoms,
      ].map((res) => {
        const dateFields = formatDateFields(res);
        return {
          ...res,
          ...dateFields,
          resourceType: res.resourceType ?? 'Unknown',
          severity: res.severity ?? 'normal',
        };
      });

      const datedResources = allResources.filter((r) => r.dateObject != null && r.fullDate);

      // Group by year (using native implementation for better performance)
      const resourcesGroupedByYear = nativeGroupBy(datedResources, (r) => r.year!);

      // For each year, group resources by fullDate and by resourceType, sorting along the way
      const yearGroups = Object.entries(resourcesGroupedByYear).map(([year, resourcesInYear]) => {
        // Group resources by fullDate (yyyy-MM-dd) - using native for performance
        const resourcesGroupedByDate = nativeGroupBy(resourcesInYear, (r) => r.fullDate!);

        // Build day entries
        const dayEntries = Object.entries(resourcesGroupedByDate).map(([fullDate, dayResources]) => {
          // Group day resources by resourceType
          const resourcesByType = mapValues(groupBy(dayResources, 'resourceType'), (items) =>
            // sort each resource type list by severity rank (ascending: severe -> normal)
            nativeSortBy(items, (r) => SEVERITY_ORDER[r.severity as keyof typeof SEVERITY_ORDER] ?? 999)
          );

          // Use the first item (all have same dateObject) to pick displayDate and sortKey reliably
          const anchor = dayResources[0];
          return {
            date: anchor.displayDate,
            fullDate,
            sortKey: anchor.sortKey as number,
            resources: resourcesByType,
          };
        });

        // Sort dayEntries descending by sortKey (newest first)
        const sortedDayEntries = orderBy(dayEntries, ['sortKey'], ['desc']);

        return { year, entries: sortedDayEntries };
      });

      // Sort years descending and attach orderedResourceTypes (preserving RESOURCE_TYPE_ORDER first)
      const sortedYearGroups = orderBy(yearGroups, [(yg) => yg.year], ['desc']).map((yearGroup) => {
        const entriesWithOrderedTypes = yearGroup.entries.map((dayEntry) => {
          const resourceTypesInDay = Object.keys(dayEntry.resources);
          const orderedResourceTypes = uniq([...RESOURCE_TYPE_ORDER, ...resourceTypesInDay]).filter(Boolean);
          return { ...dayEntry, orderedResourceTypes };
        });

        return { year: yearGroup.year, entries: entriesWithOrderedTypes };
      });

      return sortedYearGroups;
    } catch (error) {
      console.error('Timeline data processing error:', error);
      return [];
    }
  }, [rawData, patientId]);
}
