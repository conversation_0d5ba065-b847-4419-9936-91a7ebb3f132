export type Severity = 'severe' | 'moderate' | 'mild' | 'normal' | (string & {});

export interface Quantity {
  value?: number;
  unit?: string;
}

export interface ComponentItem {
  code?: { coding?: Array<{ code?: string }> };
  valueQuantity?: Quantity;
}

export interface BaseResource {
  id?: string;
  resourceType?: string;
  code?: { text?: string; coding?: Array<{ code?: string; display?: string }> };
  effectiveDateTime?: string;
  onsetDateTime?: string;
  recordedDate?: string;
  issued?: string;
  date?: string;
  effectivePeriod?: { start?: string; end?: string };
  note?: Array<{ text?: string }>;
  method?: { coding?: Array<{ display?: string }> };
  valueQuantity?: Quantity;
  component?: ComponentItem[];
  severity?: Severity;
  [k: string]: unknown;
}

export type ResourceMap = Record<string, BaseResource[]>;

export interface DayEntry {
  date: string;
  fullDate: string;
  sortKey: number;
  resources: Record<string, BaseResource[]>;
  orderedResourceTypes?: string[];
}

export interface YearGroup {
  year: string;
  entries: DayEntry[];
}
