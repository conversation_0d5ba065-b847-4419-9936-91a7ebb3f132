import type { ReactElement } from 'react';

// Import all icons from the icons file
import {
  AppointmentIcon,
  BloodPressureIcon,
  BodyTemperatureIcon,
  ConditionIcon,
  ImmunizationIcon,
  KeyHealthTestIcon,
  LabResultsIcon,
  OxygenSaturationIcon,
  ProcedureIcon,
  PulseRateIcon,
  RespiratoryRateIcon,
  SymptomIcon,
  TimelineDotIcon,
} from '../../icons';

// Re-export icons for backward compatibility
export {
  TimelineDotIcon,
  KeyHealthTestIcon,
  SymptomIcon,
  RespiratoryRateIcon,
  ProcedureIcon,
  ImmunizationIcon,
  LabResultsIcon,
  AppointmentIcon,
  ConditionIcon,
  OxygenSaturationIcon,
  BloodPressureIcon,
  PulseRateIcon,
  BodyTemperatureIcon,
};

// Derived types from configuration
export type VitalSignKey = (typeof VITAL_SIGNS_CONFIG)[keyof typeof VITAL_SIGNS_CONFIG]['key'];
export type ResourceTypeKey =
  | 'DiagnosticReport'
  | 'BloodPressure'
  | 'OxygenSaturation'
  | 'PulseRate'
  | 'RespiratoryRate'
  | 'BodyTemperature'
  | 'Symptom'
  | 'KeyHealthTest'
  | 'Procedure'
  | 'Observation'
  | 'Condition'
  | 'Immunization';

export type ResourceMeta = {
  Icon: () => ReactElement;
  label: string;
  bgColor: string;
  txtBg: string;
  txtColor: string;
};

// Resource Types Configuration
export const resourceTypes: Record<ResourceTypeKey, ResourceMeta> = {
  DiagnosticReport: {
    Icon: LabResultsIcon,
    label: 'Lab Results',
    bgColor: '#FFEFEB',
    txtBg: '#FFE0D6',
    txtColor: '#FF6333',
  },
  BloodPressure: {
    Icon: BloodPressureIcon,
    label: 'Blood Pressure',
    bgColor: '#F2FCFA',
    txtBg: '#D4EAE3',
    txtColor: '#20785C',
  },
  OxygenSaturation: {
    Icon: OxygenSaturationIcon,
    label: 'Oxygen Saturation Level',
    bgColor: '#F2FCFA',
    txtBg: '#D4EAE3',
    txtColor: '#20785C',
  },
  PulseRate: {
    Icon: PulseRateIcon,
    label: 'Pulse Rate',
    bgColor: '#F2FCFA',
    txtBg: '#D4EAE3',
    txtColor: '#20785C',
  },
  RespiratoryRate: {
    Icon: RespiratoryRateIcon,
    label: 'Respiratory Rate',
    bgColor: '#F2FCFA',
    txtBg: '#D4EAE3',
    txtColor: '#20785C',
  },
  BodyTemperature: {
    Icon: BodyTemperatureIcon,
    label: 'Body Temperature',
    bgColor: '#F2FCFA',
    txtBg: '#D4EAE3',
    txtColor: '#20785C',
  },
  Symptom: {
    Icon: SymptomIcon,
    label: 'Symptoms',
    bgColor: '#F4F9FF',
    txtBg: '#DFEDFF',
    txtColor: '#495AE4',
  },
  KeyHealthTest: {
    Icon: KeyHealthTestIcon,
    label: 'Key Health Test',
    bgColor: '#D4E7FF',
    txtBg: '#C1DCFF',
    txtColor: '#2C3EC4',
  },
  Procedure: {
    Icon: ProcedureIcon,
    label: 'SURGERIES AND/OR PROCEDURES',
    bgColor: '#FFF7EC',
    txtBg: '#FCE6C6',
    txtColor: '#FF6333',
  },
  Immunization: {
    Icon: ImmunizationIcon,
    label: 'Immunizations',
    bgColor: '#F8F8FF',
    txtBg: '#DBDEFA',
    txtColor: '#495AE4',
  },
  Condition: {
    Icon: ConditionIcon,
    label: 'Condition',
    bgColor: '#FFFAF2',
    txtBg: '#FCE6C6',
    txtColor: '#FF6333',
  },
  Observation: {
    Icon: SymptomIcon,
    label: 'Symptom',
    bgColor: '#F8F8FF',
    txtBg: '#DBDEFA',
    txtColor: '#495AE4',
  },
};

// Optimized Vital Signs Configuration (Single Source of Truth)
export const VITAL_SIGNS_CONFIG = {
  BLOOD_PRESSURE: {
    key: 'vi:blood-pressure',
    component: 'BloodPressure',
    loincCodes: { systolic: '8480-6', diastolic: '8462-4' },
    ranges: {
      systolic: {
        low: { min: 0, max: 89, label: 'Low', color: 'blue' },
        optimal: { min: 90, max: 129, label: 'Optimal', color: 'green' },
        high: { min: 130, max: 200, label: 'High', color: 'red' },
      },
      diastolic: {
        low: { min: 0, max: 59, label: 'Low', color: 'blue' },
        optimal: { min: 60, max: 84, label: 'Optimal', color: 'green' },
        high: { min: 85, max: 120, label: 'High', color: 'red' },
      },
    },
  },
  OXYGEN_SATURATION: {
    key: 'vi:oxygen-saturation-level',
    component: 'OxygenSaturation',
    loincCodes: { main: '59408-5' },
    ranges: {
      low: { min: 0, max: 94, label: 'Low', color: 'red' },
      optimal: { min: 95, max: 100, label: 'Optimal', color: 'green' },
      high: { min: 101, max: 100, label: 'High', color: 'red' },
    },
  },
  PULSE_RATE: {
    key: 'vi:pulse-rate',
    component: 'PulseRate',
    loincCodes: { main: '8867-4' },
    ranges: {
      low: { min: 0, max: 59, label: 'Low', color: 'blue' },
      optimal: { min: 60, max: 100, label: 'Optimal', color: 'green' },
      high: { min: 101, max: 200, label: 'High', color: 'red' },
    },
  },
  RESPIRATORY_RATE: {
    key: 'vi:respiratory-rate',
    component: 'RespiratoryRate',
    loincCodes: { main: '9279-1' },
    ranges: {
      low: { min: 0, max: 11, label: 'Low', color: 'blue' },
      optimal: { min: 12, max: 20, label: 'Optimal', color: 'green' },
      high: { min: 21, max: 60, label: 'High', color: 'red' },
    },
  },
  BODY_TEMPERATURE: {
    key: 'vi:body-temperature',
    component: 'BodyTemperature',
    loincCodes: { main: '8310-5' },
    ranges: {
      low: { min: 0, max: 36.4, label: 'Low', color: 'blue' },
      optimal: { min: 36.5, max: 37.5, label: 'Optimal', color: 'green' },
      high: { min: 37.6, max: 42, label: 'High', color: 'red' },
    },
  },
} as const;

// Auto-generated constants from configuration
export const VITAL_SIGN_KEYS = Object.fromEntries(
  Object.entries(VITAL_SIGNS_CONFIG).map(([key, config]) => [key, config.key])
) as Record<keyof typeof VITAL_SIGNS_CONFIG, string>;

export const VITAL_TYPE_MAPPING = Object.fromEntries(
  Object.values(VITAL_SIGNS_CONFIG).map((config) => [config.key, config.component])
) as Record<string, string>;

export const LOINC_CODES = Object.fromEntries(
  Object.entries(VITAL_SIGNS_CONFIG).flatMap(([key, config]) =>
    Object.entries(config.loincCodes).map(([subKey, code]) => [
      key === 'BLOOD_PRESSURE' && subKey === 'systolic'
        ? 'SYSTOLIC_BP'
        : key === 'BLOOD_PRESSURE' && subKey === 'diastolic'
        ? 'DIASTOLIC_BP'
        : key.toUpperCase(),
      code,
    ])
  )
) as Record<string, string>;

// UI Constants
export const UI_CONSTANTS = {
  COLORS: {
    TEXT_PRIMARY: '#14181A',
    TEXT_SECONDARY: '#5B5D5F',
    TEXT_MUTED: '#858D8F',
    BORDER_LIGHT: 'rgba(133, 140, 143, 0.20)',
  } as const,

  STATUS_LABELS: {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
    COMPLETE: 'Complete',
    LOW: 'Low',
    OPTIMAL: 'Optimal',
    HIGH: 'High',
  } as const,

  RESOURCE_TYPE_ORDER: [
    'Condition',
    'Symptom',
    'Procedure',
    'KeyHealthTest',
    'BloodPressure',
    'OxygenSaturation',
    'PulseRate',
    'RespiratoryRate',
    'BodyTemperature',
    'Observation',
    'DiagnosticReport',
  ] as const,

  SEVERITY_ORDER: {
    severe: 0,
    moderate: 1,
    mild: 2,
    normal: 3,
  } as const,
};

// Convenience exports
export const { COLORS, STATUS_LABELS, RESOURCE_TYPE_ORDER, SEVERITY_ORDER } = UI_CONSTANTS;
export const BLOOD_PRESSURE_RANGES = VITAL_SIGNS_CONFIG.BLOOD_PRESSURE.ranges;

// Layout constants for share profile components
const OTHER_HEIGHT_MAX = '539';
const OTHER_HEIGHT_MIN_OFFSET = '176';
const UI_OFFSET = 78;

export const LAYOUT_HEIGHTS = {
  OTHER_HEIGHT_MAX,
  OTHER_HEIGHT_MIN_OFFSET,
  CONTAINER_AT_TOP: `calc(100vh - ${OTHER_HEIGHT_MAX}px + ${OTHER_HEIGHT_MIN_OFFSET}px)`,
  CONTAINER_SCROLLED: `calc(100vh - ${OTHER_HEIGHT_MAX}px)`,
  UI_OFFSET,
} as const;

export const calculateContentHeight = (isAtTop: boolean): string => {
  return isAtTop
    ? `calc(100vh - ${LAYOUT_HEIGHTS.OTHER_HEIGHT_MAX}px - ${LAYOUT_HEIGHTS.UI_OFFSET}px + ${LAYOUT_HEIGHTS.OTHER_HEIGHT_MIN_OFFSET}px)`
    : `calc(100vh - ${LAYOUT_HEIGHTS.OTHER_HEIGHT_MAX}px - ${LAYOUT_HEIGHTS.UI_OFFSET}px)`;
};

export const SmoothTransition = 'all .75s cubic-bezier(0.75, -0.5, 0.21, 1.5)';
