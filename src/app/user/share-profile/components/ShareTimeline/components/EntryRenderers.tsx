import React, { useCallback, useMemo } from 'react';
import { Badge, Box, Circle, Flex, HStack, Text } from '@chakra-ui/react';
import { format, parseISO } from 'date-fns';

import { BLOOD_PRESSURE_RANGES, COLORS, LOINC_CODES, STATUS_LABELS, VITAL_SIGN_KEYS } from '../constants';
import { formatDateSafe, getBadgeStyles, getVitalStatus } from '../../../utils';

export const VitalRenderer = React.memo(function VitalRendererComponent({ entry }: { entry: any }) {
  const vitalType = entry.identifier?.[0]?.value as string | undefined;

  const bloodPressureData = useMemo(() => {
    if (vitalType !== VITAL_SIGN_KEYS.BLOOD_PRESSURE || !Array.isArray(entry.component)) {
      return null;
    }

    const systolic = entry.component?.find((c: any) => c.code?.coding?.[0]?.code === LOINC_CODES.SYSTOLIC_BP)
      ?.valueQuantity?.value;
    const diastolic = entry.component?.find((c: any) => c.code?.coding?.[0]?.code === LOINC_CODES.DIASTOLIC_BP)
      ?.valueQuantity?.value;
    const bloodPressureUnit = entry.component?.find((c: any) => c.code?.coding?.[0]?.code === LOINC_CODES.SYSTOLIC_BP)
      ?.valueQuantity.unit;

    return systolic && diastolic ? { systolic, diastolic, bloodPressureUnit } : null;
  }, [vitalType, entry.component]);

  // Memoize BP status calculation function
  const getBPStatus = useCallback((value: number, isSystolic: boolean) => {
    const ranges = isSystolic ? BLOOD_PRESSURE_RANGES.systolic : BLOOD_PRESSURE_RANGES.diastolic;
    if (value >= ranges.low.min && value <= ranges.low.max) return ranges.low;
    if (value >= ranges.optimal.min && value <= ranges.optimal.max) return ranges.optimal;
    return ranges.high;
  }, []);

  if (bloodPressureData) {
    const { systolic, diastolic, bloodPressureUnit } = bloodPressureData;
    const systolicStatus = getBPStatus(systolic, true);

    return (
      <Box
        position="relative"
        width="100%"
      >
        <Flex
          justify="space-between"
          align="flex-start"
        >
          <HStack spacing={1}>
            <Text
              fontSize="16px"
              color={COLORS.TEXT_PRIMARY}
              letterSpacing="-0.32px"
              aria-label={`Blood pressure reading: ${systolic}/${diastolic} ${bloodPressureUnit}`}
            >
              {systolic}/{diastolic} {bloodPressureUnit}
            </Text>
          </HStack>
          <Badge
            {...getBadgeStyles(systolicStatus.color)}
            size="sm"
            px="8px"
            py="2px"
            borderRadius="17px"
            fontSize="12px"
            textTransform="capitalize"
            fontWeight="400"
          >
            {systolicStatus.label}
          </Badge>
        </Flex>
        <Text
          fontSize="12px"
          color={COLORS.TEXT_SECONDARY}
          mt={1}
        >
          {entry.effectiveDateTime ? format(parseISO(entry.effectiveDateTime), 'hh:mm a') : ''}
        </Text>
      </Box>
    );
  }

  if (entry.valueQuantity) {
    const { value } = entry.valueQuantity;
    const { unit } = entry.valueQuantity;
    const status = getVitalStatus(vitalType || '', value);

    return (
      <Box
        position="relative"
        width="100%"
      >
        <Flex
          justify="space-between"
          align="flex-start"
        >
          <HStack spacing={1}>
            <Text
              fontSize="16px"
              color="#14181A"
              letterSpacing="-0.32px"
              aria-label={`Vital sign value: ${value} ${unit}`}
            >
              {value} {unit}
            </Text>
          </HStack>
          <Badge
            {...getBadgeStyles(status.color)}
            size="sm"
            px="8px"
            py="2px"
            borderRadius="17px"
            fontSize="12px"
            fontWeight="400"
            textTransform="capitalize"
          >
            {status.label}
          </Badge>
        </Flex>
        <Text
          fontSize="12px"
          color="#5B5D5F"
          mt={1}
          letterSpacing="-0.24px"
        >
          {entry.effectiveDateTime ? format(parseISO(entry.effectiveDateTime), 'HH:mm') : ''}
        </Text>
      </Box>
    );
  }

  return null;
});

export function ObservationRenderer({ entry }: { entry: any }) {
  const withinRange = entry.valueQuantity?.value ?? 0;
  const outOfRange = entry.component?.[0]?.valueQuantity?.value ?? 0;
  return (
    <Box>
      <Text
        color="gray.900"
        mb={2}
      >
        {entry.code?.text}
      </Text>
      <HStack
        spacing={3}
        mb={2}
      >
        <Box
          as="span"
          display="inline-flex"
          alignItems="center"
          px="8px"
          py="2px"
          border="1px solid"
          borderColor="rgba(133, 140, 143, 0.20)"
          borderRadius="full"
          bg="white"
        >
          <Circle
            size="6px"
            bg="green.400"
            mr="6px"
          />
          <Text
            fontSize="xs"
            color="green.700"
          >
            {withinRange} within range
          </Text>
        </Box>
        <Box
          as="span"
          display="inline-flex"
          alignItems="center"
          px="8px"
          py="2px"
          border="1px solid"
          borderColor={COLORS.BORDER_LIGHT}
          borderRadius="full"
          bg="white"
        >
          <Circle
            size="6px"
            bg="red.400"
            mr="6px"
          />
          <Text
            fontSize="xs"
            color="red.700"
          >
            {outOfRange} out of range
          </Text>
        </Box>
      </HStack>
      {entry.note?.[0]?.text && (
        <Text
          fontSize="sm"
          color="gray.600"
          mb={2}
        >
          {entry.note[0].text.substring(0, 100)}...
        </Text>
      )}
    </Box>
  );
}

export function DiagnosticReportRenderer({ entry, dayDateText }: { entry: any; dayDateText: string }) {
  return (
    <Box>
      <Text
        color="gray.900"
        pt="1"
      >
        {entry.code?.text}
      </Text>
      <Text
        fontSize="sm"
        color="gray.600"
        my="1"
        pb="2"
        borderBottom="1px solid"
        borderColor={COLORS.BORDER_LIGHT}
      >
        {dayDateText}
      </Text>
    </Box>
  );
}

export function ConditionRenderer({ entry }: { entry: any }) {
  const severityText = entry?.extension?.[0]?.valueCodeableConcept?.coding?.[0]?.display ?? null;
  return (
    <Box>
      <Flex
        align="center"
        justify="space-between"
      >
        <HStack>
          <Box>
            <Text color="gray.900">{entry.title ?? entry.code?.text ?? 'Condition'}</Text>
            <Text
              fontSize="sm"
              color="gray.600"
            >
              {entry.note?.[0]?.text ?? ''}
            </Text>
          </Box>
        </HStack>
        <Badge
          {...getBadgeStyles(severityText, 'Condition')}
          size="sm"
          px="8px"
          py="2px"
          borderRadius="17px"
          fontSize="12px"
          textTransform="capitalize"
          fontWeight="400"
        >
          {severityText}
        </Badge>
      </Flex>
    </Box>
  );
}

export function ProcedureRenderer({ entry }: { entry: any }) {
  const codeDisplay = (entry?.code?.coding?.[0]?.display as string | undefined)?.replace(' (procedure)', '');
  const title =
    (entry?.title as string | undefined) ?? codeDisplay ?? (entry?.code?.text as string | undefined) ?? 'Procedure';

  const rawStatus = (entry?.status as string | undefined)?.toLowerCase();
  const statusLabel =
    rawStatus === 'completed'
      ? STATUS_LABELS.COMPLETE
      : rawStatus
      ? rawStatus.charAt(0).toUpperCase() + rawStatus.slice(1)
      : undefined;

  let statusColorScheme: string;
  if (rawStatus === 'completed') {
    statusColorScheme = 'Complete';
  } else {
    statusColorScheme = 'Hold';
  }

  return (
    <Box>
      <Flex
        justify="space-between"
        align="center"
        mb={1}
      >
        <Text
          color="#14181A"
          fontSize="16px"
          letterSpacing="-.24px"
        >
          {title}
        </Text>
        {statusLabel && (
          <Badge
            {...getBadgeStyles(statusColorScheme, 'status')}
            size="sm"
            px="8px"
            py="2px"
            borderRadius="17px"
            fontSize="12px"
            fontWeight="400"
            textTransform="capitalize"
          >
            {statusLabel}
          </Badge>
        )}
      </Flex>
    </Box>
  );
}
export function KeyHealthTestRenderer({ entry }: { entry: any }) {
  const title =
    (entry?.code?.coding?.[0]?.display as string | undefined)?.replace(' (procedure)', '') ?? 'Key Health Test';
  return (
    <Box>
      <Text color="gray.900">{title}</Text>
    </Box>
  );
}
export function SymptomRenderer({ entry }: { entry: any }) {
  if (!entry) return null;
  const title = entry.title ?? entry.code?.coding?.[0]?.display ?? 'Symptom';
  const startDate = formatDateSafe(
    entry.effectivePeriod?.start ?? entry.effectiveDateTime ?? entry.issued ?? entry.recordedDate
  );
  const endDate = formatDateSafe(entry.effectivePeriod?.end) ? formatDateSafe(entry.effectivePeriod?.end) : 'Present';
  // const statusLabel = !endDate ? STATUS_LABELS.ACTIVE : STATUS_LABELS.INACTIVE;
  const dateRange = [startDate, endDate].filter(Boolean).join(' - ');
  const noteText = entry.note?.[0]?.text ?? '';
  const truncatedNote = noteText.length > 140 ? `${noteText.slice(0, 140)}...` : noteText;

  return (
    <Box>
      <Flex
        justify="space-between"
        align="center"
        mb={1}
      >
        <Text
          fontSize="16px"
          color="gray.900"
          letterSpacing="-0.32px"
        >
          {title}
        </Text>
        {/* <Badge
          {...getBadgeStyles(!endDate ? 'green' : 'gray', 'status')}
          size="sm"
          px="8px"
          py="2px"
          borderRadius="17px"
          fontSize="12px"
        >
          {statusLabel}
        </Badge> */}
      </Flex>

      {dateRange && (
        <HStack
          spacing={2}
          mb={1}
        >
          <Text
            fontSize="sm"
            color="gray.600"
          >
            {dateRange}
          </Text>
        </HStack>
      )}

      {truncatedNote && (
        <Text
          fontSize="sm"
          color="gray.600"
        >
          {truncatedNote}
        </Text>
      )}
    </Box>
  );
}

export function ImmunizationRenderer({ entry }: { entry: any }) {
  const doseLabel = entry.doseNumber ? `Dose: ${entry.doseNumber}` : undefined;
  return (
    <Box>
      <HStack
        justify="space-between"
        mb={1}
      >
        <Text
          color="gray.900"
          fontSize="16px"
          letterSpacing="-0.32px"
        >
          {entry.title || entry.code?.coding?.[0]?.display || 'Immunization'}
        </Text>
      </HStack>
      {doseLabel && (
        <Text
          color="#5B5D5F"
          fontSize="12px"
        >
          {doseLabel}
        </Text>
      )}
    </Box>
  );
}

export function GenericRenderer({ entry }: { entry: any }) {
  return (
    <Box>
      <Text color="gray.900">{entry.code?.text ?? 'Unknown'}</Text>
    </Box>
  );
}
