import React, { useState } from 'react';
import { Box, Button, Flex, HStack, Text, VStack } from '@chakra-ui/react';

import { ResourceMeta, ResourceTypeKey, resourceTypes } from '../constants';
import {
  Condition<PERSON><PERSON><PERSON>,
  Diagnostic<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>uni<PERSON><PERSON><PERSON><PERSON>,
  KeyHealthTestRenderer,
  Observation<PERSON><PERSON>er,
  <PERSON>cedureRenderer,
  SymptomRenderer,
  VitalRenderer,
} from './EntryRenderers';

const renderEntry = (entry: any, resourceType: ResourceTypeKey, dayDateText: string) => {
  const key = entry.id || entry.resource?.id || `${resourceType}-${entry.effectiveDateTime}`;

  switch (resourceType) {
    case 'DiagnosticReport':
      return (
        <DiagnosticReportRenderer
          key={key}
          entry={entry}
          dayDateText={dayDateText}
        />
      );
    case 'BloodPressure':
    case 'OxygenSaturation':
    case 'PulseRate':
    case 'RespiratoryRate':
    case 'BodyTemperature':
      return (
        <Box key={key}>
          <VitalRenderer entry={entry} />
        </Box>
      );
    case 'Observation':
      return (
        <ObservationRenderer
          key={key}
          entry={entry}
        />
      );
    case 'Condition':
      return (
        <ConditionRenderer
          key={key}
          entry={entry}
        />
      );
    case 'Symptom':
      return (
        <SymptomRenderer
          key={key}
          entry={entry}
        />
      );
    case 'Procedure':
      return (
        <ProcedureRenderer
          key={key}
          entry={entry}
        />
      );
    case 'KeyHealthTest':
      return (
        <KeyHealthTestRenderer
          key={key}
          entry={entry}
        />
      );
    case 'Immunization':
      return (
        <ImmunizationRenderer
          key={key}
          entry={entry}
        />
      );
    default:
      return (
        <GenericRenderer
          key={key}
          entry={entry}
        />
      );
  }
};
function DefaultIcon() {
  return <span>📄</span>;
}

export default React.memo(function ResourceGroup({
  resourceType,
  entries,
  dayDateText,
}: {
  resourceType: ResourceTypeKey | string; // allow string in case API returns unknown type
  entries: any[];
  dayDateText: string;
}) {
  const [expanded, setExpanded] = useState(false);

  const renderedEntries = entries
    .map((entry) => renderEntry(entry, resourceType as ResourceTypeKey, dayDateText))
    .filter(Boolean);

  if (renderedEntries.length === 0) return null;

  const resourceInfo: ResourceMeta & { Icon: React.FC } = (resourceTypes[
    resourceType as ResourceTypeKey
  ] as ResourceMeta) ?? {
    Icon: DefaultIcon,
    label: resourceType.toUpperCase(),
    bgColor: 'gray.100',
    txtBg: 'gray.200',
    txtColor: 'gray.700',
  };

  const visibleEntries = expanded ? renderedEntries : renderedEntries.slice(0, 3);

  return (
    <Box>
      <Box
        bg={resourceInfo.bgColor}
        p={4}
        borderRadius="10px"
        ml="-6"
        zIndex={1}
        position="relative"
        boxShadow="sm"
      >
        <HStack
          mb={1}
          bg={resourceInfo.txtBg}
          display="inline-flex"
          borderRadius="sm"
          spacing="4px"
          p="4px"
          align="start"
        >
          <Flex
            w="16px"
            h="16px"
            align="center"
            justify="center"
            fontSize="sm"
          >
            {resourceInfo.Icon ? <resourceInfo.Icon /> : <span>📄</span>}
          </Flex>
          <Text
            fontSize="12px"
            fontWeight="400"
            color={resourceInfo.txtColor}
            textTransform="uppercase"
            letterSpacing="-0.24px"
          >
            {resourceInfo.label}
          </Text>
        </HStack>

        <VStack
          spacing={0}
          align="stretch"
        >
          {visibleEntries.map((entry, idx) => {
            const isLastVisible = idx === visibleEntries.length - 1;
            const hasMoreButton = renderedEntries.length > 3;
            const showBorder = !isLastVisible || hasMoreButton;
            return (
              <Box
                key={entry?.props?.entry?.id ?? idx}
                borderBottom={showBorder ? '1px solid' : 'none'}
                borderColor="gray.200"
                py={2}
              >
                {entry}
              </Box>
            );
          })}
        </VStack>

        {renderedEntries.length > 3 && (
          <Button
            size="md"
            fontSize="14px"
            p="8px"
            mt={2}
            variant="ghost"
            colorScheme="blue"
            color="#495AE4"
            onClick={() => setExpanded((prev) => !prev)}
            w="100%"
            borderRadius={0}
            letterSpacing="-.28px"
          >
            {expanded ? 'View Less' : `View More`}
          </Button>
        )}
      </Box>
    </Box>
  );
});
