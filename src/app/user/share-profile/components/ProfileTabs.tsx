import { useMemo, useState } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { SuspenseLoader } from '@components/SuspenseLoader';

import { TabLink } from 'src/components/ui/Tab';
import HealthSnapshot from './HealthSnapshot';
import HealthTimeline from './HealthTimeline';
import { SmoothTransition } from './ShareTimeline/constants';

function MobileProfileTabs() {
  const [selectedTab, setSelectedTab] = useState(0);

  const healthSnapshotLoader = useMemo(
    () => (
      <Flex
        direction="column"
        gap={4}
      >
        <HealthSnapshot />
      </Flex>
    ),
    []
  );

  const healthTimelineLoader = useMemo(
    () => (
      <Flex
        direction="column"
        gap={4}
      >
        <HealthTimeline />
      </Flex>
    ),
    []
  );

  const myHealthHistoryLoader = useMemo(() => <SuspenseLoader component={() => import('./MyHealthHistory')} />, []);

  const basicInfoLoader = useMemo(() => <SuspenseLoader component={() => import('./BasicInfo')} />, []);

  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
    >
      <Flex
        px="20px"
        gap="8px"
        overflowX="auto"
        className="hide-scrollbar"
      >
        <TabLink
          to=""
          onClick={() => setSelectedTab(0)}
          isActive={selectedTab === 0}
        >
          Snapshot
        </TabLink>
        <TabLink
          to=""
          onClick={() => setSelectedTab(1)}
          isActive={selectedTab === 1}
        >
          Timeline
        </TabLink>
        <TabLink
          to=""
          onClick={() => setSelectedTab(2)}
          isActive={selectedTab === 2}
        >
          Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => setSelectedTab(3)}
          isActive={selectedTab === 3}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="20px"
        bgColor="periwinkle.100"
      >
        {selectedTab === 0 && healthSnapshotLoader}
        {selectedTab === 1 && healthTimelineLoader}
        {selectedTab === 2 && myHealthHistoryLoader}
        {selectedTab === 3 && basicInfoLoader}
      </Box>
    </Flex>
  );
}

export default function ProfileTabs({ contentHeight }: { contentHeight?: string }) {
  const [selectedTab, setSelectedTab] = useState(0);
  const isTablet = useIsTablet();

  const myHealthHistoryLoader = useMemo(() => <SuspenseLoader component={() => import('./MyHealthHistory')} />, []);

  const basicInfoLoader = useMemo(() => <SuspenseLoader component={() => import('./BasicInfo')} />, []);

  if (isTablet) {
    return <MobileProfileTabs />;
  }

  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
      display="flex"
      flexDirection="column"
      borderRadius="20px"
    >
      <Flex
        px="10px"
        gap="8px"
      >
        <TabLink
          to=""
          onClick={() => setSelectedTab(0)}
          isActive={selectedTab === 0}
        >
          Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => setSelectedTab(1)}
          isActive={selectedTab === 1}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="15px"
        bgColor="periwinkle.100"
        borderRadius="10px 10px 20px 20px"
        boxShadow="md"
      >
        <Box
          overflowY="scroll"
          h={contentHeight || 'auto'}
          transition={SmoothTransition}
        >
          {selectedTab === 0 && myHealthHistoryLoader}
          {selectedTab === 1 && basicInfoLoader}
        </Box>
      </Box>
    </Flex>
  );
}
