import React from 'react';
import { Flex, Text } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { useSharePatient } from '@user/lib/state';
import { useShareBasicInfo } from '@user/lib/medplum-state';

import EmptyState from './EmptyState';
import { ProfileBioCard } from './ProfileBioCard';
import EmergencyContactsCard from './ShareEmergencyContactCard';
import ShareAlternativeDecisionMaker from './ShareAlternativeDecisionMaker';
import ShareHealthInsurance from './ShareHealthInsurance';

function MobileBasicInfo() {
  const { patient } = useSharePatient();
  const { emergencyContactList, healthcareProxyList, healthInsuranceList } = useShareBasicInfo(patient?.id);

  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        My personal details
      </Text>
      <ProfileBioCard patient={patient} />
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        ADDITIONAL DETAILS
      </Text>
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Emergency Contacts
      </Text>
      {emergencyContactList.length > 0 ? (
        <EmergencyContactsCard emergencyContacts={emergencyContactList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
      <Text
        fontSize="13"
        fontWeight="medium"
        color="iris.500"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Alternative Medical Decision-Maker
      </Text>
      {healthcareProxyList.length > 0 ? (
        <ShareAlternativeDecisionMaker healthcareProxies={healthcareProxyList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Health insurance
      </Text>
      {healthInsuranceList.length > 0 ? (
        <ShareHealthInsurance healthInsurances={healthInsuranceList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
    </Flex>
  );
}
export default function BasicInfo() {
  const isTablet = useIsTablet();
  const { patient } = useSharePatient();
  const { emergencyContactList, healthcareProxyList, healthInsuranceList } = useShareBasicInfo(patient?.id);

  if (isTablet) {
    return <MobileBasicInfo />;
  }
  return (
    <Flex
      direction="column"
      p="8px"
    >
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        ADDITIONAL DETAILS
      </Text>
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Emergency Contacts
      </Text>
      {emergencyContactList.length > 0 ? (
        <EmergencyContactsCard emergencyContacts={emergencyContactList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
      <Text
        fontSize="13"
        fontWeight="medium"
        color="iris.500"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Alternative Medical Decision-Maker
      </Text>
      {healthcareProxyList.length > 0 ? (
        <ShareAlternativeDecisionMaker healthcareProxies={healthcareProxyList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Health insurance
      </Text>
      {healthInsuranceList.length > 0 ? (
        <ShareHealthInsurance healthInsurances={healthInsuranceList} />
      ) : (
        <EmptyState
          message="Nothing here yet"
          margin="16px 0px 0px 0px"
        />
      )}
    </Flex>
  );
}
