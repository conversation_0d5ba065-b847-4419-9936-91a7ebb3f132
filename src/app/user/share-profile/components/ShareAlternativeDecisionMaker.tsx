import React from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, Di<PERSON>r, Flex, Heading, Text } from '@chakra-ui/react';
import { FileText as FileIcon } from 'react-feather';

import { parsePatientName, parsePhoneNo, parseRelation } from '@lib/utils/utils';
import { Related<PERSON>erson } from 'src/gql/graphql';

const openFile = (url: string) => {
  window.open(url, '_blank');
};

function ShareAlternativeDecisionMaker({ healthcareProxies }: { healthcareProxies: RelatedPerson[] }) {
  if (healthcareProxies.length === 0) return null;
  return (
    <Flex
      direction="column"
      gap="12px"
      mt="16px"
    >
      {healthcareProxies.map((contact: any) => (
        <Flex
          bg="white"
          borderRadius="16px"
          key={contact.id}
          direction="column"
          boxShadow="0px 2px 8px rgba(0, 0, 0, 0.1)"
        >
          <Card
            bgColor="periwinkle.50"
            boxShadow="none"
            borderRadius="8px"
          >
            <CardBody padding="12px">
              <Heading
                fontSize="20px"
                color="periwinkle.600"
                fontWeight="400"
                textTransform="capitalize"
              >
                {parsePatientName(contact.name, '')}
              </Heading>
              <Text
                fontSize="16px"
                color="iris.300"
                mt="4px"
              >
                {parseRelation(contact.relationship)}
              </Text>
            </CardBody>
          </Card>
          <Flex
            direction="column"
            padding="16px 20px"
            gap="8px"
          >
            <Text
              fontSize="16px"
              color="gray.300"
              fontWeight="500"
              letterSpacing="-0.32px"
            >
              Contact number
            </Text>
            <Text
              fontSize="16px"
              color="gray.500"
              letterSpacing="-0.32px"
            >
              +91 {parsePhoneNo(contact.telecom)}
            </Text>
          </Flex>
          {contact?.file?.[0]?.content?.[0]?.attachment?.url && (
            <>
              <Divider
                bg="gray.100"
                w="90%"
                mx="auto"
              />
              <Flex
                direction="column"
                padding="16px 20px"
                gap="2px"
              >
                <Text
                  fontSize="16px"
                  color="gray.300"
                  fontWeight="500"
                  letterSpacing="-0.32px"
                >
                  Files
                </Text>
                <Button
                  leftIcon={<FileIcon size={15} />}
                  variant="ghost"
                  size="md"
                  color="gray.500"
                  justifyContent="left"
                  padding={0}
                  borderRadius={0}
                  onClick={() => openFile(contact?.file[0]?.content[0]?.attachment?.url)}
                >
                  <Text
                    color="gray.500"
                    maxW="150px"
                    overflow="hidden"
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                    fontSize="14px"
                  >
                    {contact?.file[0]?.content[0]?.attachment?.title}
                  </Text>
                </Button>
              </Flex>
            </>
          )}
        </Flex>
      ))}
    </Flex>
  );
}

export default ShareAlternativeDecisionMaker;
