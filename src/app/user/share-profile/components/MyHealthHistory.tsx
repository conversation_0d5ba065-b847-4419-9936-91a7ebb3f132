import React, { Suspense, memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  Card,
  CardBody,
  Divider,
  Drawer,
  DrawerContent,
  DrawerOverlay,
  Flex,
  Heading,
  Spacer,
  Text,
  VStack,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { MY_HEALTH_PROFILE_MAP_SHARE } from '@user/lib/constants';
import { useParams, useSearchParams } from 'react-router-dom';
import { useShareFamilyMemberHistoryList } from '@user/lib/medplum-state';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { hexOpacity } from '@components/theme/utils';
import { getAge } from '@utils/utils';
import { FluentHealthLoader } from '@components/FluentHealthLoader';
import { medplumApi } from '@user/lib/medplum-api';

import { SidebarConditions } from '../../profile/family-history/components/SidebarConditions';
import { FACT_CODE_SYSTEM } from '@lib/constants';
import { DrawerSection } from '../../profile/components/ProfileSidebarCTAs';
import EmptyState from './EmptyState';
import { usePublicSettings } from '@lib/state';

const FamilyMemberCard = memo(function FamilyMemberCard({
  member,
  bloodGroup,
  ethnicityGrp,
}: {
  member: any;
  bloodGroup: any[];
  ethnicityGrp: any[];
}) {
  const theme = useTheme();

  const ethnicityURL = useMemo(
    () => member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-eth')),
    [member?.extension]
  );

  const bloodGroupURL = useMemo(
    () => member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-Category-bg')),
    [member?.extension]
  );

  const bloodType = useMemo(
    () => bloodGroup.find((item) => item.value === bloodGroupURL?.valueCode),
    [bloodGroup, bloodGroupURL?.valueCode]
  );

  const ethnicity = useMemo(
    () => ethnicityGrp?.find((item) => item.value === ethnicityURL?.valueCode),
    [ethnicityGrp, ethnicityURL?.valueCode]
  );
  const conditionSidebar = useDisclosure();

  const openConditionsSidebar = () => {
    conditionSidebar.onOpen();
  };

  return (
    <>
      <Drawer
        isOpen={conditionSidebar.isOpen}
        placement="right"
        onClose={conditionSidebar.onClose}
        size="sm"
        blockScrollOnMount={false}
        aria-label="Family member conditions sidebar"
      >
        <DrawerOverlay />
        <DrawerContent bg="gradient.profileDrawer">
          <Suspense
            fallback={
              <FluentHealthLoader
                position="absolute"
                top={0}
                bottom={0}
                left={0}
                right={0}
                my="auto"
              />
            }
          >
            <SidebarConditions
              relatedPerson={member}
              onClose={conditionSidebar.onClose}
            />
          </Suspense>
        </DrawerContent>
      </Drawer>
      <Flex
        gap="16px"
        direction="column"
      >
        <Card
          bgColor="fluentHealthSecondary.500"
          w="full"
          boxShadow="none"
          borderRadius="8px"
          role="group"
        >
          <CardBody
            padding="12px 8px 12px 12px"
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
          >
            <Flex
              direction="column"
              px="2"
              gap="1"
            >
              <Heading
                fontSize="20px"
                color="fluentHealthSecondary.100"
                lineHeight="8"
              >
                {member?.name || ''}
              </Heading>
              <Heading
                fontSize="16px"
                color="fluentHealthSecondary.200"
                mt="4px"
              >
                {member?.relationship?.coding[0]?.display || ''}
              </Heading>
            </Flex>
          </CardBody>
        </Card>
        <Flex
          direction="column"
          flex={1}
          gap="16px"
        >
          <Flex
            justifyContent="space-between"
            direction="column"
            gap="8px"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
            mx="2"
          >
            <Text color="gray.300">Age</Text>
            <Text color="fluentHealthText.100">
              {member?.bornDate && !Number.isNaN(getAge(member?.bornDate)) ? getAge(member?.bornDate) : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            gap="8px"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
            mx="2"
          >
            <Text color="gray.300">Status</Text>
            <Text color="fluentHealthText.100">
              {member?.deceasedBoolean === true ? 'Deceased' : member?.deceasedBoolean === false ? 'Living' : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            mx="2"
            pb="2"
            paddingBottom="2"
            borderBottom="1px solid"
            borderColor="gray.100"
          >
            <Text color="gray.300">Blood group</Text>
            <Text color="fluentHealthText.100">{bloodType?.label || '—'}</Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction="column"
            mx="2"
            pb="2"
          >
            <Text color="gray.300">Ethnicity</Text>
            <Text color="fluentHealthText.100">{ethnicity?.label || '—'}</Text>
          </Flex>
          <Flex justifyContent="space-between">
            <Flex
              alignItems="center"
              direction="column"
              w="100%"
            >
              <Card
                bgColor="transparent"
                borderRadius="xl"
                border="1px solid"
                borderColor="periwinkle.400"
                boxShadow={`0px 1px 4px ${hexOpacity(theme.colors.royalBlue['500'], 0.12)}`}
                w="100%"
              >
                <Flex
                  direction="column"
                  p="2"
                  gap="1"
                >
                  <Flex
                    h="12"
                    alignItems="center"
                    px="2"
                    fontSize="lg"
                    fontWeight="medium"
                    lineHeight="short"
                    _hover={{ cursor: 'pointer', bgColor: 'periwinkle.200', borderRadius: 'lg' }}
                    onClick={() => openConditionsSidebar()}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        openConditionsSidebar();
                      }
                    }}
                    tabIndex={0}
                    role="button"
                    aria-label={`View conditions for ${member?.name || 'family member'}`}
                    aria-expanded={conditionSidebar.isOpen}
                  >
                    Conditions
                    <Spacer />
                    <ChevronRightIcon
                      fontSize="xl"
                      color="papaya.600"
                      aria-hidden="true"
                    />
                  </Flex>
                </Flex>
              </Card>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
});

const MyHealthHistory = memo(function MyHealthHistory() {
  const params = useParams();
  const { ehrId, subEhrId = '', action } = params as any;
  const sharePatientId = useMemo(() => localStorage.getItem('sharePatientId'), []);
  const familyMemberList = useShareFamilyMemberHistoryList(sharePatientId);
  const [searchParams] = useSearchParams();
  const [sharedBloodGroup, setSharedBloodGroup] = useState<any[]>([]);
  const [sharedEthnicityGrp, setSharedEthnicityGrp] = useState<any[]>([]);
  const hasFetchedRef = useRef(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  const accessToken = searchParams.get('access_token');
  const { isPublicMode } = usePublicSettings();

  const healthProfileEntries = useMemo(() => Object.entries(MY_HEALTH_PROFILE_MAP_SHARE), []);

  useEffect(() => {
    if (isPublicMode) {
      hasFetchedRef.current = true;
      setIsLoading(false);
      return;
    }
    if (!accessToken || hasFetchedRef.current || isLoading) {
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      hasFetchedRef.current = true;

      try {
        const [inEthnicity, bloodGroupData] = await Promise.all([
          medplumApi.valueSetList.getAllValueSetFromShareDirectus(true, FACT_CODE_SYSTEM.ETHNICITY, accessToken),
          medplumApi.valueSetList.getAllValueSetFromShareDirectus(true, FACT_CODE_SYSTEM.BLOOD_GROUP, accessToken),
        ]);

        const ethnicityData = inEthnicity.map((e: { display: string; code: string }) => ({
          label: e.display,
          value: e.code,
        }));
        const bloodGroupDataFormatted = bloodGroupData.map((e: { display: string; code: string }) => ({
          label: e.display,
          value: e.code,
        }));

        setSharedEthnicityGrp(ethnicityData);
        setSharedBloodGroup(bloodGroupDataFormatted);
      } catch (error) {
        console.error('Error fetching value sets:', error);
        setErrorMsg('Failed to load reference data. Please try refreshing the page.');
        hasFetchedRef.current = false;
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [accessToken, isLoading, isPublicMode]);

  useEffect(() => {
    return () => {
      hasFetchedRef.current = false;
    };
  }, []);

  return (
    <Flex
      direction="column"
      role="main"
      aria-label="My Health History"
    >
      {errorMsg && (
        <Text
          color="red.500"
          fontSize="sm"
          mb="4"
          p="2"
          bg="red.50"
          borderRadius="md"
          border="1px solid"
          borderColor="red.200"
        >
          {errorMsg}
        </Text>
      )}
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mb="8px"
        ml="7px"
      >
        My Health History
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pb="24px"
        ml="7px"
        pt="8px"
      >
        A detailed record of your conditions, procedures, and medications—the single source of truth for your health.
      </Text>
      <Card
        bgColor="transparent"
        borderRadius="xl"
        boxShadow="0px"
        w="full"
      >
        <VStack alignItems="left">
          {healthProfileEntries.map(([key, value]) => (
            <DrawerSection
              key={key}
              name={value.name}
              active={ehrId}
              subEhrId={subEhrId || ''}
              action={action}
              // route={value.route}
            />
          ))}
        </VStack>
      </Card>
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
        ml="7px"
      >
        My FAMILY history
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pt="8px"
        ml="7px"
        mb="24px"
      >
        A snapshot of the health you&apos;ve inherited. Track relevant details from your immediate family to help map
        potential risks and proactively plan for your future care.
      </Text>

      {isLoading ? (
        <Flex
          justify="center"
          align="center"
          py="8"
        >
          <FluentHealthLoader />
        </Flex>
      ) : familyMemberList && familyMemberList.length > 0 ? (
        <Flex
          direction="column"
          gap={{ base: '6', md: '5' }}
          aria-label="Family members list"
          ml="7px"
        >
          {familyMemberList.map((member: any, index: number) => (
            <React.Fragment key={member?.id || `member-${index}`}>
              <FamilyMemberCard
                member={member}
                bloodGroup={sharedBloodGroup}
                ethnicityGrp={sharedEthnicityGrp}
              />
              {index !== familyMemberList.length - 1 && <Divider borderColor="gray.100" />}
            </React.Fragment>
          ))}
        </Flex>
      ) : (
        <EmptyState
          message="No family members added"
          margin="16px 0px 0px 0px"
        />
      )}
    </Flex>
  );
});

export default MyHealthHistory;
