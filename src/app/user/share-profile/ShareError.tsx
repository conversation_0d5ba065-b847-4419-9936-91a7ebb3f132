import React from 'react';
import { Box, Flex, Heading, Image, Text } from '@chakra-ui/react';

import NothingAvailableSVG from '@assets/objects/undraw_not_found.svg';

function ShareErrorPage({ error }: { error?: any }) {
  console.log('Share error', error);
  return (
    <Box
      padding="120px"
      borderRadius="20px"
      maxWidth="632px"
      width="100%"
      bg="beige.100"
    >
      <Flex
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
      >
        <Image
          src={NothingAvailableSVG}
          mb="4"
          borderRadius="full"
          objectFit="cover"
        />
        <Heading
          fontSize="2xl"
          textAlign="center"
          mb={9}
          color="fluentHealthText.100"
        >
          Uh-oh! The link has expired
        </Heading>
        <Text textAlign="center">To access this, please contact the person who shared the link.</Text>
      </Flex>
    </Box>
  );
}

export default ShareErrorPage;
