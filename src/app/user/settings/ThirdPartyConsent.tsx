// import { Box, Divider, Flex, Stack, Text, useToast } from '@chakra-ui/react';
import { useToast } from '@chakra-ui/react';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

// interface DataUsageSectionProps {
//   title: string;
//   description: string;
// }

// function ThirdPartyConsentSection({ title, description }: DataUsageSectionProps) {
//   return (
//     <Flex
//       alignItems="center"
//       justifyContent="space-between"
//     >
//       <Flex
//         flexDirection="column"
//         alignItems="flex-start"
//         gap="8px"
//       >
//         <Text fontSize="lg">{title}</Text>
//         <Text
//           color="fluentHealthText.300"
//           fontSize="md"
//           maxWidth="512px"
//         >
//           {description}
//         </Text>
//       </Flex>
//     </Flex>
//   );
// }

export function ThirdPartyConsent() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  // const sections = [
  //   {
  //     id: 1,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum is measured in kilograms or pounds and can fluctuate due to physical activity levels, nutrition, hormones and other factors.',
  //   },
  //   {
  //     id: 2,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum measures your head-to-toe length in feet/inches or metres/centimetres. Height and weight together can help in calculating doses of medication.',
  //   },
  //   {
  //     id: 3,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum is measured in kilograms or pounds and can fluctuate due to physical activity levels, nutrition, hormones and other factors.',
  //   },
  // ];

  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_third_party_consent_title: title, section_third_party_consent_content: content } =
    appSettingData || {};
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      {/* <Stack
        padding="12px 12px"
        spacing="20px"
      >
        {sections.map((section, index) => (
          <Box key={section.id}>
            <ThirdPartyConsentSection
              title={section.title}
              description={section.description}
            />
            {index < sections.length - 1 && (
              <Box marginTop="0px">
                <Divider color="gray.100" />
              </Box>
            )}
          </Box>
        ))}
      </Stack> */}
      <div>{parse(content.content)}</div>
    </GenericSettingsPage>
  );
}
