import { Button, <PERSON>lapse, Flex, Spacer, Stack, Text, VStack, useToast } from '@chakra-ui/react';
import { useState } from 'react';
import { ChevronDownIcon, ChevronRightIcon, ChevronUpIcon } from '@chakra-ui/icons';
import _ from 'lodash';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';
import { FAQItem } from '@lib/contentLibrary/appSettings/types';
import { useAnalyticsService } from '@lib/state';

export function FaqsPage() {
  const [showMore, setShowMore] = useState(false);
  const [showFaqsAnswer, setShowFaqsAnswer] = useState('');
  const { trackEventInFlow } = useAnalyticsService();
  const toast = useToast();
  const { faqs: appSettingData, error: appSettingError } = useFetchAppSettingData();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }

  const faqData: FAQItem[] = appSettingData || [];
  const title = 'FAQs';

  const groupedFaqs = _.map(
    _.groupBy(faqData, (item: any) => item.faq_group?.faq_group_title),
    (faqs, key) => ({ key, faqs })
  );
  const generalFaqs = groupedFaqs.find((faq) => faq.key.toLowerCase() === 'general');
  const otherFaqs = groupedFaqs.filter((faq) => faq.key.toLowerCase() !== 'general');
  const renderQuestionAnswer = (data: FAQItem, index: number, array: FAQItem[]) => (
    <Stack key={`${data.question.slice(0, 10)}${index}-questions`}>
      <Flex
        justify="space-between"
        alignItems="center"
        cursor="pointer"
        onClick={() => {
          recordSettingsEvents(trackEventInFlow, {
            EventName: 'FAQInteracted',
            st_faq: data.question,
          });
          setShowFaqsAnswer(showFaqsAnswer === data.question ? '' : data.question);
        }}
      >
        <Text
          fontSize="16px"
          color="charcoal.100"
          fontWeight="medium"
          letterSpacing="-0.32px"
        >
          {data.question}
        </Text>
        <ChevronRightIcon
          color="fluentHealthText.400"
          transform={showFaqsAnswer === data.question ? 'rotate(90deg)' : ''}
        />
      </Flex>
      <Collapse in={showFaqsAnswer === data.question}>
        <Text
          fontSize="18px"
          color="charcoal.100"
          mt=".75rem"
        >
          {parse(data.answer)}
        </Text>
      </Collapse>
      <Spacer />
      {index !== array.length - 1 ? <hr style={{ color: '#E6E8E8' }} /> : null}
      <Spacer />
    </Stack>
  );

  const renderTopicDiv = (group: { key: string; faqs: FAQItem[] }) => (
    <Stack key={`${group.key}-topic`}>
      <Text
        fontSize="24px"
        marginTop="1rem"
        marginBottom="1.5rem"
        color="charcoal.100"
        textTransform="capitalize"
        letterSpacing="-0.48px"
      >
        {group.key}
      </Text>
      {group.faqs && group.faqs.length
        ? group.faqs.map((faq, index) => renderQuestionAnswer(faq, index, group.faqs))
        : null}
      <Spacer />
    </Stack>
  );
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
      containerPadding="32px"
    >
      <Stack>
        <Text
          fontSize="24px"
          marginBottom="1.5rem"
          color="charcoal.100"
          textTransform="capitalize"
          letterSpacing="-0.48px"
        >
          {generalFaqs?.faqs.length ? generalFaqs.key : 'FAQs'}
        </Text>
        {generalFaqs?.faqs.length
          ? generalFaqs.faqs.map((faq, index) => renderQuestionAnswer(faq, index, generalFaqs.faqs))
          : null}
        <Spacer />
        {otherFaqs && otherFaqs.length ? (
          <Stack>
            {!showMore && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMore(true)}
              >
                <Text
                  color="fluentHealth.500"
                  fontWeight="normal"
                >
                  Show more <ChevronDownIcon />
                </Text>
              </Button>
            )}
            <Spacer />
            <Collapse in={showMore}>
              {otherFaqs.map(renderTopicDiv)}
              <VStack>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowMore(false)}
                >
                  <Text
                    color="fluentHealth.500"
                    fontWeight="normal"
                  >
                    Show less <ChevronUpIcon />
                  </Text>
                </Button>
              </VStack>
            </Collapse>
          </Stack>
        ) : null}
      </Stack>
    </GenericSettingsPage>
  );
}
