import { <PERSON><PERSON>, <PERSON>, CardBody, Container, Flex, Heading, Text, VStack } from '@chakra-ui/react';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { BigCircleDecoration, HalfCircleWaveDecoration } from 'src/components/ui/Decorations';
import { SidebarAddButton } from '../profile/components/SidebarComponents';

export function GenericSettingsPage({
  title,
  children,
  width,
  maxWidth,
  containerPadding,
  isConsentManagement = false,
  isAuthoriseConsentRequest = false,
  mb,
  routeURL,
  updateFlagOfConsentManagement = () => {},
}: {
  title: string;
  children?: JSX.Element;
  width?: string;
  maxWidth?: string;
  containerPadding?: string;
  isConsentManagement?: boolean;
  isAuthoriseConsentRequest?: boolean;
  mb?: any;
  routeURL?: string;
  updateFlagOfConsentManagement?: () => void;
}) {
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const navigate = useNavigate();

  const onAddHandler = () => {
    navigate(routeURL || `/${DASHBOARD}/${VIEW}`, { state: { createConsent: true } });
    updateFlagOfConsentManagement();
  };

  return (
    <VStack
      as={Container}
      w={width ?? '640px'}
      maxWidth={maxWidth ?? 'unset'}
      mb={isAuthoriseConsentRequest ? mb : ['80px', '260px']}
    >
      <HalfCircleWaveDecoration />
      <BigCircleDecoration />
      <Flex
        width="full"
        mt="7"
        mb="8"
        justifyContent={isConsentManagement ? 'space-between' : 'center'}
        alignItems="center"
      >
        {!isAuthoriseConsentRequest && (
          <Button
            mr="auto"
            variant="ghost"
            size="sm"
            ml="-1"
            color="fluentHealth.500"
            onClick={() => navigate(-1)}
            display={{ sm: 'inline-flex', base: 'none' }}
          >
            <ChevronLeftIcon />
            <Text fontSize="16px">Back</Text>
          </Button>
        )}
        <Heading
          fontSize="32px"
          mr={isAuthoriseConsentRequest ? '0px' : 'auto'}
          ml={['auto', isAuthoriseConsentRequest ? '0px' : '-80px']}
          pt={3}
          pb={5}
        >
          {title}
        </Heading>
        {isConsentManagement && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
      </Flex>
      <Card
        width="full"
        bgColor="#FFF2DF"
        borderRadius="2xl"
        border="1px solid"
        borderColor="fluentHealthSecondary.300"
        boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      >
        <CardBody padding={containerPadding ?? '8px'}>{children}</CardBody>
      </Card>
    </VStack>
  );
}
