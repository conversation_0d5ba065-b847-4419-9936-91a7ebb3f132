import { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Divider,
  Flex,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { usePatientSettingsAll } from '@user/lib/medplum-state';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';
import { enumMesurment, enumUnit } from '@user/lib/constants';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { findFHIRExtension } from './SettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

export function MeasurementPreferencePage() {
  const { authenticatedUser } = useAuthService();
  const { data, updateBasicSettings } = usePatientSettingsAll(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();

  const [selectedWeight, setSelectedWeight] = useState({ path: '', value: 'kilograms' });
  const [selectedHeight, setSelectedHeight] = useState({ path: '', value: 'inches' });
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  useEffect(() => {
    if (data) {
      const extnHeight = findFHIRExtension(enumMesurment.HEIGHT_UNIT, data, '');
      const extnWeight = findFHIRExtension(enumMesurment.WEIGHT_UNIT, data, '');
      const extnHeightIndex = extnHeight.extnVal?.extension?.findIndex(
        (extnValue: any) => extnValue.url === 'preference'
      );
      const extnWeightIndex = extnWeight.extnVal?.extension?.findIndex(
        (extnValue: any) => extnValue.url === 'preference'
      );
      const preferredHeight = {
        ...extnHeight,
        path: `${extnHeight.path}/${extnHeightIndex}/valueCoding`,
        value: extnHeight.extnVal?.extension[extnHeightIndex]?.valueCoding.code === enumUnit.CM ? 'cm' : 'inches',
      };
      const preferredWeight = {
        ...extnWeight,
        path: `${extnWeight.path}/${extnWeightIndex}/valueCoding`,
        value:
          extnWeight.extnVal?.extension[extnWeightIndex]?.valueCoding.code === enumUnit.KG ? 'kilograms' : 'pounds',
      };
      setSelectedHeight(preferredHeight);
      setSelectedWeight(preferredWeight);
    }
  }, [data]);

  // useEffect(() => {
  //   console.log('Selected Height', selectedHeight);
  // }, [selectedHeight]);

  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_measurement_preferences_title: title } = appSettingData || {};
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <Stack padding="12px 12px">
        <Flex
          alignItems="center"
          justifyContent="space-between"
        >
          <Flex
            flexDirection="column"
            alignItems="left"
            gap="8px"
          >
            <Text fontSize="lg">Weight</Text>
            <Text
              color="fluentHealthText.300"
              fontSize="md"
              maxWidth="512px"
            >
              Weight is measured in kilograms or pounds and can fluctuate due to physical activity levels, nutrition,
              hormones and other factors.
            </Text>
          </Flex>
          <Menu>
            <MenuButton
              as={Button}
              rightIcon={<ChevronDownIcon />}
              variant="ghost"
            >
              <Text
                fontSize="sm"
                color="fluentHealthText.200"
                fontWeight="normal"
                textTransform="capitalize"
              >
                {selectedWeight.value}
              </Text>
            </MenuButton>
            <MenuList>
              <MenuItem
                onClick={() => {
                  setSelectedWeight((prev) => {
                    return { ...prev, value: 'kilograms' };
                  });
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'MeasurementPreferencesWeightInteracted',
                    st_weight_measurement_unit: 'Kilogram',
                  });
                  updateBasicSettings({
                    path: selectedWeight?.path || enumMesurment.WEIGHT,
                    value: { code: enumUnit.KG },
                    basicId: data?.id,
                  });
                }}
              >
                <Text
                  fontSize="sm"
                  color="fluentHealthText.200"
                >
                  kilograms
                </Text>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setSelectedWeight((prev) => {
                    return { ...prev, value: 'pounds' };
                  });
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'MeasurementPreferencesWeightInteracted',
                    st_weight_measurement_unit: 'Pounds',
                  });
                  updateBasicSettings({
                    path: selectedWeight?.path || enumMesurment.WEIGHT,
                    value: { code: enumUnit.POUNDS },
                    basicId: data?.id,
                  });
                }}
              >
                <Text
                  fontSize="sm"
                  color="fluentHealthText.200"
                >
                  pounds
                </Text>
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
        <Box
          padding="12px 0px"
          marginTop="0px"
        >
          <Divider color="gray.100" />
        </Box>
        <Flex
          alignItems="center"
          justifyContent="space-between"
        >
          <Flex
            flexDirection="column"
            alignItems="left"
            gap="8px"
          >
            <Text fontSize="lg">Height</Text>
            <Text
              color="fluentHealthText.300"
              fontSize="md"
              maxWidth="512px"
            >
              Height measures your head-to-toe length in feet/inches or metres/centimetres. Height and weight together
              can help in calculating doses of medication.
            </Text>
          </Flex>
          <Menu>
            <MenuButton
              as={Button}
              rightIcon={<ChevronDownIcon />}
              variant="ghost"
            >
              <Text
                fontSize="sm"
                color="fluentHealthText.200"
                fontWeight="normal"
              >
                {selectedHeight.value}
              </Text>
            </MenuButton>
            <MenuList>
              <MenuItem
                onClick={() => {
                  setSelectedHeight((prev) => {
                    return { ...prev, value: 'inches' };
                  });
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'MeasurementPreferencesHeightInteracted',
                    st_height_measurement_unit: 'Inches',
                  });
                  updateBasicSettings({
                    path: selectedHeight?.path || enumMesurment.HEIGHT,
                    value: { code: enumUnit.INCHES },
                    basicId: data?.id,
                  });
                }}
              >
                <Text
                  fontSize="sm"
                  color="fluentHealthText.200"
                >
                  inches
                </Text>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setSelectedHeight((prev) => {
                    return { ...prev, value: enumUnit.CM };
                  });
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'MeasurementPreferencesHeightInteracted',
                    st_height_measurement_unit: 'Centimeter',
                  });
                  updateBasicSettings({
                    path: selectedHeight?.path || enumMesurment.HEIGHT,
                    value: { code: enumUnit.CM },
                    basicId: data?.id,
                  });
                }}
              >
                <Text
                  fontSize="sm"
                  color="fluentHealthText.200"
                >
                  cm
                </Text>
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </Stack>
    </GenericSettingsPage>
  );
}
