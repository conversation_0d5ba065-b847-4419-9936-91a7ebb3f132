import { Stack, useToast } from '@chakra-ui/react';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

export function MedicalAdviceDisclaimer() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_legal_medadvice_title, section_legal_medadvice_content } = appSettingData || {};

  if (!section_legal_medadvice_content) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
    return null;
  }

  return (
    <GenericSettingsPage
      title={section_legal_medadvice_title}
      width="100%"
      maxWidth="800px"
    >
      <Stack
        padding="20px"
        spacing="16px"
      >
        {parse(section_legal_medadvice_content)}
      </Stack>
    </GenericSettingsPage>
  );
}
