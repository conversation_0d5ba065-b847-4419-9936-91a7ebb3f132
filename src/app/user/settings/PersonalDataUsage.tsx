// import { Box, Divider, Flex, Stack, Text } from '@chakra-ui/react';
import { useToast } from '@chakra-ui/react';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

// interface DataUsageSectionProps {
//   title: string;
//   description: string;
// }

// function DataUsageSection({ title, description }: DataUsageSectionProps) {
//   return (
//     <Flex
//       alignItems="center"
//       justifyContent="space-between"
//     >
//       <Flex
//         flexDirection="column"
//         alignItems="flex-start"
//         gap="8px"
//       >
//         <Text fontSize="lg">{title}</Text>
//         <Text
//           color="fluentHealthText.300"
//           fontSize="md"
//           maxWidth="512px"
//         >
//           {description}
//         </Text>
//       </Flex>
//     </Flex>
//   );
// }

export function PersonalDataUsage() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  // const sections = [
  //   {
  //     id: 1,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum is measured in kilograms or pounds and can fluctuate due to physical activity levels, nutrition, hormones and other factors.',
  //   },
  //   {
  //     id: 2,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum measures your head-to-toe length in feet/inches or metres/centimetres. Height and weight together can help in calculating doses of medication.',
  //   },
  //   {
  //     id: 3,
  //     title: 'Lorem Ipsum',
  //     description:
  //       'Lorem Ipsum is measured in kilograms or pounds and can fluctuate due to physical activity levels, nutrition, hormones and other factors.',
  //   },
  // ];
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_personal_data_usage_title: title, section_personal_data_usage_content: content } =
    appSettingData || {};
  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <div>{parse(content.content)}</div>
    </GenericSettingsPage>
  );
}
