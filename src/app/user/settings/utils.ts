import { findMostMatchingString } from '@utils/utils';

import { ContentBlock } from '@lib/contentLibrary/appSettings/types';

export const getSectionDataFromBlockData = (blocks: { item: ContentBlock }[], key: string): ContentBlock | null => {
  const match = findMostMatchingString(
    blocks.map((block) => block.item.title),
    key
  );
  if (!match) {
    return null;
  }
  const block = blocks.find((blockItem) => blockItem.item.title === match);
  if (!block) {
    return null;
  }
  return block.item;
};
