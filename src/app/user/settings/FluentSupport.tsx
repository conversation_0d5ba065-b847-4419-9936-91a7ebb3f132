import { Stack, useToast } from '@chakra-ui/react';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

export function FluentSupportPage() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_fluent_support_title: title, section_fluent_support_content: content } = appSettingData || {};

  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <Stack
        padding="20px"
        spacing="16px"
      >
        {parse(content.content)}
      </Stack>
    </GenericSettingsPage>
  );
}
