import {
  Box,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  <PERSON>ing,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Spacer,
  Stack,
  Text,
  VStack,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import React, { useRef } from 'react';
import { ArrowRight } from 'react-feather';
import { useNavigate } from 'react-router-dom';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { BigCircleDecoration, HalfCircleWaveDecoration } from 'src/components/ui/Decorations';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';
import { AppStaticSettings } from '@lib/contentLibrary/appSettings/types';

const { DASHBOARD, SETTINGS, HELP, FAQS, SOCIAL, MEASUREMENT, COMMUNICATION, PERSONAL_DATA_USAGE, LEGAL } =
  ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;
const SUPPORT_TABS = [
  {
    title: 'FAQs',
    route: `/${HELP}/${FAQS}/${VIEW}`,
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_faqs_title,
  },
  {
    title: 'Email us',
    route: `mailto:<EMAIL>`,
    getTitle: () => 'Email us',
  },
];

const SHARING_TABS = [
  {
    title: 'Follow Us On social',
    route: `/${SETTINGS}/${SOCIAL}`,
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_socialmedia_title,
  },
];

const ACCOUNT_SETTINGS_TABS = [
  {
    title: 'Measurement Preferences',
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_measurement_preferences_title,
    route: `/${SETTINGS}/${MEASUREMENT}`,
  },
  {
    title: 'Communication Preferences',
    route: `/${SETTINGS}/${COMMUNICATION}`,
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_communication_preferences_title,
  },
];

const DATA_AND_PRIVACY_TABS = [
  {
    title: 'Personal Data Usage',
    route: `/${SETTINGS}/${PERSONAL_DATA_USAGE}`,
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_personal_data_usage_title,
  },
  // TODO: Third Party Consent - Will be pick after Launch
  {
    title: 'Legal',
    route: `/${SETTINGS}/${LEGAL}`,
    getTitle: (appSettings: AppStaticSettings) => appSettings?.section_legal_title,
  },
];

let currentExtension: any;
// var extensionPath: string;
export function findFHIRExtension(url: any, currentNode: any, path: string): any {
  let i;
  let result;
  const pathVal = `${path}/extension`;

  if (url === currentNode?.url) {
    // TODO: return the required Object
    return { extnVal: currentExtension, path: pathVal };
  }
  // Use a for loop instead of forEach to avoid nested functions
  // Otherwise "return" will not work properly
  for (i = 0; i < currentNode?.extension?.length; i += 1) {
    currentExtension = currentNode?.extension[i];

    // Search in the current child
    result = findFHIRExtension(url, currentExtension, `${pathVal}/${i}`);

    // Return the result if the node has been found
    if (result !== false) {
      return result;
    }
  }
  // The node has not been found and we have no more options
  return false;
}

export function SettingsPage() {
  const theme = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const fallbackTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const toast = useToast();
  const navigate = useNavigate();
  const { logout } = useAuthService();
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const { trackEventInFlow } = useAnalyticsService();

  React.useEffect(() => {
    const handleBlur = () => {
      if (fallbackTimeout.current) {
        clearTimeout(fallbackTimeout.current);
      }
    };

    window.addEventListener('blur', handleBlur);
    return () => {
      window.removeEventListener('blur', handleBlur);
    };
  }, []);
  const onLogout = async () => {
    await logout();
    recordSettingsEvents(trackEventInFlow, {
      EventName: 'AccountLoggedOut',
    });
    toast({
      title: 'Successfully logged out!',
      status: 'success',
      duration: 4000,
      isClosable: true,
    });
    navigate(`/${DASHBOARD}/${VIEW}`);
  };

  if (appSettingError) {
    console.log('Error fetching app settings', appSettingError);
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }

  const { account_settings_title, data_and_privacy_title, support_title, sharing_title } = appSettingData || {};

  const handleRediects = (tab: any) => {
    recordSettingsEvents(trackEventInFlow, {
      EventName: 'SettingsInteracted',
      st_settings_section: 'Account Settings',
      st_settings_objective: tab?.title,
    });

    navigate(tab?.route);
  };
  return (
    <>
      <HalfCircleWaveDecoration />
      <BigCircleDecoration />
      <VStack px={['16px', '36px']}>
        <Heading
          mt="7"
          mb="8"
          fontSize="3xl"
        >
          Settings
        </Heading>
        {/* Account Settings card */}
        <Card
          bgColor="#FFF2DF"
          borderRadius="2xl"
          border="1px solid"
          borderColor="fluentHealthSecondary.300"
          boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
          maxW="640px"
          width="100%"
          mt={['24px', '32px']}
        >
          <Heading
            px="20px"
            pt="16px"
            fontSize="xl"
          >
            {account_settings_title}
          </Heading>
          <CardBody>
            <Stack>
              {/* TODO: Allow Notification - Will be pick after Launch */}
              {ACCOUNT_SETTINGS_TABS.map((tab) => (
                <Flex
                  borderRadius="lg"
                  flexDirection="row"
                  justifyContent="space-between"
                  py="11px"
                  px="8px"
                  _hover={{ bg: 'periwinkle.200' }}
                  cursor="pointer"
                  key={tab.route}
                  onClick={() => handleRediects(tab)}
                >
                  <Text
                    fontSize="lg"
                    fontWeight="500"
                  >
                    {tab.getTitle(appSettingData)}
                  </Text>
                  <ArrowRight
                    size={20}
                    color={theme.colors.papaya[600]}
                  />
                </Flex>
              ))}
            </Stack>
          </CardBody>
        </Card>
        <Spacer />

        {/* Data & Privacy */}

        <Card
          bgColor="#FFF2DF"
          borderRadius="2xl"
          border="1px solid"
          borderColor="fluentHealthSecondary.300"
          boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
          maxW="640px"
          width="100%"
        >
          <Heading
            mb="4px"
            px="20px"
            pt="16px"
            fontSize="xl"
          >
            {data_and_privacy_title}
          </Heading>
          <CardBody padding="12px">
            {DATA_AND_PRIVACY_TABS.map((tab) => (
              <Flex
                borderRadius="lg"
                flexDirection="row"
                justifyContent="space-between"
                py="11px"
                px="8px"
                _hover={{ bg: 'periwinkle.200' }}
                cursor="pointer"
                key={tab.route}
                onClick={async () => {
                  await recordSettingsEvents(trackEventInFlow, {
                    EventName: 'SettingsInteracted',
                    st_settings_section: 'Data and Privacy',
                    st_settings_objective: tab.title,
                  });

                  navigate(tab.route);
                }}
              >
                <Text
                  fontSize="lg"
                  fontWeight="500"
                >
                  {tab.getTitle(appSettingData)}
                </Text>

                <ArrowRight
                  size={20}
                  color={theme.colors.papaya[600]}
                />
              </Flex>
            ))}
          </CardBody>
        </Card>

        <Spacer />
        {/* Support card */}

        <Card
          bgColor="#FFF2DF"
          borderRadius="2xl"
          border="1px solid"
          borderColor="fluentHealthSecondary.300"
          boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
          maxW="640px"
          width="100%"
        >
          <Heading
            mb="4px"
            px="20px"
            pt="16px"
            fontSize="xl"
          >
            {support_title}
          </Heading>
          <CardBody padding="12px">
            {SUPPORT_TABS.map((tab) => (
              <Flex
                key={tab.route}
                borderRadius="lg"
                flexDirection="row"
                justifyContent="space-between"
                py="11px"
                px="8px"
                _hover={{ bg: 'periwinkle.200' }}
                cursor="pointer"
                onClick={(e) => {
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'SettingsInteracted',
                    st_settings_section: 'Support',
                    st_settings_objective: tab?.title,
                  });
                  if (tab.title === 'Email us') {
                    window.location.href = 'mailto:<EMAIL>';
                    e.preventDefault();
                    fallbackTimeout.current = setTimeout(() => {
                      onOpen();
                    }, 3000);
                  } else {
                    navigate(tab.route);
                  }
                }}
              >
                <Text
                  fontSize="lg"
                  fontWeight="500"
                >
                  {tab.getTitle(appSettingData)}
                </Text>

                <ArrowRight
                  size={20}
                  color={theme.colors.papaya[600]}
                />
              </Flex>
            ))}
          </CardBody>
        </Card>
        <Card
          bgColor="#FFF2DF"
          borderRadius="2xl"
          border="1px solid"
          borderColor="fluentHealthSecondary.300"
          boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
          maxW="640px"
          width="100%"
        >
          <Heading
            mb="4px"
            px="20px"
            pt="16px"
            fontSize="xl"
          >
            {sharing_title}
          </Heading>
          <CardBody padding="12px">
            {SHARING_TABS.map((tab) => (
              <Flex
                key={tab.route}
                borderRadius="lg"
                flexDirection="row"
                justifyContent="space-between"
                py="11px"
                px="8px"
                _hover={{ bg: 'periwinkle.200' }}
                cursor="pointer"
                onClick={() => {
                  recordSettingsEvents(trackEventInFlow, {
                    EventName: 'SettingsInteracted',
                    st_settings_section: 'Sharing',
                    st_settings_objective: tab?.title,
                  });
                  navigate(tab.route);
                }}
              >
                <Text
                  fontSize="lg"
                  fontWeight="500"
                >
                  {tab.getTitle(appSettingData)}
                </Text>
                <ArrowRight
                  size={20}
                  color={theme.colors.papaya[600]}
                />
              </Flex>
            ))}
          </CardBody>
        </Card>
        <Spacer />

        <Flex
          direction="column"
          justify-content="center"
          align-items="center"
          paddingTop="40px"
          ml={['16px', '36px']}
        >
          <Box
            display="flex"
            flexDirection="column"
            justify-content="center"
            align-items="center"
          >
            <Button
              color="iris.500"
              backgroundColor="transparent"
              _hover={{ backgroundColor: 'transparent' }}
              onClick={onLogout}
            >
              Logout
            </Button>
          </Box>
          <Divider
            paddingTop="24px"
            width="auto"
            px={['16px', '36px']}
            color="periwinkle.200"
          />
          {/* TODO: Download your personal data - Will be pick after Launch */}
          <Box
            paddingTop="24px"
            marginBottom="127px"
            display="flex"
            flexDirection="column"
            justify-content="center"
            align-items="center"
          >
            <Button
              color="fluentHealthComplementary.Red"
              backgroundColor="transparent"
              _hover={{ backgroundColor: 'transparent' }}
              onClick={() => navigate('/settings/account/delete/view')}
            >
              Delete account and all your personal data
            </Button>
            <Text
              color="fluentHealthText.200"
              maxW="360px"
              width="100%"
              fontWeight="400"
              fontSize="md"
              textAlign="center"
            >
              Permanently remove your account and erase all related records from our system.
            </Text>
          </Box>
        </Flex>
      </VStack>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        isCentered
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Email Not Configured</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Text>
              It seems you don’t have an email client configured on your device. Please contact us directly at
              <b><EMAIL></b>.
            </Text>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
