import { useEffect, useState } from 'react';
import { Flex, Image, Stack, Text, useTheme, useToast } from '@chakra-ui/react';
import { ArrowRight } from 'react-feather';
import { recordSettingsEvents } from '@user/lib/events-analytics-manager';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';
import contentArticles from '@lib/contentLibrary/articles';
import { useAnalyticsService } from '@lib/state';

// const SOCIAL_MEDIA_PLATFORMS = [
//   {
//     icon: InstagramIcon,
//     title: 'Instagram',
//     url: 'https://www.instagram.com/fluentinhealth/',
//   },
//   {
//     icon: FacebookIcon,
//     title: 'Facebook',
//     url: 'https://www.facebook.com/fluentinhealth',
//   },
//   {
//     icon: LinkedInIcon,
//     title: 'LinkedIn',
//     url: 'https://www.linkedin.com/company/fluentinhealth/',
//   },
// ];

function LinkItem({ iconAssetId, title, url }: { iconAssetId: string; title: string; url: string }) {
  const [image, setImage] = useState<string>();
  const theme = useTheme();
  const { trackEventInFlow } = useAnalyticsService();
  const fetchImage = async () => {
    if (!iconAssetId) return;
    const imageRes = await contentArticles.fetchAsset(iconAssetId);
    setImage(imageRes);
  };
  useEffect(() => {
    fetchImage();
  }, [iconAssetId]);
  return (
    <Flex
      borderRadius="lg"
      flexDirection="row"
      justifyContent="space-between"
      py="11px"
      px="8px"
      _hover={{ bg: 'periwinkle.200' }}
      cursor="pointer"
      key={title}
      onClick={() => {
        recordSettingsEvents(trackEventInFlow, {
          EventName: 'FollowUsOnPlatformInteracted',
          st_follow_us_platform: title,
        });
        window.open(url, '_blank');
      }}
    >
      <Flex
        flexDirection="row"
        justifyContent="left"
      >
        <Image
          src={image}
          width="22px"
          height="22px"
          style={{ marginRight: '12px' }}
        />
        <Text
          fontSize="lg"
          fontWeight="500"
        >
          {title}
        </Text>
      </Flex>

      <ArrowRight
        size={20}
        color={theme.colors.papaya[600]}
      />
    </Flex>
  );
}

export function SocialMediaPage() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_socialmedia_title: title, section_socialmedia_links: links } = appSettingData || {};

  return (
    <GenericSettingsPage
      title={title}
      width="100%"
      maxWidth="800px"
    >
      <Stack>
        {links.map((link) => (
          <LinkItem
            key={link.socialmedia_links_id.title}
            iconAssetId={link.socialmedia_links_id.icon_patapp.id}
            title={link.socialmedia_links_id.title}
            url={link.socialmedia_links_id.link}
          />
        ))}
      </Stack>
    </GenericSettingsPage>
  );
}
