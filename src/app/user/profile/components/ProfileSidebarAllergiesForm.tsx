import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Stack,
  Switch,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { useCallback, useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { z } from 'zod';
import { useAllergiesIntolerancesList } from '@user/lib/medplum-state';
import { recordAllergiesEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { medplumApi } from '@user/lib/medplum-api';

import { formatDateForSave, isDuplicatePresentAllergies } from '@lib/utils/utils';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { Allergy } from '@lib/models/allergies';
import { DatePickerField, SuggestionOptionProps } from 'src/components/ui/Form';
import { SearchableSelect, Select, SelectOptionProps } from 'src/components/ui/Select';
import { AllergyIntolerance } from 'src/gql/graphql';
import { FHIR_VALUE_SET_URL } from '@lib/constants';
import { MedicalRecordSelect } from './MedicalRecordSelect';
import { LinkedDocumentsCard } from './LinkedDocumentsCard';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { FHIR_HL7_CODE_SYSTEM_ROLE_CODE } from 'src/constants/medplumConstants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

const customOption = { label: 'My allergies is not listed', value: 'MY_ALLERGIES_NOT_LISTED' };
export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      sx={{
        width: '100%',
        textAlign: 'left',
        color: 'fluentHealthText.100',
        cursor: 'pointer',
      }}
      onClick={onClick}
    >
      My allergies is not listed
    </Text>
  );
}
type AllergyFormValues = Allergy;
function getInitialFormData(
  allergy: AllergyIntolerance | null,
  allergyCriticalityList: SelectOptionProps[],
  allergyType: any
): AllergyFormValues | undefined {
  if (allergy?.id) {
    const { id, onsetDateTime, code, category, criticality, lastOccurrence, note } = allergy;
    const allergy_type = allergyType.filter((item: any) => category?.includes(item?.value));
    const criticalityItem = allergyCriticalityList.find((item) => item.value === criticality) ?? {
      value: '',
      label: '',
    };
    const primary = code?.coding?.[0] ?? { code: '', display: '' };
    const isCustom = primary.code === `al:${PATIENT_DEFINED}`;
    return {
      id: id ?? '',
      diagnosis_date: onsetDateTime ?? '',
      allergy: {
        label: isCustom ? customOption.label : code?.coding?.[0]?.display || '',
        value: isCustom ? customOption.value : code?.coding?.[0]?.code || '',
      },
      allergy_type,
      criticality: {
        label: String(criticalityItem.label),
        value: String(criticalityItem.value),
      },
      resourceType: 'AllergyIntolerance',
      intoleranceStatus: allergy?.clinicalStatus?.coding?.[0]?.code === 'active',
      lastOccurrence: lastOccurrence || '',
      notes: note?.[0]?.text || '',
      custom_allergy: isCustom ? primary.display ?? '' : '',
      external_reports: allergy?.extension ? useExtractDocumentResource(allergy?.extension) : [],
    };
  }

  return undefined;
}

function AllergySelect({
  onAfterSelect,
  allergyOptions,
  setShowCustomInput,
}: {
  onAfterSelect?: (value: SuggestionOptionProps) => void;
  allergyOptions: SelectOptionProps[];
  setShowCustomInput: (flag: boolean) => void;
}) {
  const form: any = useFormContext();
  const [allergyValue, setallergyValue] = useState<SelectOptionProps | null>(form.watch('allergy'));

  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleNoOptionsClick = useCallback(() => {
    setallergyValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('allergy', customOption);
  }, [setShowCustomInput]);
  const handleChange = useCallback(
    (option: any) => {
      if (option) {
        form.setValue('allergy', { value: option.value, label: option.label });
        form.trigger('allergy');
        setallergyValue(option);
      } else {
        form.setValue('allergy', '');
        form.trigger('allergy');
        setallergyValue(null);
      }
      setShowCustomInput(false);
      onAfterSelect?.(option);
    },
    [form, setShowCustomInput, onAfterSelect]
  );
  const handleFocus = useCallback(() => {
    if (allergyValue?.value === customOption.value) {
      setallergyValue(null);
      form.setValue('allergy', '');
      setShowCustomInput(false);
    }
  }, [allergyValue, form, setShowCustomInput]);

  const noOptionsMessageFn = useCallback(
    () => <NoOptionsMessageComponent onClick={handleNoOptionsClick} />,
    [handleNoOptionsClick]
  );
  return (
    <Controller
      name="allergy"
      control={form.control}
      rules={{ required: 'Allergy selection is required' }}
      render={({ fieldState: { error } }) => (
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors?.allergy}
        >
          <SearchableSelect
            labelText="Select allergy and/or intolerance*"
            options={allergyOptions}
            value={allergyValue}
            onChange={handleChange}
            error={!!error}
            helperText={error ? error.message : null}
            onFocus={handleFocus}
            menuIsOpen={menuIsOpen}
            onMenuOpen={() => setMenuIsOpen(true)}
            onMenuClose={() => setMenuIsOpen(false)}
            inputValue={inputValue}
            onInputChange={setInputValue}
            noOptionsMessage={noOptionsMessageFn}
          />
          <FormErrorMessage> {form?.formState?.errors?.allergy?.value?.message} </FormErrorMessage>
        </FormControl>
      )}
    />
  );
}

export default function ProfileSidebarAllergyForm({
  allergies,
  allergy_name,
  allergyOptions,
  allergyCriticalities,
  allergyTypes,
  closeDialog,
  isLoading,
}: {
  allergies: any;
  allergy_name: AllergyIntolerance | null;
  name: string;
  allergyOptions: SelectOptionProps[];
  allergyCriticalities: SelectOptionProps[];
  allergyTypes: SelectOptionProps[];
  closeDialog: () => void;
  isLoading: (loading: boolean) => void;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const { authenticatedUser } = useAuthService();
  const startDatePickerPopover = useDisclosure();
  const endDatePickerPopover = useDisclosure();
  const { trackEventInFlow } = useAnalyticsService();
  const { allergyListOfData, addAllergiesIntolerances, updateAllergiesIntolerances } = useAllergiesIntolerancesList(
    authenticatedUser?.id
  );
  const [showCustomInput, setShowCustomInput] = useState(
    allergies?.code?.coding[0].code === `al:${PATIENT_DEFINED}` || false
  );
  const [isFormValid, setIsFormValid] = useState(false);
  const isEditing = allergy_name !== null;
  const form = useForm<AllergyFormValues>({
    mode: 'onChange',
    defaultValues: getInitialFormData(allergy_name, allergyCriticalities, allergyTypes),
    resolver: zodResolver(
      z.object({
        id: z.string().optional(),
        allergy: z.object({
          value: z
            .string()
            .min(1, 'This is a required field')
            .refine((value) => {
              if (value === customOption.value) {
                form.clearErrors('allergy.value');
                return true;
              }
              const duplicateId = isDuplicatePresentAllergies(value, allergyListOfData, allergies);
              return !duplicateId;
            }, 'Allergy already exists'),
          label: z.string(),
        }),
        custom_allergy: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
        allergy_type: z
          .array(
            z.object({
              value: z.string(),
              label: z.string(),
            })
          )
          .min(1, 'Allergy type is required'),
        criticality: z
          .object({
            value: z.string().optional(),
            label: z.string().optional(),
          })
          .optional(),
        diagnosis_date: z.string().min(1, 'Diagnosis date is required'),
        lastOccurrence: z.string().nullable().optional(),
        notes: z.string().max(255).optional(),
        intoleranceStatus: z.boolean().nullable().optional(),
        external_reports: z.array(z.any()).optional(),
      })
    ),
  });

  const {
    handleSubmit,
    formState: { isSubmitting, isValid },
  } = form;
  const dateField = form.watch('diagnosis_date');
  const externalReportsField = form.watch('external_reports');

  const lastOccurrenceField = form.watch('lastOccurrence');
  const categoryField = form.watch('allergy_type');
  const severityField = form.watch('criticality');
  const datePickerChangeHandler = (date: Date | null, type: string) => {
    if (type === 'start') {
      if (dayjs(date).isValid()) {
        form.setValue('diagnosis_date', dayjs(date).format('YYYY-MM-DD'));
      } else {
        form.setValue('diagnosis_date', '');
      }
      startDatePickerPopover.onClose();
    } else {
      if (dayjs(date).isValid()) {
        form.setValue('lastOccurrence', dayjs(date).format('YYYY-MM-DD'));
      } else {
        form.setValue('lastOccurrence', '');
      }
      endDatePickerPopover.onClose();
    }
    if (!allergy_name) {
      recordAllergiesEvents(trackEventInFlow, {
        EventName: type === 'start' ? 'AllergiesAddInProgStartDate' : 'AllergiesAddInProgEndDate',
        ...(type === 'start'
          ? { al_date: dayjs(date).format('YYYY/MM/DD') }
          : { al_end_date: dayjs(date).format('YYYY/MM/DD') }),
      });
    }
  };
  const datePickerClearHandler = (type: string) => {
    if (type === 'start') {
      form.setValue('diagnosis_date', '');
      startDatePickerPopover.onClose();
    } else {
      form.setValue('lastOccurrence', '');
      endDatePickerPopover.onClose();
    }
  };
  const removeAttachedMedicalRecordHandler = (records: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField?.filter((item) => item.id !== records.id)
    );
  };
  const onAllergyIntoleranceSelect = useCallback((option: SuggestionOptionProps) => {
    const newAllergy = option?.value ? { value: String(option.value), label: option.label } : { value: '', label: '' };
    form.setValue('allergy', newAllergy);
    form.trigger('allergy');
    if (option?.value && !allergy_name) {
      recordAllergiesEvents(trackEventInFlow, {
        EventName: 'AllergiesAddInProgName',
        al_name: String(option.value),
      });
    }
  }, []);
  const notesEventCapture = (event: any) => {
    if (!allergy_name) {
      recordAllergiesEvents(trackEventInFlow, {
        EventName: 'AllergiesAddInProgNotes',
        al_notes: event.target.value.toString(),
      });
    }
  };
  const customCategorySelect = useCallback((option: any) => {
    form.setValue('allergy_type', option);
    form.trigger('allergy_type');
  }, []);

  const customSeveritySelect = useCallback((option: SelectOptionProps | any) => {
    form.setValue('criticality', { value: option?.value, label: option?.label });
    form.trigger('criticality');
    if (!allergy_name) {
      recordAllergiesEvents(trackEventInFlow, {
        EventName: 'AllergiesAddInProgCriticality',
        al_criticality: option?.value,
      });
    }
  }, []);

  async function onSubmit(addedAllergy: any) {
    try {
      isLoading(true);
      const { allergy, allergy_type, criticality, diagnosis_date, id, external_reports } = addedAllergy;
      const lastOccurrence = form.watch('intoleranceStatus') ? undefined : addedAllergy.lastOccurrence;
      let coding: any = [];
      let transformedCoding: any = [];
      if (allergy?.value !== customOption.value) {
        const codeValue = allergy?.value;
        coding = await medplumApi.valueSetList.getFHIRCodingFromCMS(codeValue);
        transformedCoding = coding?.map(({ system, code }: { system: string; code: string }) => ({
          system,
          value: code,
        }));
      } else {
        coding = [
          {
            system: 'http://fluentinhealth/fact',
            code: `al:${PATIENT_DEFINED}`,
            display: addedAllergy.custom_allergy,
          },
        ];
      }
      const payload: any = {
        resourceType: 'AllergyIntolerance',
        identifier: [
          {
            system: `${FHIR_VALUE_SET_URL}/FACT`,
            value: 'al',
          },
          ...transformedCoding,
        ],
        code: { coding },
        category: allergy_type.map((item: any) => item.value),
        criticality: criticality?.value,
        patient: { reference: `Patient/${authenticatedUser?.id}` },
        onsetDateTime: formatDateForSave(diagnosis_date),
        ...(lastOccurrence ? { lastOccurrence: lastOccurrenceField } : {}),
        ...(addedAllergy?.notes && { note: [{ text: addedAllergy.notes }] }),
        clinicalStatus: {
          coding: [
            {
              system: 'http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical',
              code: form.watch('intoleranceStatus') ? 'active' : 'inactive',
              display: form.watch('intoleranceStatus') ? 'Active' : 'Inactive',
            },
          ],
        },
        id: id || '',
      };
      if (external_reports?.length) {
        payload.extension = external_reports.map((item: any) => ({
          url: FHIR_HL7_CODE_SYSTEM_ROLE_CODE,
          valueReference: {
            reference: `DocumentReference/${item.id}`,
          },
        }));
      }
      if (id) {
        await updateAllergiesIntolerances({ allergyId: id, payload });
      } else {
        await addAllergiesIntolerances(payload);
      }

      toast({
        title: `Successfully ${!isEditing ? 'added' : 'updated'} allergy/intolerance`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      closeDialog();
      navigate(NavigationHelper.getEhrView(false, 'allergies'));
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      isLoading(false);
      const { allergy, allergy_type, criticality, diagnosis_date, id, external_reports } = addedAllergy;
      const lastOccurrence = form.watch('intoleranceStatus') ? undefined : addedAllergy.lastOccurrence;
      recordAllergiesEvents(trackEventInFlow, {
        EventName: id ? 'AllergiesEdited' : 'AllergiesAddCompleted',
        al_criticality: criticality?.value,
        al_date: dayjs(diagnosis_date).format('YYYY/MM/DD'),
        al_name: allergy?.value,
        al_type: allergy_type.map((item: any) => item.label)?.join(', '),
        al_records_added: !!external_reports?.length,
        al_end_date: dayjs(lastOccurrence).format('YYYY/MM/DD'),
        al_notes: addedAllergy?.notes,
        al_entry_point: 'my_health_profile',
        al_status: form.watch('intoleranceStatus'),
      });
    }
  }
  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });

    return () => subscription.unsubscribe();
  }, []);
  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        gap="10"
      >
        <Box mt={6}>
          <Flex
            direction="column"
            gap="10"
          >
            <AllergySelect
              allergyOptions={allergyOptions}
              onAfterSelect={onAllergyIntoleranceSelect}
              setShowCustomInput={setShowCustomInput}
            />
            {showCustomInput && (
              <FormControl
                isInvalid={!!form.formState.errors.custom_allergy}
                variant="floating"
                isRequired
              >
                <Input
                  defaultValue={form.watch('custom_allergy') || ''}
                  placeholder=" "
                  {...form.register('custom_allergy')}
                  onChange={(e) => {
                    form.setValue('custom_allergy', e?.target?.value);
                    form.trigger('custom_allergy');
                  }}
                  onBlurCapture={(e) => {
                    if (e.target.value && !allergies) {
                      recordAllergiesEvents(trackEventInFlow, {
                        EventName: 'AllergiesAddInProgName',
                        al_entry_point: 'my_health_profile',
                        al_name: e.target.value.toString(),
                      });
                    }
                  }}
                />
                <FormLabel>Enter the Allergy and/or Intolerances</FormLabel>
              </FormControl>
            )}
          </Flex>
        </Box>

        <Select
          labelText="Type of allergy and/or intolerance*"
          value={categoryField}
          onChange={customCategorySelect}
          options={allergyTypes}
          isSearchable={false}
          isMulti
          onBlur={() => {
            if (!allergy_name) {
              const selectedOptions = form.getValues('allergy_type') || [];
              recordAllergiesEvents(trackEventInFlow, {
                EventName: 'AllergiesAddInProgType',
                al_type: Array.isArray(selectedOptions)
                  ? selectedOptions.map((item: any) => item.label).join(', ')
                  : selectedOptions?.label || '',
              });
            }
          }}
        />
        <Select
          labelText="Select criticality"
          value={allergyCriticalities.find((item) => {
            return item.value === severityField?.value && item.label === severityField?.label;
          })}
          onChange={customSeveritySelect}
          options={allergyCriticalities}
          isSearchable={false}
        />
        <Box mt="3">
          <DatePickerField
            name="diagnosis_date"
            labelText="Date of diagnosis*"
            rules={{ required: true }}
            isInvalid={
              form.formState.touchedFields.diagnosis_date && form.control._formValues.diagnosis_date.length === 0
            }
            datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'start')}
            datePickerClearHandler={() => datePickerClearHandler('start')}
            datePickerPopover={startDatePickerPopover}
            isClearDateButtonDisabled={dateField?.length === 0}
            selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
            popoverProps={{ placement: 'bottom-start' }}
            maxDate={new Date()}
          />
        </Box>

        <Flex
          alignItems="center"
          justifyContent="space-between"
          borderBottom="1px solid var(--chakra-colors-iris-500)"
          color="var(--chakra-colors-iris-500)"
        >
          <FormLabel
            fontSize="18px"
            fontWeight={400}
            mb="1"
          >
            Do you still have this allergy and/or intolerance?
          </FormLabel>
          <Switch
            size="md"
            mb={2}
            isChecked={form.watch('intoleranceStatus')}
            onChange={(e) => {
              form.setValue('intoleranceStatus', e.target.checked);
              if (!allergy_name) {
                recordAllergiesEvents(trackEventInFlow, {
                  EventName: 'AllergiesAddInProgStatus',
                  al_status: e.target.checked,
                });
              }
            }}
          />
        </Flex>
        {!form.watch('intoleranceStatus') && (
          <Flex mt={4}>
            <DatePickerField
              name="lastOccurrence"
              labelText="End date for this allergy and/or intolerance"
              datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'end')}
              datePickerClearHandler={() => datePickerClearHandler('end')}
              datePickerPopover={endDatePickerPopover}
              selected={dayjs(lastOccurrenceField).isValid() ? dayjs(lastOccurrenceField).toDate() : null}
              popoverProps={{ placement: 'bottom-start' }}
              minDate={dateField && dayjs(dateField).isValid() ? dayjs(dateField).toDate() : new Date()}
              maxDate={new Date()}
            />
          </Flex>
        )}
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.notes}
        >
          <Input
            id="notes"
            name="notes"
            defaultValue={form.watch('notes') || ''}
            placeholder=" "
            onChange={(e) => {
              form.setValue('notes', e.target.value);
              form.trigger('notes');
            }}
            onBlurCapture={notesEventCapture}
          />
          <FormLabel fontWeight="normal">Add your notes here</FormLabel>
        </FormControl>
        <Stack spacing={2}>
          <MedicalRecordSelect
            labelText="Link health records"
            onSelectExtra={() => {
              // Todo when link record is working fix events for this
              // if (!allergy_name) {
              //   recordSEvents(trackEventInFlow, {
              //     EventName: 'AddInProgRecordsAdded',
              //     sy_entry_point: 'my_health_profile',
              //     sy_record_added: !!value?.length,
              //   });
              // }
            }}
          />
          {externalReportsField && externalReportsField?.length > 0 && (
            <LinkedDocumentsCard
              records={externalReportsField}
              onRemove={removeAttachedMedicalRecordHandler}
              showRemoveButton
            />
          )}
        </Stack>
      </Box>
      <HStack
        justifyContent="flex-end"
        py="4"
        mt="8"
      >
        <Button
          isDisabled={!isValid || !isFormValid}
          isLoading={isSubmitting}
          onClick={handleSubmit(onSubmit)}
        >
          {isEditing ? 'Save' : 'Add'}
        </Button>
      </HStack>
    </FormProvider>
  );
}
