import React from 'react';
import { Box, ChakraProps, Divider, Flex, IconButton, Text, useTheme } from '@chakra-ui/react';
import { Trash as TrashIcon } from 'react-feather';
import dayjs from 'dayjs';
import { DATE_MONTH_YEAR } from '@user/lib/constants';

import { AttachedMedicalRecord, MEDICAL_RECORD_TYPES } from '@lib/models/medical-record';
import { MEDICAL_RECORD_ICON_MAP, MEDICAL_RECORD_NAME_MAP } from '../../../medical-records/lib/constants';

import { ReactComponent as AddNewRecord } from '@assets/icons/add-new-record.svg';

export function AddNewMedicalRecordLabel({ addNewMedical }: any) {
  return (
    <Flex
      onClick={addNewMedical}
      border="1px solid var(--Periwinkle-400, #DADCFF)"
      width="calc(560px - 48px - 20px)"
      maxWidth="calc(100vw - 48px - 5px)"
      padding="10px"
      borderRadius="4px"
      margin="-9px -7px"
      backgroundColor="#ffffff"
    >
      <Box height="26px">
        <AddNewRecord
          width="100%"
          height="100%"
        />
      </Box>
      <Text
        fontSize="16px"
        lineHeight="24px"
        marginLeft="8px"
        marginTop="3px"
      >
        Add new health record
      </Text>
    </Flex>
  );
}
export function MedicalRecordLabel({
  record,
  onRemove,
  showRemoveButton = false,
  ...props
}: ChakraProps & {
  record: AttachedMedicalRecord;
  showRemoveButton?: boolean;
  onRemove?: (record: AttachedMedicalRecord) => void;
}) {
  const theme = useTheme();
  return (
    <Flex
      gap="12px"
      align="center"
      role="group"
      {...props}
    >
      <Box
        maxW="22px"
        height="30px"
      >
        {record?.type && MEDICAL_RECORD_ICON_MAP[record.type as MEDICAL_RECORD_TYPES]}
      </Box>
      <Flex
        gap="6px"
        flexDirection="column"
      >
        <Text
          fontSize="md"
          lineHeight={1}
        >
          {record?.title}
        </Text>
        <Text
          fontSize="xs"
          lineHeight={1}
          color="fluentHealthText.400"
        >
          {MEDICAL_RECORD_NAME_MAP[record?.type as MEDICAL_RECORD_TYPES]} from&nbsp;
          {dayjs(record?.date).format(DATE_MONTH_YEAR)}
        </Text>
      </Flex>
      {showRemoveButton && (
        <IconButton
          aria-label="Remove attached EMR"
          variant="ghost"
          ml="auto"
          mr="12px"
          minW="auto"
          opacity={0}
          pointerEvents="none"
          icon={
            <TrashIcon
              size={20}
              color={theme.colors.periwinkle[700]}
            />
          }
          _hover={{
            '& > svg': { stroke: theme.colors.fluentHealthComplementary.Red },
          }}
          _groupHover={{
            opacity: 1,
            pointerEvents: 'all',
          }}
          onClick={() => onRemove?.(record)}
        />
      )}
    </Flex>
  );
}

export function LinkedDocumentsCard({
  records,
  showRemoveButton = false,
  onRemove,
  ...props
}: ChakraProps & {
  records: AttachedMedicalRecord[];
  showRemoveButton?: boolean;
  onRemove?: (record: AttachedMedicalRecord) => void;
}) {
  return (
    <Flex
      flexDirection="column"
      maxH="200px"
      overflowY="scroll"
      className="hide-scrollbar"
      border="1px solid"
      borderColor={showRemoveButton ? 'iris.500' : 'gray.100'}
      borderRadius="8px"
      py={1}
      {...props}
    >
      {records.map((record, index: number) => (
        <React.Fragment key={record?.id}>
          <MedicalRecordLabel
            record={record}
            showRemoveButton={showRemoveButton}
            onRemove={onRemove}
            py={3}
            px={2}
            ml={1}
          />
          {records && index !== records.length - 1 && (
            <Divider
              width="95%"
              borderColor={showRemoveButton ? 'iris.500' : 'gray.100'}
              mx="auto"
            />
          )}
        </React.Fragment>
      ))}
    </Flex>
  );
}

export function LinkedDocumentsLabel() {
  return (
    <Text
      fontSize="sm"
      color="fluentHealthText.300"
    >
      Linked to
    </Text>
  );
}
