import React, { PropsWithChildren } from 'react';
import { Button, ButtonProps, ChakraProps, Flex, Heading, Text } from '@chakra-ui/react';

import { IModal, MODAL_VARIANTS, Modal } from '../../../../components/Modal';

export function ConsentModalHeading({ children, ...props }: PropsWithChildren<ChakraProps>) {
  return (
    <Heading
      fontSize="3xl"
      textAlign="center"
      mb="24px"
      {...props}
    >
      {children}
    </Heading>
  );
}

export function ConsentModalContent({ children, ...props }: PropsWithChildren<ChakraProps>) {
  if (typeof children === 'string') {
    return (
      <Text
        textAlign="center"
        mb="24px"
        color="gray.400"
        {...props}
      >
        {children}
      </Text>
    );
  }
  return (
    <Flex
      flexDirection="column"
      gap="4"
      textAlign="center"
      px={2}
      {...props}
    >
      {children}
    </Flex>
  );
}

export function ConsentModalFooter({ children, ...props }: PropsWithChildren<ChakraProps>) {
  return (
    <Flex
      justify="center"
      align="center"
      gap="48px"
      mt="36px"
      {...props}
    >
      {children}
    </Flex>
  );
}

export function ConsentModalPrimaryButton({ children, ...props }: PropsWithChildren<ButtonProps>) {
  return (
    <Button
      size="xl"
      {...props}
    >
      {children}
    </Button>
  );
}

export function ConsentModalSecondaryButton({ children, ...props }: PropsWithChildren<ButtonProps>) {
  return (
    <Button
      variant="quiet"
      size="xl"
      color="iris.500"
      {...props}
    >
      {children}
    </Button>
  );
}

export function ConsentModal({ children, ...props }: PropsWithChildren<IModal>) {
  return (
    <Modal
      variant={MODAL_VARIANTS.PERIWINKLE}
      showModalFooter={false}
      showModalHeading={false}
      minWidth="480px"
      modalContentProps={{ width: '480px', py: '62px' }}
      isCentered
      maxHeight
      {...props}
    >
      {children}
    </Modal>
  );
}
