import { Box, Flex, Skeleton } from '@chakra-ui/react';
import React from 'react';

export function ProfilePageSkeleton() {
  return (
    <>
      <Box
        bgColor="fluentHealth.500"
        bgImage="/background-profile-cover.png"
        bgPos="center"
        mt="5"
        borderRadius="4xl"
        w="100%"
        h="260px"
      />
      <Flex direction={{ base: 'column', md: 'row' }}>
        <Flex
          direction="column"
          align="center"
          gap="4"
          bgColor="fluentHealthSecondary.400"
          p="4"
          mx="5"
          mt="-240px"
          borderRadius="2xl"
          minWidth="360px"
          minHeight="720px"
        >
          <Skeleton
            my="32px"
            width="full"
            height="full"
            maxWidth="110px"
            maxHeight="110px"
            borderRadius="full"
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
          <Skeleton
            width="full"
            height="200px"
            borderRadius="8px"
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
          <Skeleton
            width="full"
            height="100%"
            borderRadius="8px"
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
        </Flex>
        <Flex
          direction="column"
          w="full"
          p="4"
          mt="4"
          borderRadius="2xl"
          bgColor="fluentHealthSecondary.400"
        >
          <Skeleton
            width="full"
            height="620px"
            borderRadius="8px"
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
        </Flex>
      </Flex>
    </>
  );
}

export function ProfilePhotoSkeleton() {
  return (
    <Flex
      direction="column"
      align="center"
      minWidth="360px"
      minHeight="155px"
    >
      <Skeleton
        my="32px"
        width="110px"
        height="110px"
        borderRadius="full"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
    </Flex>
  );
}
