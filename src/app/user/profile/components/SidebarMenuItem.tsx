import { Box, Flex, Text, useTheme } from '@chakra-ui/react';
import { ChevronRight as ChevronRightIcon } from 'react-feather';

export function SidebarMenuItem({
  title,
  rightLabel,
  bottomLabel,
  onClick,
}: {
  title: string;
  rightLabel?: string;
  bottomLabel?: string | null;
  onClick: () => void;
}) {
  const theme = useTheme();

  return (
    <Flex
      align="center"
      py="11px"
      px="8px"
      gap="8px"
      cursor="pointer"
      borderRadius="lg"
      justifyContent="space-between"
      _hover={{
        bgColor: 'fluentHealthSecondary.500',
      }}
      onClick={onClick}
    >
      <Flex
        direction="column"
        gap="2px"
        width="100%"
      >
        <Text
          fontSize="lg"
          fontWeight="medium"
          lineHeight="26px"
          textTransform="capitalize"
        >
          {title}
        </Text>
        {bottomLabel && (
          <Text
            fontSize="sm"
            color="gray.300"
          >
            {bottomLabel}
          </Text>
        )}
      </Flex>
      <Flex
        gap="4px"
        alignItems="center"
        flexShrink="0"
        maxWidth="60%"
      >
        {rightLabel && (
          <Text
            color="gray.300"
            lineHeight="1"
            flexShrink="1"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            overflow="hidden"
          >
            {rightLabel}
          </Text>
        )}
        <Box flexBasis="20px">
          <ChevronRightIcon
            size={20}
            color={theme.colors.papaya[600]}
          />
        </Box>
      </Flex>
    </Flex>
  );
}
