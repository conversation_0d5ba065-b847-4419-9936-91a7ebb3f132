import { useEffect } from 'react';
import { Box, Button, Flex, Heading, Image, Text, useTheme } from '@chakra-ui/react';
import { PlusCircle } from 'react-feather';

import { CircleWaveDecoration } from '../../../../components/ui/Decorations/CircleWaveDecoration';

import NothingAvailableSVG from '@assets/objects/undraw_not_found.svg';

export function SidebarEmptyState({
  imageSrc,
  title,
  actionButtonText,
  completeInfoText,
  onClick,
  hideActionButton,
  isPublicMode = false,
}: {
  imageSrc: string;
  title: string;
  actionButtonText?: string;
  completeInfoText?: string;
  onClick?: () => void;
  hideActionButton?: boolean;
  isPublicMode?: boolean;
}) {
  const theme = useTheme();
  useEffect(() => {
    document.body.classList.add('hide-scrollbar');
    return () => {
      document.body.classList.remove('hide-scrollbar');
    };
  }, []);
  return !isPublicMode ? (
    <Flex
      flexDirection="column"
      alignItems="center"
      flex="1"
      width="100%"
    >
      <Flex
        position="relative"
        direction="column"
        alignItems="center"
        width="100%"
        mt="132px"
      >
        <Box
          position="absolute"
          left="-20px"
          zIndex="-1"
          top="0"
          color="fluentHealth.500"
          width="140px"
          overflow="hidden"
          transform="rotate(180deg) translateY(calc(50% - 180px))"
        >
          <CircleWaveDecoration
            position="relative"
            height="679px"
            strokeWidth="0.5"
          />
        </Box>
        <Box
          position="absolute"
          right="-45px"
          zIndex="-1"
          top="0"
          color="fluentHealth.500"
          width="140px"
          overflow="hidden"
          transform="translateY(calc(-50% + 180px))"
        >
          <CircleWaveDecoration
            position="relative"
            height="679px"
            strokeWidth="0.5"
          />
        </Box>
        <Image
          src={imageSrc}
          mb="8"
          width="345px"
          height="345px"
          borderRadius="full"
          objectFit="cover"
        />
        <Heading
          fontSize="2xl"
          textAlign="center"
          mx="8"
          color="fluentHealthSecondary.100"
          mb="8"
        >
          {title}
        </Heading>
      </Flex>
      {!hideActionButton && (
        <Button
          mb="4"
          padding="8px 12px 8px 16px"
          onClick={onClick}
          height="40px"
        >
          <Text mr="4px">{actionButtonText}</Text>
          <PlusCircle
            size="20"
            color={theme.colors.fluentHealthSecondary['200']}
          />
        </Button>
      )}

      {completeInfoText && completeInfoText.length > 0 && (
        <Text
          fontWeight="medium"
          fontSize="md"
          color="iris.300"
        >
          {completeInfoText}
        </Text>
      )}
    </Flex>
  ) : (
    <Flex
      height="100%"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
    >
      <Image
        src={NothingAvailableSVG}
        mb="4"
        borderRadius="full"
        objectFit="cover"
      />
      <Heading
        fontSize="2xl"
        textAlign="center"
        mb={9}
        color="fluentHealthText.100"
      >
        Nothing here yet
      </Heading>
    </Flex>
  );
}
