import React, { PropsWithChildren, Ref, forwardRef } from 'react';
import {
  Box,
  Button,
  ButtonProps,
  Card as ChakraCard,
  ChakraProps,
  Checkbox,
  Flex,
  HStack,
  Heading,
  IconButton,
  Skeleton,
  Text,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { PlusCircle as AddIcon, X as CloseIcon, Info as InfoIcon, PlusCircle } from 'react-feather';
import { useFormContext } from 'react-hook-form';

import { MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { useAnalyticsService } from '@lib/state';
import { AnalyticsEventName, AnalyticsFlow } from '@lib/analyticsService';

// import { ReactComponent as TrendsIcon } from '@assets/icons/trends.svg';

export function SidebarHeading({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      fontSize="2xl"
      fontWeight="400"
      {...props}
    >
      {children}
    </Text>
  );
}

export function SidebarCloseButton(props: ChakraProps & ButtonProps) {
  return (
    <IconButton
      aria-label="close"
      minW="28px"
      height="28px"
      icon={
        <CloseIcon
          width="20px"
          cursor="pointer"
        />
      }
      {...props}
    />
  );
}

export function SidebarAddButton({ children = 'Add', ...props }: PropsWithChildren<ChakraProps & ButtonProps>) {
  return (
    <Button
      width="max-content"
      variant="quiet"
      fontSize="md"
      color="fluentHealth.500"
      rightIcon={<AddIcon width="16px" />}
      {...props}
    >
      {children}
    </Button>
  );
}

export function Card({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <ChakraCard
      bg={props.bg || 'transparent'}
      borderRadius="16px"
      borderWidth="1px"
      borderColor="iris.500"
      boxShadow="none"
      px="4"
      pt="3"
      pb="4"
      {...props}
    >
      {children}
    </ChakraCard>
  );
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export const CardSkeleton = forwardRef((props: ChakraProps, ref: Ref<HTMLDivElement>) => (
  <ChakraCard
    ref={ref}
    bg="transparent"
    borderRadius="16px"
    borderWidth="1px"
    borderColor="iris.500"
    boxShadow="none"
    px="4"
    pt="3"
    pb="4"
    {...props}
  >
    <Flex
      gap="8px"
      justify="space-between"
      alignItems="center"
    >
      <Skeleton
        height="24px"
        width="50%"
        borderRadius="12px"
        visibility="visible"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="12px"
        width="28px"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
    </Flex>
    <Flex
      gap="8px"
      mt="18px"
    >
      <Skeleton
        height="16px"
        width="35%"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="16px"
        width="25%"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
    </Flex>
    <Skeleton
      mt="16px"
      height="16px"
      width="45%"
      borderRadius="8px"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.400"
    />
  </ChakraCard>
));

export function CardHeading({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      fontSize="xl"
      {...props}
    >
      {children}
    </Text>
  );
}

export function CardDescription({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      color="gray.400"
      {...props}
    >
      {children}
    </Text>
  );
}

export function CardPerformedLabel({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      bgColor="iris.500"
      rounded="sm"
      width="fit-content"
      borderRadius="4px"
      padding="2px 6px"
      color="white"
      {...props}
    >
      {children}
    </Text>
  );
}

export function CardCreatedAtLabel({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      color="fluentHealthText.300"
      fontSize="sm"
      {...props}
    >
      {children}
    </Text>
  );
}

export function InheritedFromLabel({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Text
      color="iris.300"
      {...props}
    >
      {children}
    </Text>
  );
}

export function SidebarHelperTooltip({
  text,
  tooltipText,
  showText = true,
  containerProps,
}: {
  text: string;
  tooltipText: string;
  showText?: boolean;
  containerProps?: ChakraProps;
}) {
  const theme = useTheme();
  const infoModal = useDisclosure();
  const isMobile = useIsMobile();
  const { trackEventInFlow } = useAnalyticsService();
  const handleClick = () => {
    trackEventInFlow(AnalyticsFlow.InformationButtonClicked, AnalyticsEventName.InformationButtonClicked, {
      entry_point: text,
    });
  };
  return (
    <>
      <HStack
        pt="2"
        pb="4"
        cursor="pointer"
        onClick={infoModal.onOpen}
        {...containerProps}
      >
        <Box onClick={handleClick}>
          {showText ? (
            <Text
              fontSize="lg"
              lineHeight="26px"
            >
              {text}
              <InfoIcon
                style={{ display: 'inline', marginLeft: '4px', marginTop: '-4px' }}
                size={16}
                color={theme.colors.fluentHealth[500]}
              />
            </Text>
          ) : (
            <InfoIcon
              style={{ display: 'inline', marginLeft: '4px', marginTop: '-4px' }}
              size={16}
              color={theme.colors.fluentHealth[500]}
            />
          )}
        </Box>
      </HStack>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        showModalFooter={false}
        showModalHeading={false}
        minWidth={isMobile ? '100%' : '480px'}
        modalContentProps={{ width: isMobile ? '100%' : '480px', py: '40px' }}
        isCentered
        {...infoModal}
      >
        <Flex
          direction="column"
          gap="24px"
        >
          <Heading fontSize="28px">{text}</Heading>
          <Text
            fontSize="lg"
            color="gray.400"
          >
            {tooltipText.split('\n').map((line) => (
              <React.Fragment key={line}>
                {line}
                <br />
              </React.Fragment>
            ))}
          </Text>
        </Flex>
      </Modal>
    </>
  );
}

export function ShareEntryCheckbox({
  entityName,
  tooltipText,
  fieldName = 'is_shareable',
  isDisabled,
  onChange,
  ...props
}: ChakraProps & {
  entityName: string;
  tooltipText: string;
  fieldName?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isDisabled: boolean;
}) {
  const { register } = useFormContext();
  // useEffect(() => {
  // }, []);
  return (
    <Flex
      gap="8px"
      alignItems="center"
      {...props}
    >
      <Checkbox
        colorScheme="fluentHealth"
        isDisabled={isDisabled}
        {...register(fieldName)}
        onChange={onChange}
      >
        Share {entityName} when sharing profile
      </Checkbox>
      <SidebarHelperTooltip
        text="What are shared and hidden conditions?"
        tooltipText={tooltipText}
        containerProps={{
          p: 0,
          m: 0,
          ml: '-6px',
        }}
        showText={false}
      />
    </Flex>
  );
}

export function GraphModalButtons({
  onAddClick,
}: // onTrendsClick,
{
  onAddClick?: () => void;
  // onTrendsClick: () => void;
}) {
  return (
    <Flex justifyContent="space-between">
      {onAddClick && (
        <Button
          width="max-content"
          variant="quiet"
          fontSize="md"
          color="fluentHealth.500"
          rightIcon={<PlusCircle width="16px" />}
          onClick={() => onAddClick?.()}
        >
          Add
        </Button>
      )}
      {/* <Button
        width="max-content"
        variant="quiet"
        fontSize="md"
        color="fluentHealth.500"
        ml="auto"
        rightIcon={<TrendsIcon width="16px" />}
        onClick={onTrendsClick}
      >
        Trends
      </Button> */}
    </Flex>
  );
}

export function QuestionCTA({
  text,
  onClick,
  hideIcon = false,
  iconColor = 'currentColor',
  ...props
}: ChakraProps & { text: string; onClick?: () => void; iconColor?: string; hideIcon?: boolean }) {
  return (
    <Flex
      justify="space-between"
      alignItems="center"
      gap="18px"
      color="iris.500"
      cursor="pointer"
      onClick={() => onClick?.()}
      {...props}
    >
      <Text fontSize="lg">{text}</Text>
      {!hideIcon && (
        <AddIcon
          width="16px"
          style={{ flexShrink: 0 }}
          color={iconColor}
        />
      )}
    </Flex>
  );
}
