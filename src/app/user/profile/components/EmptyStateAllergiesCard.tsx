import { ChakraProps, Flex, Text } from '@chakra-ui/react';

import { ReactComponent as EmptyIllustration } from '@assets/objects/empty-illustration.svg';

export function EmptyStateAllergiesCard({ title, ...props }: ChakraProps & { title: string }) {
  return (
    <Flex
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap="16px"
      mt="180px"
      ml="80px"
      mr="80px"
      maxWidth="320px"
      {...props}
    >
      <EmptyIllustration />
      <Text
        fontSize="24px"
        fontFamily="P22 Mackinac"
        letterSpacing="-0.48px"
        lineHeight="32px"
        color="fluentHealthText.100"
        textAlign="center"
      >
        {title}
      </Text>
    </Flex>
  );
}
