import { ChakraP<PERSON>, Flex, HStack, Skeleton } from '@chakra-ui/react';
import { LegacyRef, forwardRef } from 'react';

// eslint-disable-next-line @typescript-eslint/naming-convention
export const MedicalRecordSelectItemSkeleton = forwardRef(
  (props: ChakraProps & { hideIconSkeleton?: boolean; small?: boolean }, ref: LegacyRef<HTMLDivElement>) => {
    const { hideIconSkeleton, small, ...otherProps } = props;
    return (
      <Flex
        ref={ref}
        justifyContent="space-between"
        alignItems="center"
        flex={1}
        pl={small ? '0' : '2'}
        pr={small ? '0' : '4'}
        py={small ? '0' : '2'}
        {...otherProps}
      >
        <HStack alignItems="center">
          <Skeleton
            mt="auto"
            borderRadius="8px"
            ml={small ? 'auto' : '1'}
            mb={small ? 'auto' : '1'}
            width={small ? '24px' : '32px'}
            height={small ? '30px' : '40px'}
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
          <Flex
            flexDirection="column"
            gap={small ? '6px' : '8px'}
          >
            <Skeleton
              borderRadius="8px"
              width="240px"
              height="16px"
              startColor="fluentHealthSecondary.300"
              endColor="fluentHealthSecondary.500"
            />
            <Skeleton
              borderRadius="8px"
              width="160px"
              height="12px"
              startColor="fluentHealthSecondary.300"
              endColor="fluentHealthSecondary.500"
            />
          </Flex>
        </HStack>
        {!hideIconSkeleton && (
          <Skeleton
            borderRadius="100%"
            width="20px"
            height="20px"
            startColor="fluentHealthSecondary.300"
            endColor="fluentHealthSecondary.500"
          />
        )}
      </Flex>
    );
  }
);
