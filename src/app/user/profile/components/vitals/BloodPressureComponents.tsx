// Package modules
import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { Edit3, Trash } from 'react-feather';
import dayjs from 'dayjs';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from '@user/lib/constants';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
// eslint-disable-next-line import/order
import { useNavigate, useSearchParams } from 'react-router-dom';
import { medplumApi } from '@user/lib/medplum-api';
import { useSharePatient } from '@user/lib/state';

import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../ConsentModal';
// Local modules
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { Card, CardHeading, CardSkeleton, GraphModalButtons, SidebarHelperTooltip } from '../SidebarComponents';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from 'src/components/ui/Menu';
import { MasterVital, PatientVitals } from '@lib/models/patient-vitals';
import { UpsertBloodPressureModal } from './UpsertBloodPressureModal';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { FACT_CODE_SYSTEM } from '@lib/constants';

export function BloodPressureType({
  value,
  isSystolic = false,
  unit,
}: {
  value?: string;
  isSystolic?: boolean;
  unit?: string;
}) {
  const valueToRender = React.useMemo(() => {
    if (Array.isArray(value)) {
      const uniqueValues = Array.from(new Set(value.filter(Boolean)));
      return uniqueValues.length ? uniqueValues.join(' - ') : 'N/A';
    }
    return value || 'N/A';
  }, [value]);

  return (
    <Flex
      direction="column"
      gap="8px"
    >
      <Flex
        gap="8px"
        alignItems="center"
      >
        <Text>{isSystolic ? 'Systolic' : 'Diastolic'}</Text>
        <Flex
          backgroundColor={isSystolic ? 'fluentHealthComplementary.Orange' : 'fluentHealthSecondary.100'}
          borderRadius="50%"
          w="6px"
          h="6px"
        />
      </Flex>
      <Flex
        alignItems="baseline"
        gap="8px"
      >
        <Text
          fontSize="2xl"
          fontWeight="500"
        >
          {valueToRender}
        </Text>
        <Text fontSize="sm">{unit}</Text>
      </Flex>
    </Flex>
  );
}

export function BloodPressureDetails({
  sistolicValue,
  sistolicUnit,
  diastolicValue,
  diastolicUnit,
  hideDivider,
}: {
  sistolicValue?: string;
  sistolicUnit?: string;
  diastolicValue?: string;
  diastolicUnit?: string;
  hideDivider?: boolean;
}) {
  return (
    <Flex gap="12px">
      <BloodPressureType
        value={sistolicValue}
        unit={sistolicUnit}
        isSystolic
      />
      {!hideDivider && (
        <Flex
          height="56px"
          width="1px"
          backgroundColor="fluentHealthText.500"
        />
      )}
      <BloodPressureType
        value={diastolicValue}
        unit={diastolicUnit}
      />
    </Flex>
  );
}

export function BloodPressureCard({
  vitals,
  onRemove,
  onEdit,
  isPublicMode = false,
  isLoading,
}: PropsWithChildren<{
  vitals: any;
  onRemove?: () => void;
  onEdit: () => void;
  isPublicMode?: boolean;
  isLoading?: boolean;
}>) {
  const deleteModal = useDisclosure();
  const sistolicValue = vitals?.component?.find((i: any) => i.code.coding[0].code === '8480-6')?.valueQuantity?.value;
  const sistolicUnit = vitals?.component?.find((i: any) => i.code.coding[0].code === '8480-6')?.valueQuantity?.unit;
  const diastolicValue = vitals?.component?.find((i: any) => i.code.coding[0].code === '8462-4')?.valueQuantity?.value;
  const diastolicUnit = vitals?.component?.find((i: any) => i.code.coding[0].code === '8462-4')?.valueQuantity?.unit;
  const dateTime = vitals?.effectiveDateTime;
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={() => {
              onRemove?.();
              deleteModal.onClose?.();
            }}
            isLoading={isLoading}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card>
        <Stack>
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading fontWeight="500">{dayjs(dateTime).format('DD MMM, YYYY')}</CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<Trash size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>

          <Spacer />
          <BloodPressureDetails
            sistolicValue={sistolicValue}
            sistolicUnit={sistolicUnit}
            diastolicValue={diastolicValue}
            diastolicUnit={diastolicUnit}
          />
          {/* <Text
            fontSize="sm"
            color="fluentHealthText.300"
          >
            Recorded by {recordedBy}
          </Text> */}
        </Stack>
      </Card>
    </>
  );
}
function BloodPresureCardList({
  setSelectedVital,
  upsertBloodPressureModal,
  answerList,
}: {
  setSelectedVital: React.Dispatch<React.SetStateAction<PatientVitals | null>>;
  upsertBloodPressureModal: any;
  answerList: any;
}) {
  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser } = useAuthService();
  const { deleteObservationResponseTask } = useMasterObservationResponseList(authenticatedUser?.id);
  const { isPublicMode } = usePublicSettings();

  const handleRemoveVital = async (vital: PatientVitals) => {
    try {
      await deleteObservationResponseTask({
        item: VITALS.BloodPressure.key,
        code: VITALS.BloodPressure,
        observationId: vital?.id,
      });
      toast({ title: 'Blood pressure removed', status: 'success', duration: 3000, isClosable: true });
    } catch {
      toast({
        title: 'Error',
        description: 'Error removing blood pressure',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTBPRemoved',
      });
    }
  };

  const handleEditClick = (vital: PatientVitals) => {
    setSelectedVital(vital);
    upsertBloodPressureModal.onOpen();
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTBPInteracted',
      vt_bp_entry_point: 'my_health_profile',
    });
  };

  if (!answerList?.length) {
    return (
      <Flex
        direction="column"
        gap="12px"
        height="full"
        overflowY="hidden"
      >
        <SidebarEmptyState
          title="No Blood Pressure recordings found."
          imageSrc="/empty-card-vital-blood-pressure.png"
          hideActionButton
        />
      </Flex>
    );
  }

  return (
    <Flex
      gap="16px"
      direction="column"
      overflowY="scroll"
      className="hide-scrollbar"
    >
      {answerList?.map((answer: PatientVitals) => (
        <BloodPressureCard
          key={answer.id}
          vitals={answer}
          onRemove={() => handleRemoveVital(answer)}
          onEdit={() => handleEditClick(answer)}
          isPublicMode={isPublicMode}
        />
      ))}
    </Flex>
  );
}

export function BloodPressureSection({ masterVital, action }: { masterVital: MasterVital; action: string }) {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { PROFILE, EHR, VITALS: VITALS_ROUTE, BLOOD_PRESSURE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { trackEventInFlow } = useAnalyticsService();
  const { authenticatedUser: authenticatedPatient } = useAuthService();
  const patientId = !isPublicMode ? authenticatedPatient.id : useSharePatient()?.patient.id;
  const { masterVitalList } = useMasterObservationResponseList(patientId);
  const answerList = masterVitalList[`vi:blood-pressure`];
  const [selectedVital, setSelectedVital] = useState<PatientVitals | null>(null);
  const [positionofRecordingOptions, setPositionofRecordingOptions] = useState();
  const upsertBloodPressureModal = useDisclosure();
  const onAddHandler = () => {
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${BLOOD_PRESSURE}/${ADD}`);
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTBPAddStarted',
      vt_bp_entry_point: 'my_health_profile',
    });
  };
  useEffect(() => {
    if (!isPublicMode) {
      medplumApi.valueSetList
        .getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.BLOODPRESSURE_POSITION)
        .then((val) => {
          const data = val.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }));
          setPositionofRecordingOptions(data);
        });
    }
  }, [isPublicMode, searchParams]);
  // Reset selectedVital when modal closing the modal
  useEffect(() => {
    if (!upsertBloodPressureModal.isOpen) setSelectedVital(null);
  }, [upsertBloodPressureModal.isOpen]);

  useEffect(() => {
    if (action === ADD) {
      upsertBloodPressureModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    upsertBloodPressureModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'vitals', BLOOD_PRESSURE));
  };

  return (
    <>
      <UpsertBloodPressureModal
        selectedVital={selectedVital}
        masterVital={masterVital}
        positionofRecordingOptions={positionofRecordingOptions}
        {...upsertBloodPressureModal}
        onClose={closeFn}
      />

      {answerList?.length ? (
        <Flex
          direction="column"
          gap="12px"
          height="full"
          overflowY="hidden"
        >
          <GraphModalButtons {...(!isPublicMode && { onAddClick: onAddHandler })} />
          <Suspense fallback={<CardSkeleton />}>
            <BloodPresureCardList
              setSelectedVital={setSelectedVital}
              upsertBloodPressureModal={upsertBloodPressureModal}
              answerList={answerList}
            />
          </Suspense>
          <Spacer />
        </Flex>
      ) : (
        <SidebarEmptyState
          title="Update blood pressure readings"
          actionButtonText="Add"
          imageSrc="/empty-card-vital-blood-pressure.png"
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      )}

      {!isPublicMode && (
        <SidebarHelperTooltip
          text="What is blood pressure?"
          tooltipText="Blood pressure is the force exerted as blood pumps through arteries from the heart and the resistance to blood flow. It is measured as systolic or diastolic. It's usually written as the systolic pressure over the diastolic pressure. The Indian Heart Association defines systolic blood pressure as the pressure applied against an artery when the heart contracts. Similarly diastolic blood pressure is measured when the heart relaxes and is calculated as the difference between heartbeats. Blood pressure diagnoses require repeated readings, and issues can be inherited, so do investigate your family history."
        />
      )}
    </>
  );
}
