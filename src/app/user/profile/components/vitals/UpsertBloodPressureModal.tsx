import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { Clock as ClockIcon } from 'react-feather';
import { LOINC_ORG, VITALS } from '@user/lib/constants';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { ObservationVitalResponsePayload } from '@user/lib/models/questionnaire-response';

import { TimeOfRecordingOption } from '@lib/models/misc';
import { Select } from '../../../../../components/ui/Select';
import { MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { DatePickerField, SuggestionDropdown } from 'src/components/ui/Form';
import { FHIR_CODE_SYSTEM_URL, HOURLY_BASE_OPTIONS } from '@lib/constants';
import { useAnalyticsService, useAuthService } from '@lib/state';

export const TIME_OF_RECORDING_BASE_OPTIONS = HOURLY_BASE_OPTIONS.map((el) => ({
  ...el,
  hideIcon: true,
})) as TimeOfRecordingOption[];

function getInitialFormData(bloodPressure: any | null): any {
  if (!bloodPressure || bloodPressure?.code?.coding?.[1]?.code !== '85354-9') {
    return {};
  }

  const effectiveDateTime = bloodPressure?.effectiveDateTime || '';
  const dateOfRecording = dayjs(effectiveDateTime).isValid() ? dayjs(effectiveDateTime).format('YYYY-MM-DD') : '';
  const timeOfRecording = effectiveDateTime
    ? dayjs(effectiveDateTime)
        .utcOffset(-5 * 60)
        .utc()
        .format('HH:mm')
    : null;

  const systolicComponent = bloodPressure?.component?.find((comp: any) => comp?.code?.coding?.[0]?.code === '8480-6');
  const diastolicComponent = bloodPressure?.component?.find((comp: any) => comp?.code?.coding?.[0]?.code === '8462-4');

  const sistolicValue = systolicComponent?.valueQuantity?.value || '';
  const diastolicValue = diastolicComponent?.valueQuantity?.value || '';
  const methodCoding = bloodPressure.method?.coding?.[0] || {};

  return {
    sistolicValue: sistolicValue.toString(),
    diastolicValue: diastolicValue.toString(),
    dateOfRecording,
    timeOfRecording,
    recorded_by: {
      value: '',
      label: '',
    },
    position_recorded: methodCoding.code
      ? {
          value: methodCoding.code || '',
          label: methodCoding.display || '',
        }
      : undefined,
  };
}

export function UpsertBloodPressureModal({
  selectedVital,
  masterVital,
  positionofRecordingOptions,
  closeDialog,
  ...props
}: any) {
  const { authenticatedUser } = useAuthService();
  const [isLoading] = useState(false);
  const [timeOfRecordingOptions, setTimeOfRecordingOptions] = useState(HOURLY_BASE_OPTIONS);
  const [formChanged, setFormChanged] = useState(false);
  const [originalValues, setOriginalValues] = useState<any>(null);

  const { addObservationResponse, updateObservationResponse } = useMasterObservationResponseList(authenticatedUser?.id);

  const theme = useTheme();
  const toast = useToast();
  const datePickerPopover = useDisclosure();

  const defaultFormValues = getInitialFormData(selectedVital);

  useEffect(() => {
    if (props.isOpen && selectedVital) {
      setOriginalValues(defaultFormValues);
      setFormChanged(false);
    }
  }, [props.isOpen, selectedVital]);

  const form = useForm({
    mode: 'onChange',
    defaultValues: defaultFormValues,
    resolver: zodResolver(
      z.object({
        sistolicValue: z.string().refine(
          (val) => {
            const num = parseInt(val, 10);
            return !Number.isNaN(num) && num >= 30 && num <= 220;
          },
          { message: 'Please input valid data' }
        ),
        diastolicValue: z.string().refine(
          (val) => {
            const num = parseInt(val, 10);
            return !Number.isNaN(num) && num >= 20 && num <= 150;
          },
          { message: 'Please input valid data' }
        ),
        dateOfRecording: z.string().min(1, 'Date of recording is required'),
        timeOfRecording: z.string().regex(/^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/, 'Format should be hh:mm'),
        recorded_by: z
          .object({
            value: z.string().optional(),
            label: z.string().optional(),
          })
          .optional(),
        position_recorded: z
          .object({
            value: z.string().optional(),
            label: z.string().optional(),
          })
          .optional(),
      })
    ),
  });

  const {
    handleSubmit,
    register,
    control,
    watch,
    formState: { isSubmitting, isValid },
  } = form;

  const sistolicValue = watch('sistolicValue');
  const diastolicValue = watch('diastolicValue');
  const dateField = watch('dateOfRecording');
  const timeField = watch('timeOfRecording');
  const positionRecorded = watch('position_recorded');
  const { trackEventInFlow } = useAnalyticsService();

  useEffect(() => {
    if (selectedVital && originalValues) {
      const currentValues = {
        sistolicValue,
        diastolicValue,
        dateOfRecording: dateField,
        timeOfRecording: timeField,
        position_recorded: positionRecorded,
      };

      const normalizeValue = (val: any) => {
        if (val == null) return '';
        if (typeof val === 'object' && val.value !== undefined) return String(val.value);
        return String(val);
      };

      const sistolicChanged =
        normalizeValue(originalValues.sistolicValue) !== normalizeValue(currentValues.sistolicValue);
      const diastolicChanged =
        normalizeValue(originalValues.diastolicValue) !== normalizeValue(currentValues.diastolicValue);
      const dateFieldChanged =
        normalizeValue(originalValues.dateOfRecording) !== normalizeValue(currentValues.dateOfRecording);
      const timeFieldChanged =
        normalizeValue(originalValues.timeOfRecording) !== normalizeValue(currentValues.timeOfRecording);

      const originalPosition = originalValues.position_recorded?.value || '';
      const currentPosition = currentValues.position_recorded?.value || '';
      const positionChanged = normalizeValue(originalPosition) !== normalizeValue(currentPosition);

      setFormChanged(sistolicChanged || diastolicChanged || dateFieldChanged || timeFieldChanged || positionChanged);
    }
  }, [sistolicValue, diastolicValue, dateField, timeField, positionRecorded, selectedVital, originalValues]);

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('dateOfRecording', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('dateOfRecording', '');
    }
    if (!selectedVital?.id) {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTBPAddInProgDate',
        vt_bp_date: dayjs(date).format('YYYY/MM/DD'),
      });
    }
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('dateOfRecording', '');
    datePickerPopover.onClose();
  };

  const onTimeOfRecordingSelect = useCallback((value: TimeOfRecordingOption) => {
    const fullTimeValue = value.value;
    form.setValue('timeOfRecording', fullTimeValue);
    form.trigger('timeOfRecording');
    if (!selectedVital?.id) {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTBPAddInProgTime',
        vt_bp_time: fullTimeValue,
      });
    }
  }, []);

  const onTimeOfRecordingSearchChange = useCallback(
    async (searchText: string) => {
      const newList = HOURLY_BASE_OPTIONS.filter((option) => option.value.includes(searchText));
      setTimeOfRecordingOptions(newList);
      return newList;
    },
    [form]
  );

  async function onSubmit(vitalsToUpsert: any) {
    const { id: selectedBloodPressureId } = selectedVital || {};
    try {
      const payload: ObservationVitalResponsePayload = {
        item: 'vi:blood-pressure',
        recordingDateTime: dayjs(`${vitalsToUpsert.dateOfRecording}T${vitalsToUpsert.timeOfRecording}`).format(
          'YYYY-MM-DDTHH:mm:ss.SSS[Z]'
        ),
        code: {
          coding: [
            {
              system: FHIR_CODE_SYSTEM_URL,
              code: 'vi:blood-pressure',
              display: 'Blood Pressure',
            },
            {
              system: LOINC_ORG,
              code: '85354-9',
              display: 'Blood pressure panel with all children optional',
            },
          ],
        },
        component: [
          {
            code: {
              coding: [
                {
                  system: LOINC_ORG,
                  code: '8480-6',
                  display: 'Systolic blood Pressure',
                },
              ],
            },
            valueQuantity: {
              value: Number(vitalsToUpsert.sistolicValue),
              unit: VITALS.BloodPressure.unit,
              system: 'http://snomed.info/sct',
              code: '259018001',
            },
          },
          {
            code: {
              coding: [
                {
                  system: LOINC_ORG,
                  code: '8462-4',
                  display: 'Diastolic blood Pressure',
                },
              ],
            },
            valueQuantity: {
              value: Number(vitalsToUpsert.diastolicValue),
              unit: VITALS.BloodPressure.unit,
              system: 'http://snomed.info/sct',
              code: '259018001',
            },
          },
        ],
      };
      if (vitalsToUpsert?.position_recorded?.value) {
        payload.method = {
          coding: [
            {
              system: 'http://snomed.info/sct',
              code: vitalsToUpsert.position_recorded.value,
              display: vitalsToUpsert.position_recorded.label,
            },
          ],
        };
      }

      if (selectedBloodPressureId) {
        await updateObservationResponse({ ...payload, id: selectedBloodPressureId });
      } else {
        await addObservationResponse(payload);
      }

      toast({
        title: `Successfully ${!selectedBloodPressureId ? 'added' : 'updated'} blood pressure`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      props.onClose();
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: selectedBloodPressureId ? 'VTBPEdited' : 'VTBPAddCompleted',
        vt_bp_diastolic: Number(vitalsToUpsert.diastolicValue),
        vt_bp_systolic: Number(vitalsToUpsert.sistolicValue),
        vt_bp_position: vitalsToUpsert?.position_recorded?.label || defaultFormValues?.position_recorded?.label,
        vt_bp_date: dayjs(vitalsToUpsert.dateOfRecording).format('YYYY/MM/DD'),
        vt_bp_time: vitalsToUpsert.timeOfRecording,
      });
    }
  }

  // Populate fields if selectedVital !== null

  useEffect(() => {
    if (!props.isOpen) {
      form.reset();
    }
    form.setValue('dateOfRecording', dayjs(defaultFormValues.dateOfRecording).format('YYYY-MM-DD'));
  }, [props.isOpen]);

  return (
    <Modal
      title="Blood Pressure"
      primaryButtonLabel={`${selectedVital ? 'Save' : 'Add'}`}
      variant={MODAL_VARIANTS.PERIWINKLE}
      onPrimaryButtonClick={handleSubmit(onSubmit)}
      onSecondaryButtonClick={props.onClose}
      primaryButtonEnabled={isValid && timeField?.length > 4 && (selectedVital ? formChanged : true)}
      showSecondaryButton={false}
      maxH="1000px"
      scrollY="false"
      isPrimaryButtonLoading={isSubmitting || isLoading}
      isCentered
      {...props}
    >
      <FormProvider {...form}>
        <Box
          color="black"
          display="flex"
          flexDirection="column"
          mt="24px"
          gap="14"
        >
          <Flex
            justifyContent="space-between"
            gap="6"
          >
            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.sistolicValue}
            >
              <Input
                placeholder=" "
                id="sistolicValue"
                type="number"
                min="70"
                max="200"
                defaultValue={defaultFormValues.sistolicValue}
                onBlurCapture={(e) => {
                  if (!form.formState.errors.sistolicValue && !selectedVital?.id) {
                    recordVitalsEvents(trackEventInFlow, {
                      EventName: 'VTBPAddInProgSystolic',
                      vt_bp_systolic: parseInt(e.target.value, 10),
                    });
                  }
                }}
                {...register('sistolicValue')}
              />
              <FormLabel>Systolic ({VITALS.BloodPressure.unit})*</FormLabel>
              <FormErrorMessage>{form.formState.errors.sistolicValue?.message?.toString()}</FormErrorMessage>
            </FormControl>
            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.diastolicValue}
            >
              <Input
                placeholder=" "
                type="number"
                id="diastolicValue"
                defaultValue={defaultFormValues.diastolicValue}
                {...register('diastolicValue')}
                onBlurCapture={(e) => {
                  if (!form.formState.errors.diastolicValue && !selectedVital?.id) {
                    recordVitalsEvents(trackEventInFlow, {
                      EventName: 'VTBPAddInProgDiastolic',
                      vt_bp_diastolic: parseInt(e.target.value, 10),
                    });
                  }
                }}
              />
              <FormLabel>Diastolic ({VITALS.BloodPressure.unit})*</FormLabel>
              <FormErrorMessage>{form.formState.errors.diastolicValue?.message?.toString()}</FormErrorMessage>
            </FormControl>
          </Flex>
          {/* <Controller
            control={control}
            name="recorded_by"
            render={({ field }) => (
              <Select
                labelText="Recorded by"
                value={recordedByOptions.find(
                  (item: any) => item.value === field.value?.value && item.label === field.value?.label
                )}
                onChange={(selectedOption: any) => {
                  field.onChange(selectedOption);
                  recordVitalsEvents(trackEventInFlow, {
                    action: selectedVital ? 'Edit' : 'Add',
                    screenName: selectedVital ? 'add vitals filled state' : 'add vitals empty state',
                    vitalsName: selectedVital.label,
                    stepCompleted: 'vital measure added',
                    completionSuccess: false,
                    vitalsMeasure: true,
                  });
                }}
                options={recordedByOptions}
                isSearchable={false}
                isClearable
              />
            )}
          /> */}

          <Controller
            control={control}
            name="position_recorded"
            render={({ field }) => (
              <Select
                labelText="Position of recording"
                defaultValue={{
                  value: defaultFormValues.position_recorded?.value,
                  label: defaultFormValues.position_recorded?.label,
                }}
                onChange={(selectedOption: any) => {
                  field.onChange(selectedOption);
                  form.setValue('position_recorded', selectedOption);
                  if (selectedOption?.value && !selectedVital?.id) {
                    recordVitalsEvents(trackEventInFlow, {
                      EventName: 'VTBPAddInProgPosition',
                      vt_bp_position: selectedOption.value,
                    });
                  }
                }}
                options={positionofRecordingOptions}
                isSearchable={false}
              />
            )}
          />
          <Flex
            justifyContent="space-between"
            gap="6"
          >
            <DatePickerField
              // Field props
              name="dateOfRecording"
              labelText="Date*"
              errorText="This field is required"
              rules={{ required: true }}
              isInvalid={
                form.formState.touchedFields.dateOfRecording && form.control._formValues.dateOfRecording.length === 0
              }
              // Datepicker props
              datePickerChangeHandler={datePickerChangeHandler}
              datePickerClearHandler={datePickerClearHandler}
              datePickerPopover={datePickerPopover}
              isClearDateButtonDisabled={dateField?.length === 0}
              selected={dayjs(defaultFormValues.dateOfRecording).toDate()}
              maxDate={new Date()}
            />
            <Box
              width="100%"
              position="relative"
            >
              <FormControl
                variant="floating"
                isInvalid={!!form.formState.errors.timeOfRecording}
              >
                <SuggestionDropdown
                  options={timeOfRecordingOptions}
                  textValue={timeField}
                  onSelect={onTimeOfRecordingSelect}
                  onChange={onTimeOfRecordingSearchChange}
                  onClear={() => {}}
                  keepOpenAfterBlur={false}
                  resetTextValueAfterBlur={false}
                  debounceDelay={100}
                  isFreeInput
                >
                  {({ searchInputChangeHandler, searchInputBlueHandler, suggestionDropdownPopover }) => (
                    <InputGroup display="block">
                      <Input
                        placeholder=" "
                        {...register('timeOfRecording', {
                          onChange: searchInputChangeHandler,
                          onBlur: searchInputBlueHandler,
                        })}
                        defaultValue={defaultFormValues.timeOfRecording}
                        sx={{
                          '&::-webkit-inner-spin-button, &::-webkit-calendar-picker-indicator': {
                            display: 'none',
                            WebkitAppearance: 'none',
                          },
                        }}
                      />
                      <FormLabel>Time*</FormLabel>
                      <InputRightElement
                        w="36px"
                        h="36px"
                        cursor="pointer"
                        {...{
                          onClick: () => {
                            // Reset the list of filtered options when clicking on the icon button
                            setTimeOfRecordingOptions(HOURLY_BASE_OPTIONS);
                            suggestionDropdownPopover.onOpen();
                          },
                        }}
                      >
                        <ClockIcon
                          size={18}
                          color={theme.colors.papaya[600]}
                        />
                      </InputRightElement>
                    </InputGroup>
                  )}
                </SuggestionDropdown>
              </FormControl>
            </Box>
          </Flex>
        </Box>
      </FormProvider>
    </Modal>
  );
}
