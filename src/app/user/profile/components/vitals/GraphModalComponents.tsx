import { Box } from '@chakra-ui/react';
import { Dot } from 'recharts';

export function RenderDot({
  id,
  cx,
  cy,
  color,
  selectedEntryId,
}: {
  id?: string;
  cx?: number;
  cy?: number;
  color: string;
  selectedEntryId: null | string;
}) {
  return (
    <Dot
      cx={cx}
      cy={cy}
      fill={color}
      r={id === selectedEntryId ? 5 : 3}
    />
  );
}

export function CustomChartTooltipLine() {
  return (
    <Box
      mt="-28px"
      height="238px"
      width="1px"
      bg="fluentHealthText.100"
    />
  );
}
