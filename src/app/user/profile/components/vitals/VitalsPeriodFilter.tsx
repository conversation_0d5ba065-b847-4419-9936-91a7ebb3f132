import dayjs from 'dayjs';

import { BASE_FILTERS } from '@lib/models/misc';
import { PeriodSelector } from 'src/components/PeriodSelector';

export type PeriodSearchQuery = {
  [BASE_FILTERS.FROM_DATE]?: string;
  [BASE_FILTERS.TO_DATE]?: string;
};

export function VitalsPeriodFilter({ setSearchQuery }: { setSearchQuery: (query: PeriodSearchQuery) => void }) {
  const periodChangeHandler = async (fromDate: Date | null = null, toDate: Date | null = null) => {
    const from = fromDate && fromDate?.toString()?.length > 0 ? dayjs(fromDate).format('YYYY-MM-DD') : '';
    const to = toDate && toDate?.toString()?.length > 0 ? dayjs(toDate).format('YYYY-MM-DD') : '';
    const query = {
      ...(from ? { [BASE_FILTERS.FROM_DATE]: from } : {}),
      ...(to ? { [BASE_FILTERS.TO_DATE]: to } : {}),
    };
    setSearchQuery(query);
  };

  return (
    <PeriodSelector
      initialDate={[null, null]}
      onChange={periodChangeHandler}
      strategy="fixed"
      fullWidth
    />
  );
}
