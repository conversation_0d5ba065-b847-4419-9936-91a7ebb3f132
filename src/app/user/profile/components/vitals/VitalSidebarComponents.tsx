import { Flex, Stack, useDisclosure } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { PropsWithChildren, ReactNode } from 'react';
import { Edit3, Trash } from 'react-feather';

import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from 'src/components/ui/Menu';
import { Card, CardCreatedAtLabel, CardHeading } from '../SidebarComponents';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../ConsentModal';

export function VitalItemCard({
  vitals,
  vitalDetails,
  bottomLabel,
  onRemove,
  onEdit,
  isPublicMode = false,
}: PropsWithChildren<{
  vitals: any;
  vitalDetails: ReactNode;
  bottomLabel?: ReactNode;
  onRemove?: () => void;
  onEdit: () => void;
  isPublicMode?: boolean;
}>) {
  const getVitalValue = (linkId: any) => {
    if (vitals && vitals.code?.coding) {
      const vital = vitals.code.coding.find((coding: any) => coding.code === linkId);
      return vital ? vitals.effectiveDateTime : null;
    }
    return null;
  };
  const bodyTemperatureDateTime = getVitalValue('8310-5');
  const pulseRate = getVitalValue('8867-4');
  const oxygenSaturation = getVitalValue('59408-5');
  const respiratoryRate = getVitalValue('9279-1');
  const deleteModal = useDisclosure();
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={onRemove}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card>
        <Stack>
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading
              fontSize="lg"
              fontWeight={500}
            >
              {bodyTemperatureDateTime && <div>{dayjs(bodyTemperatureDateTime).format('DD MMM, YYYY')}</div>}
              {pulseRate && <div>{dayjs(pulseRate).format('DD MMM, YYYY')}</div>}
              {oxygenSaturation && <div>{dayjs(oxygenSaturation).format('DD MMM, YYYY')}</div>}
              {respiratoryRate && <div>{dayjs(respiratoryRate).format('DD MMM, YYYY')}</div>}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<Trash size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          <Flex alignItems="end">{vitalDetails}</Flex>
          <CardCreatedAtLabel>{bottomLabel}</CardCreatedAtLabel>
        </Stack>
      </Card>
    </>
  );
}
