import React, { Suspense, useEffect, useState } from 'react';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { ObservationVitalResponsePayload } from '@user/lib/models/questionnaire-response';
import { LOINC_ORG, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES, VITALS } from '@user/lib/constants';
import { useMasterObservationResponseList } from '@user/lib/medplum-state';
import dayjs from 'dayjs';
import { recordVitalsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';
import { useSharePatient } from '@user/lib/state';

import { MasterVital, PatientVitals } from '@lib/models/patient-vitals';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { IModal, MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import { UpsertVitalsForm } from './UpsertVitalsForm';
import { VitalItemCard } from './VitalSidebarComponents';
import { CardSkeleton, GraphModalButtons, SidebarHelperTooltip } from '../SidebarComponents';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { FHIR_CODE_SYSTEM_URL, deleteIdentifier } from '@lib/constants';

function PulseSectionList({
  answerList,
  setSelectedVital,
  upsertModal,
  onTrackEditClickEvent,
}: {
  answerList: any;
  setSelectedVital: React.Dispatch<React.SetStateAction<PatientVitals | null>>;
  upsertModal: any;
  onTrackEditClickEvent: (vitals: PatientVitals) => void;
}) {
  const toast = useToast();
  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();
  const { authenticatedUser } = useAuthService();
  const { deleteObservationResponseTask } = useMasterObservationResponseList(authenticatedUser?.id);
  const onRemoveVital = async (vitals: any) => {
    const deleteTask = 'Delete Vitals';
    const identifier = `${deleteIdentifier}:vitals`;
    const payload: ObservationVitalResponsePayload = {
      observationId: vitals?.id,
      deleteTask,
      identifier,
    };
    try {
      await deleteObservationResponseTask(payload);

      toast({
        title: 'Pulse rate removed',
        description: 'Pulse rate has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: 'An error occurred while removing the allergy/intolerance',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: 'VTPRRemoved',
      });
    }
  };

  return (
    <>
      {answerList.length === 0 && (
        <Flex
          direction="column"
          gap="12px"
          height="full"
          overflowY="hidden"
        >
          <SidebarEmptyState
            title="No Pulse recordings found."
            imageSrc="/empty-card-vital-pulse.png"
            hideActionButton
          />
        </Flex>
      )}
      <Stack
        overflowY="scroll"
        className="hide-scrollbar"
        gap="2"
      >
        {answerList.length > 0 &&
          answerList.map((answer: any) => (
            <VitalItemCard
              key={answer.id}
              vitals={answer}
              vitalDetails={
                <Flex alignItems="baseline">
                  <Text
                    fontSize="32px"
                    color="gray.500"
                  >
                    {answer?.valueQuantity?.value}
                  </Text>
                  <Text
                    fontSize="sm"
                    color="fluentHealthText.300"
                    ml="2"
                  >
                    {answer?.valueQuantity?.unit ? `${answer.valueQuantity.unit}` : VITALS.PulseRate.unit}
                  </Text>
                </Flex>
              }
              bottomLabel={
                answer?.effectiveDateTime &&
                `Recorded at ${dayjs(answer.effectiveDateTime)
                  .utcOffset(-5 * 60)
                  .utc()
                  .format('HH:mm')}`
              }
              onRemove={() => onRemoveVital(answer)}
              onEdit={() => {
                setSelectedVital(answer);
                upsertModal.onOpen();
                onTrackEditClickEvent(answer);
              }}
              isPublicMode={isPublicMode}
            />
          ))}
      </Stack>
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function PulseSection({ masterVital, action }: { masterVital: MasterVital; action: string }) {
  const { authenticatedUser: authenticatedPatient } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const patientId = !isPublicMode ? authenticatedPatient.id : useSharePatient()?.patient.id;
  const { PROFILE, EHR, VITALS: VITALS_ROUTE, PULSE_RATE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { masterVitalList, addObservationResponse, updateObservationResponse } =
    useMasterObservationResponseList(patientId);
  const answerList = masterVitalList[VITALS.PulseRate.key];
  const { trackEventInFlow } = useAnalyticsService();
  const [modalState, setModalState] = useState<IModal>({});
  const [selectedVital, setSelectedVital] = useState<PatientVitals | null>(null);

  const toast = useToast();
  const navigate = useNavigate();
  const upsertModal = useDisclosure();

  const onAddHandler = () => {
    navigate(`/${PROFILE}/${EHR}/${VITALS_ROUTE}/${PULSE_RATE}/${ADD}`);

    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTPRAddStarted',
      vt_pr_entry_point: 'my_health_profile',
    });
  };

  const onTrackEditClickEvent = () => {
    recordVitalsEvents(trackEventInFlow, {
      EventName: 'VTPRInteracted',
      vt_pr_entry_point: 'my_health_profile',
    });
  };

  const closeFn = () => {
    upsertModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'vitals', PULSE_RATE));
  };
  async function onSubmit(vitalsToUpsert: any) {
    const { id: selectedPulseRateId, dateOfRecording, timeOfRecording } = vitalsToUpsert || {};
    const pulse = vitalsToUpsert[VITALS.PulseRate.key];
    try {
      const effectiveDateTime =
        dateOfRecording && timeOfRecording
          ? dayjs(`${dateOfRecording}T${timeOfRecording}`).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
          : undefined;

      const payloadData: ObservationVitalResponsePayload = {
        ...(selectedPulseRateId && { id: selectedPulseRateId }),
        item: VITALS.PulseRate.key,
        code: {
          coding: [
            {
              system: FHIR_CODE_SYSTEM_URL,
              code: VITALS.PulseRate.key,
              display: VITALS.PulseRate.label,
            },
            {
              system: LOINC_ORG,
              code: VITALS.PulseRate.code,
              display: VITALS.PulseRate.label,
            },
          ],
        },
        recordingDateTime: effectiveDateTime,
        valueQuantity: {
          value: parseFloat(pulse),
          unit: VITALS.PulseRate.unit,
          system: 'http://unitsofmeasure.org',
          code: 'BPM',
        },
      };

      if (selectedPulseRateId) {
        await updateObservationResponse(payloadData);
      } else {
        await addObservationResponse(payloadData);
      }

      toast({
        title: `Successfully ${selectedPulseRateId ? 'updated' : 'added'} ${VITALS.PulseRate.label}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      closeFn();
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      recordVitalsEvents(trackEventInFlow, {
        EventName: selectedPulseRateId ? 'VTPREdited' : 'VTPRAddCompleted',
        vt_pr_rate: pulse,
        vt_pr_date: dayjs(dateOfRecording).format('YYYY/MM/DD'),
        vt_pr_time: timeOfRecording,
      });
    }
  }

  // Reset selectedVital when closing the modal
  useEffect(() => {
    if (upsertModal.isOpen) {
      return;
    }
    setSelectedVital(null);
  }, [upsertModal.isOpen]);

  useEffect(() => {
    if (action === ADD) {
      upsertModal.onOpen();
    }
  }, [action]);

  return (
    <>
      <Modal
        title={VITALS.PulseRate.label}
        primaryButtonLabel={selectedVital ? 'Save' : 'Add'}
        showSecondaryButton={false}
        isCentered
        variant={MODAL_VARIANTS.PERIWINKLE}
        {...modalState}
        {...upsertModal}
        scrollY="false"
        maxH="400px"
        onClose={closeFn}
      >
        <UpsertVitalsForm
          mainFieldName={VITALS.PulseRate.key}
          mainFieldLabel="Beats per minute*"
          selectedVital={selectedVital}
          myFormState={selectedVital ? 'Edit' : 'Add'}
          onSubmit={onSubmit}
          setModalState={setModalState}
          minReqValue={20}
          maxReqValue={250}
        />
      </Modal>

      {answerList.length === 0 ? (
        <SidebarEmptyState
          title="Update pulse rate readings"
          actionButtonText="Add"
          imageSrc="/empty-card-vital-pulse.png"
          {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
          isPublicMode={isPublicMode}
        />
      ) : (
        <Stack
          gap="2"
          height="full"
          width="full"
          overflowY="hidden"
          className="hide-scrollbar"
        >
          <GraphModalButtons {...(isPublicMode ? {} : { onAddClick: onAddHandler })} />
          <Suspense fallback={<CardSkeleton />}>
            <PulseSectionList
              answerList={answerList}
              setSelectedVital={setSelectedVital}
              upsertModal={upsertModal}
              onTrackEditClickEvent={onTrackEditClickEvent}
            />
          </Suspense>
          <Spacer />
        </Stack>
      )}
      {!isPublicMode && (
        <SidebarHelperTooltip
          text="What is pulse rate?"
          tooltipText="A pulse rate is the number of times the heart beats in one minute. The Mayo Clinic explains how certain body parts highlight supply routes near the surface, including the neck, wrist, and elbow. A normal resting heart rate for adults ranges from 60-100 beats per minute, varying between people. A lower resting heart rate indicates improved heart function and cardiovascular fitness."
        />
      )}
    </>
  );
}
