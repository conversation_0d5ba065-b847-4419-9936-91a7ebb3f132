import React, { PropsWithChildren } from 'react';
import { Flex, Spacer, Stack } from '@chakra-ui/react';
import { Edit3, Trash as TrashIcon } from 'react-feather';

import { Card, CardCreatedAtLabel, CardHeading } from './SidebarComponents';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../components/ui/Menu';

export function SidebarCard({
  title,
  children,
  date,
  onRemove,
  onEdit,
  isPublicMode = false,
}: PropsWithChildren<{
  title: string;
  date: any;
  onRemove?: () => void;
  onEdit: () => void;
  isPublicMode?: boolean;
}>) {
  return (
    <Card>
      <Stack>
        <Flex
          justifyContent="space-between"
          color="fluentHealthText.100"
        >
          <CardHeading fontWeight="medium">{title}</CardHeading>
          {!isPublicMode && (
            <MoreActionsMenu>
              <MoreActionsMenuButton />
              <MoreActionsMenuList>
                <MoreActionsMenuItem
                  icon={<Edit3 size={16} />}
                  onClick={onEdit}
                >
                  Edit
                </MoreActionsMenuItem>
                <MoreActionsMenuItem
                  icon={<TrashIcon size={16} />}
                  onClick={onRemove}
                >
                  Delete
                </MoreActionsMenuItem>
              </MoreActionsMenuList>
            </MoreActionsMenu>
          )}
        </Flex>
        {children}
        <Spacer />
        <CardCreatedAtLabel>Added at {new Date(date).toLocaleDateString()}</CardCreatedAtLabel>
      </Stack>
    </Card>
  );
}
