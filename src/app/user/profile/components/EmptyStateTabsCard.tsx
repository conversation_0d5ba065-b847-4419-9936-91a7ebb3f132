import { ChakraProps, Flex, Text } from '@chakra-ui/react';

import { ReactComponent as EmptyFHLogo } from '@assets/icons/logo-mark.svg';

export function EmptyStateTabsCard({ title, mt, ...props }: ChakraProps & { title: string }) {
  return (
    <Flex
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap="32px"
      mt={mt || '220px'}
      // ml="100px"
      // mr="100px"
      // maxWidth="240px"
      {...props}
    >
      <EmptyFHLogo />
      <Text
        fontSize="24px"
        letterSpacing="-0.48px"
        lineHeight="32px"
        color="fluentHealthText.100"
        textAlign="center"
      >
        {title}
      </Text>
    </Flex>
  );
}
