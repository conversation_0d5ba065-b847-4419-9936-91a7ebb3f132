// Package modules
import React from 'react';
import {
  Avatar,
  Box,
  // Button,
  // CircularProgress,
  // CircularProgressLabel,
  Flex,
  Heading,
  Spinner,
  Text,
  useClipboard,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { Copy as CopyIcon } from 'react-feather';
// import { useLocation } from 'react-router-dom';
// Local modules
import { usePatient } from '@user/lib/medplum-state';

import { Modal } from '../../../../components/Modal';
import { usePatientProgress } from '../../lib/state';
import UploadProfileImageForm from './UploadProfileImageForm';
import { useAuthService, usePublicSettings } from '@lib/state';
import { parsePatientName, truncateName } from '@lib/utils/utils';
// import { useIsMobile, useIsTablet } from 'src/components/ui/hooks/device.hook';
import { useIsTablet } from 'src/components/ui/hooks/device.hook';

import { ReactComponent as AvatarIcon } from '@assets/icons/ProfileAvatar.svg';
// import { ReactComponent as CircleObject } from '@assets/objects/progress-disclaimer-circle.svg';
import { ReactComponent as PublicAvatarIcon } from '@assets/objects/public-profile.svg';

export function ProgressDisclaimer() {
  // const [dismissDisclaimer, setDismissDisclaimer] = useState(false);
  // const { pathname } = useLocation();
  // const isFamilyHistory = pathname === '/profile/family-history';
  // const theme = useTheme();

  return (
    <Box>
      {/* Will be needed post beta */}
      {/* {!dismissDisclaimer && (
        <Flex
          flexDirection="column"
          maxWidth="100%"
          position="relative"
          bg="fluentHealthSecondary.300"
          borderRadius="15px"
          p="20px 16px"
          overflow="hidden"
          mt="20px"
        >
          <Box
            position="absolute"
            right="0"
            top="0"
          >
            <CircleObject />
          </Box>

          {isFamilyHistory ? (
            <Heading
              width={{ lg: '217px' }}
              maxWidth={{ base: 'calc(100% - 150px)', lg: '217px' }}
              fontSize="md"
              zIndex="1"
              lineHeight="1.25"
            >
              Want to earn more points? Add at least one family member to earn
              <span
                style={{
                  color: theme.colors.iris[600],
                  fontSize: '16px',
                  fontWeight: '500',
                  lineHeight: '24px',
                  letterSpacing: '-0.32px',
                  margin: '0px 4px',
                }}
              >
                +5%
              </span>
              towards your profile completion
            </Heading>
          ) : (
            <Heading
              width={{ lg: '217px' }}
              maxWidth={{ base: 'calc(100% - 150px)', lg: '217px' }}
              fontSize="md"
              zIndex="1"
              lineHeight="1.25"
            >
              Hey, did you know, you can earn points with every profile section you complete? Every piece of information
              improves your experience.
            </Heading>
          )}
          <Box>
            <Button
              variant="ghost"
              color="iris.600"
              padding={0}
              fontSize="sm"
              onClick={() => setDismissDisclaimer(true)}
              mt="8px"
            >
              Dismiss
            </Button>
          </Box>
        </Flex>
      )} */}
    </Box>
  );
}

export function AvatarBioCard() {
  const profilePhotoModal = useDisclosure();
  const theme = useTheme();

  const { isPublicMode, myConsent } = usePublicSettings();
  const isLastNameShared: boolean =
    !isPublicMode || (myConsent && myConsent.find((val: any) => val.meaning === 'lastName'));

  const { authenticatedUser } = useAuthService();
  const { patient, updatePatient, deletePatientData } = usePatient(authenticatedUser?.id); //! isPublicMode ?: myPatient;

  const { patientProgress } = usePatientProgress();
  const profileProgress = Object.values(patientProgress).reduce<number>((total, value: any) => total + value, 0);

  const avatarUrl = !isPublicMode && patient?.photo?.length ? patient.photo[0].url : undefined;

  const patientName = parsePatientName(patient?.name);
  const patientFirstName = patientName.split(' ')[0] ?? '';
  const patientLastName = isLastNameShared ? patientName.split(' ')[1] : '';
  const patientFullName = `${patientFirstName} ${patientLastName}`.trim();

  const patientEmail = authenticatedUser ? authenticatedUser?.gatewayUser?.additionalInformation?.mg_route_email : '';
  const clipboard = useClipboard(patientEmail);

  // const isMobile = useIsMobile();
  const isTablet = useIsTablet();

  return (
    <Box>
      <Modal
        title="Upload profile picture"
        showModalFooter={false}
        isCentered
        {...profilePhotoModal}
      >
        <UploadProfileImageForm
          patient={patient}
          updatePatient={updatePatient}
          deletePatientData={deletePatientData}
          onCloseDialog={profilePhotoModal.onClose}
        />
      </Modal>
      <Flex
        alignItems="center"
        direction="column"
        gap={{ base: '8px', lg: '12px' }}
      >
        <Flex
          alignItems="center"
          justifyContent="center"
          flexDirection="column"
          height={!isPublicMode ? '180px' : '160px'}
          gap="8px"
          mt="10px"
        >
          {/* Removed for beta */}
          {/* {!isPublicMode ? (
            <>
              <CircularProgress
                value={profileProgress}
                size="136px"
                thickness="3px"
                color={isMobile ? 'white' : 'fluentHealth.500'}
                trackColor={isMobile ? 'white' : 'fluentHealthSecondary.300'}
                sx={{
                  '& .chakra-progress__track': {
                    strokeWidth: '1px',
                    opacity: isMobile ? 0.3 : 1,
                  },
                  '& circle': {
                    // @ts-ignore
                    r: isMobile ? 46 : 47,
                  },
                }}
              >
                <CircularProgressLabel>
                  <Flex justifyContent="center">
                    <Avatar
                      src={avatarUrl}
                      icon={<AvatarIcon style={{ color: '#DADCFF' }} />}
                      rounded="full"
                      size="2xl"
                      maxW="110px"
                      maxH="110px"
                      loading="lazy"
                      variant="outline"
                      {...(!isPublicMode
                        ? {
                            cursor: 'pointer',
                            onClick: profilePhotoModal.onOpen,
                          }
                        : {})}
                      padding="4px"
                      bgColor="white"
                      boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12)"
                    />
                  </Flex>
                </CircularProgressLabel>
              </CircularProgress>

              <Text
                color={{ base: 'white', lg: 'fluentHealthSecondary.100' }}
                fontSize="sm"
              >
                {profileProgress}% complete
              </Text>
            </>
          ) : (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
            >
              <Avatar
                // src={avatarUrl}
                icon={<PublicAvatarIcon />}
                size="3xl"
                maxW="120px"
                maxH="120px"
                variant="outline"
                padding="4px"
                bgColor="transparent"
              />
            </Box>
          )} */}
          {!isPublicMode ? (
            <Avatar
              src={avatarUrl}
              icon={<AvatarIcon style={{ color: '#DADCFF' }} />}
              rounded="full"
              size="2xl"
              maxW="110px"
              maxH="110px"
              loading="lazy"
              variant="outline"
              {...(!isPublicMode
                ? {
                    cursor: 'pointer',
                    onClick: profilePhotoModal.onOpen,
                  }
                : {})}
              padding="4px"
              bgColor="white"
              boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12)"
            />
          ) : (
            <Avatar
              // src={avatarUrl}
              icon={<PublicAvatarIcon />}
              size="3xl"
              maxW="120px"
              maxH="120px"
              variant="outline"
              padding="4px"
              bgColor="transparent"
            />
          )}
        </Flex>
        <Flex
          direction="column"
          gap={{ base: '4px', lg: '12px' }}
        >
          <Heading
            color={{ base: 'white', lg: 'fluentHealthText.100' }}
            fontSize={{ base: '24px', lg: '28px' }}
            mt="-4px"
            ml="auto"
            mr="auto"
          >
            {truncateName(patientFullName, 20)}
          </Heading>
          {!isPublicMode && (
            <Flex
              gap="4px"
              justifyContent="center"
            >
              <Text
                fontSize="lg"
                color={{ base: 'periwinkle.200', lg: 'iris.500' }}
                lineHeight="1.2"
                opacity="0.7"
              >
                {patientEmail || 'No email'}
              </Text>
              <Box display={patientEmail !== 'No email' ? 'block' : 'none'}>
                {clipboard.hasCopied ? (
                  <Spinner
                    width="18px"
                    height="18px"
                    color="iris.500"
                  />
                ) : (
                  <CopyIcon
                    size={20}
                    color={theme.colors.iris[500]}
                    onClick={clipboard.onCopy}
                    style={{ cursor: 'pointer' }}
                  />
                )}
              </Box>
            </Flex>
          )}
        </Flex>
      </Flex>
      {!isPublicMode && profileProgress !== 100 && !isTablet && <ProgressDisclaimer />}
    </Box>
  );
}
