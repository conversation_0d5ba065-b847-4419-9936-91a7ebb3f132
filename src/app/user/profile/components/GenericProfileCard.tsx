/* eslint-disable complexity */
import { AddIcon } from '@chakra-ui/icons';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  HStack,
  Heading,
  Image,
  Spacer,
  Text,
  Tooltip,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { Info, PlusCircle } from 'react-feather';
import { useState } from 'react';

import { usePublicSettings } from '@lib/state';
import { InfoModal } from 'src/components/InfoModal';

/** *
 * Props:
 *  title - title of the card
 *  info (optional) - tooltip information for a card that has children / info within the body otherwise
 *  actionButton (optional) - function to handle the action of the "Add" button
 *  actionButtonText (optional) - text that shows up next to "Add" when the card doesn't have any children
 *  children (optional) - the main body of the card
 */

function GenericProfileCard({
  title = '',
  info = '',
  actionButton,
  actionButtonText,
  actionButtonTextPrefix = 'Add',
  showActionButtonIcon = true,
  actionButtonDisabled = false,
  actionButtonTooltipText,
  children,
  customHeader,
  profileCompletionPercentage,
  isMaxLimitReached = false,
  maxLimitTitleText = '',
  maxLimitInfoText = '',
  titleTextColor,
}: {
  title: string;
  info?: string;
  actionButton?: () => void;
  actionButtonText?: string;
  actionButtonTextPrefix?: string;
  showActionButtonIcon?: boolean;
  actionButtonDisabled?: boolean;
  actionButtonTooltipText?: string;
  children?: JSX.Element;
  customHeader?: JSX.Element;
  profileCompletionPercentage?: number;
  isMaxLimitReached?: boolean;
  maxLimitTitleText?: string;
  maxLimitInfoText?: string;
  titleTextColor?: string;
}) {
  const theme = useTheme();
  const infoModal = useDisclosure();
  const { isPublicMode } = usePublicSettings();

  type ModalType = 'info' | 'maxLimit';
  const [modalType, setModalType] = useState<ModalType>('info');

  const handleModalClick = (modelType: ModalType) => {
    setModalType(modelType);
    infoModal.onOpen();
  };

  if (!children) {
    return (
      <Card
        bgColor="periwinkle.300"
        borderRadius="20px"
        boxShadow="none"
        overflow="hidden"
      >
        <CardBody padding="0 0 0 24px">
          <Flex alignItems="center">
            <Flex
              direction="column"
              gap="24px"
              py="24px"
            >
              <Flex
                gap="16px"
                direction="column"
              >
                <Heading
                  fontSize="2xl"
                  lineHeight="32px"
                  // color="periwinkle.700"
                  color="royalBlue.500"
                >
                  {title}
                </Heading>
                {!isPublicMode && info && (
                  <Text
                    color="periwinkle.700"
                    opacity="0.8"
                  >
                    {info}
                  </Text>
                )}
              </Flex>
              <Flex
                direction="row"
                alignItems="center"
                justifyItems="center"
                verticalAlign="middle"
                gap="8px"
              >
                {!isPublicMode && actionButton && (
                  <Button
                    onClick={actionButton}
                    variant="outline"
                    padding="8px 8px 8px 16px"
                    color="periwinkle.700"
                    borderColor="rgba(73, 90, 228, 0.16)"
                    lineHeight="22px"
                    iconSpacing="6px"
                    {...(showActionButtonIcon
                      ? {
                          rightIcon: (
                            <PlusCircle
                              size={20}
                              opacity={0.5}
                            />
                          ),
                        }
                      : {})}
                  >
                    {actionButtonTextPrefix} {actionButtonText}
                  </Button>
                )}
                {!isPublicMode && profileCompletionPercentage && (
                  <Flex
                    bg="iris.100"
                    borderRadius="5px"
                    py="1"
                    px="2"
                  >
                    <Text color="iris.600">+{profileCompletionPercentage}%</Text>
                  </Flex>
                )}
              </Flex>
            </Flex>
            <Spacer />
            <Image
              src="/empty-card-placeholder-left.png"
              maxW="160px"
              maxH="236px"
              opacity="0.2"
            />
            <Image
              src="/empty-card-placeholder-right.png"
              maxW="160px"
              maxH="236px"
              opacity="0.2"
            />
          </Flex>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card
      borderRadius="20px"
      boxShadow="none"
      bg="transparent"
      gap={{ base: '10px', md: '10px' }}
    >
      <CardHeader padding={{ base: '0', md: '16px 16px 0 16px' }}>
        <InfoModal
          title={modalType === 'maxLimit' ? maxLimitTitleText : title}
          isCentered
          description={modalType === 'maxLimit' ? maxLimitInfoText : info}
          onSecondaryButtonClick={infoModal.onClose}
          {...infoModal}
        />
        <Flex
          direction={{ base: 'column-reverse', md: 'row' }}
          alignItems={{ base: 'end', md: 'center' }}
          gap={{ base: '5px', md: '0' }}
          justifyContent={{ base: 'space-between', md: 'flex-start' }}
        >
          {customHeader ?? (
            <HStack>
              {title !== 'GENERAL' ? (
                <Text
                  // textTransform="uppercase"
                  fontSize="16"
                  // fontWeight="medium"
                  // letterSpacing="widest"
                  lineHeight="1"
                  color={titleTextColor ? 'titleTextColor' : 'royalBlue.500'}
                >
                  {title}
                </Text>
              ) : (
                <Text
                  textTransform="uppercase"
                  fontSize="13"
                  fontWeight="medium"
                  letterSpacing="1.56px"
                  lineHeight="16px"
                >
                  {title}
                </Text>
              )}
              {!isPublicMode && info && (
                <Info
                  size={16}
                  color={theme.colors.gray[200]}
                  cursor="pointer"
                  onClick={() => handleModalClick('info')}
                />
              )}
            </HStack>
          )}
          <Spacer display={{ base: 'hidden', md: 'block' }} />
          {!isPublicMode && actionButton && (
            <Tooltip
              label={actionButtonTooltipText}
              borderRadius="16px"
              padding="16px 16px 12px 16px"
              fontSize="18px"
              fontFamily="Apercu"
              letterSpacing="-0.36px"
              lineHeight="26px"
              minW="400px"
              hasArrow
            >
              <Button
                onClick={() => (isMaxLimitReached ? handleModalClick('maxLimit') : actionButton())}
                variant="quiet"
                size="sm"
                fontSize="md"
                fontWeight="medium"
                color="fluentHealth.500"
                minWidth="auto"
                isDisabled={actionButtonDisabled}
                {...(showActionButtonIcon
                  ? {
                      rightIcon: (
                        <AddIcon
                          border="1.5px solid"
                          borderColor="fluentHealthSecondary.200"
                          borderRadius="50%"
                          p="0.5"
                          color="fluentHealthSecondary.200"
                        />
                      ),
                    }
                  : {})}
              >
                {actionButtonTextPrefix} {actionButtonText}
              </Button>
            </Tooltip>
          )}
        </Flex>
      </CardHeader>
      <CardBody
        padding={{ base: '8px', md: '16px' }}
        pt="0"
      >
        {children}
      </CardBody>
    </Card>
  );
}

export default GenericProfileCard;
