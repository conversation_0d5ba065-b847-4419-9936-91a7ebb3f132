/* eslint-disable @typescript-eslint/naming-convention */
import React, { Suspense, lazy, useEffect, useState } from 'react';
import { ChevronRightIcon } from '@chakra-ui/icons';
import {
  But<PERSON>,
  Card,
  Drawer,
  DrawerContent,
  DrawerOverlay,
  Flex,
  Skeleton,
  Text,
  VStack,
  useDisclosure,
} from '@chakra-ui/react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { recordProfileEvents, recordReproductiveHealthEvents } from '@user/lib/events-analytics-manager';

import { ISidebarProps } from '@lib/models/misc';
import { FluentHealthLoader } from '../../../../components/FluentHealthLoader';
import {
  MY_HEALTH_PROFILE_MAP,
  NavigationHelper,
  ROUTE_ACTIONS,
  ROUTE_VARIABLES,
  VIEW_EDIT_HISTORY,
} from '../../lib/constants';
import { useAnalyticsService, usePublicSettings } from '@lib/state';
import { ProfileSidebarLifestyleAndNutrition } from './lifestyle-and-nutrition/ProfileSidebarLifestyleAndNutrition';
import { ProfileSidebarReproductiveHealth } from './reproductive-health/ProfileSidebarReproductiveHealth';

// Lazy-loaded components
const ProfileSidebarConditions = lazy(() => import('./ProfileSidebarConditions'));
const ProfileSidebarSymptoms = lazy(() => import('./ProfileSidebarSymptoms'));
const ProfileSidebarMedication = lazy(() => import('./medication-and-supplements/ProfileSidebarMedication'));
const ProfileSidebarSupplements = lazy(() => import('./medication-and-supplements/ProfileSidebarSupplements'));
const ProfileSidebarSurgeries = lazy(() => import('./ProfileSidebarProcedures'));
const ProfileSidebarAllergiesIntolerances = lazy(() => import('./ProfileSidebarAllergiesIntolerances'));
const ProfileSidebarPreventativeScreenings = lazy(() => import('./ProfileSidebarPreventativeScreenings'));
const ProfileSidebarImmunizations = lazy(() => import('./immunization/ProfileSidebarImmunizations'));
const ProfileSidebarVitals = lazy(() => import('./vitals/ProfileSidebarVitals'));
const ProfileSideBarViewEditHistory = lazy(() => import('./ProfileSideBarViewEditHistory'));

// Action resolver map
const ACTION_BUTTON_RESOLVER_LIST = {
  [MY_HEALTH_PROFILE_MAP.conditions.name]: (props: ISidebarProps) => <ProfileSidebarConditions {...props} />,
  [MY_HEALTH_PROFILE_MAP.symptoms.name]: (props: ISidebarProps) => <ProfileSidebarSymptoms {...props} />,
  [MY_HEALTH_PROFILE_MAP.medication.name]: (props: ISidebarProps) => <ProfileSidebarMedication {...props} />,
  [MY_HEALTH_PROFILE_MAP.supplement.name]: (props: ISidebarProps) => <ProfileSidebarSupplements {...props} />,
  [MY_HEALTH_PROFILE_MAP.allergies_intolerance.name]: (props: ISidebarProps) => (
    <ProfileSidebarAllergiesIntolerances {...props} />
  ),
  [MY_HEALTH_PROFILE_MAP.surgeries_procedure.name]: (props: ISidebarProps) => <ProfileSidebarSurgeries {...props} />,
  [MY_HEALTH_PROFILE_MAP.lifestyle_nutrition.name]: ProfileSidebarLifestyleAndNutrition,
  [MY_HEALTH_PROFILE_MAP.reproductive_health.name]: ProfileSidebarReproductiveHealth,
  [MY_HEALTH_PROFILE_MAP.preventative_screenings.name]: (props: ISidebarProps) => (
    <ProfileSidebarPreventativeScreenings {...props} />
  ),
  [MY_HEALTH_PROFILE_MAP.vaccines.name]: (props: ISidebarProps) => <ProfileSidebarImmunizations {...props} />,
  [MY_HEALTH_PROFILE_MAP.vitals.name]: (props: ISidebarProps) => <ProfileSidebarVitals {...props} />,
  [VIEW_EDIT_HISTORY]: (props: ISidebarProps) => <ProfileSideBarViewEditHistory {...props} />,
};

export function DrawerSection({
  route,
  name,
  active,
  action,
  subEhrId,
}: {
  route?: string;
  name: string;
  active: string;
  action: string;
  subEhrId: string;
}) {
  const drawer = useDisclosure();
  const { trackEventInFlow } = useAnalyticsService();
  const { pathname } = useLocation();
  const { PROFILE, EHR, ALLERGIES, REPRODUCTIVE_HEALTH } = ROUTE_VARIABLES;
  const { ADD, VIEW } = ROUTE_ACTIONS;
  const [clickOutside, setClickOutside] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if ((route && pathname.includes(route)) || (pathname.includes(ALLERGIES) && route?.includes(ALLERGIES))) {
      drawer.onOpen();
    } else {
      drawer.onClose(); // Optional: Close the drawer if it doesn't match
    }
  }, [active]);

  const drawerCloseHandler = () => {
    drawer.onClose();
    setClickOutside(true);
    if (route) {
      navigate(NavigationHelper.getBasicsView(false, { midPath: true }));
    }
    setTimeout(() => {
      setClickOutside(false);
    });
  };

  return (
    <>
      <Flex
        align="center"
        py="11px"
        px="8px"
        cursor="pointer"
        borderRadius="lg"
        justifyContent="space-between"
        _hover={{
          bgColor: 'periwinkle.200',
        }}
        onClick={() => {
          recordProfileEvents(trackEventInFlow, {
            EventName: 'MyHealthProfileInteracted',
            pi_my_health_profile: name,
          });
          drawer.onOpen();
          if (route?.includes(REPRODUCTIVE_HEALTH)) {
            navigate(`/${PROFILE}/${EHR}/${route}/${ADD}`);
            recordReproductiveHealthEvents(trackEventInFlow, {
              EventName: 'ReproductiveHealthAddStarted',
            });
            return;
          }
          if (route) {
            navigate(`/${PROFILE}/${EHR}/${route}/${VIEW}`);
          }
        }}
      >
        <Text
          fontSize="lg"
          fontWeight="medium"
          color="gray.500"
        >
          {name}
        </Text>
        <ChevronRightIcon
          fontSize="xl"
          color="papaya.600"
        />
      </Flex>
      <Drawer
        placement="right"
        onClose={drawerCloseHandler}
        isOpen={drawer.isOpen}
        size={{ base: 'full', md: 'sm' }}
      >
        <DrawerOverlay />
        <DrawerContent
          bg="gradient.profileDrawer"
          maxW="440px"
        >
          <Suspense
            fallback={
              <FluentHealthLoader
                position="absolute"
                top={0}
                bottom={0}
                left={0}
                right={0}
                my="auto"
              />
            }
          >
            {ACTION_BUTTON_RESOLVER_LIST[name]({
              onClose: drawerCloseHandler,
              isOpen: drawer.isOpen,
              name,
              action,
              subActive: subEhrId || '',
              clickOutside,
            })}
          </Suspense>
        </DrawerContent>
      </Drawer>
    </>
  );
}

function ProfileSidebarCTAs() {
  const { isPublicMode } = usePublicSettings();
  const drawer = useDisclosure();
  // const location = useLocation();
  const params = useParams();
  const { ehrId, subEhrId = '', action } = params as any;

  return (
    <Suspense
      fallback={
        <Skeleton
          width="full"
          height="620px"
          borderRadius="8px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
      }
    >
      {!isPublicMode && (
        <Button
          variant="outlined"
          border="1px solid"
          color="fluentHealth.500"
          borderColor="fluentHealth.500"
          onClick={() => drawer.onOpen()}
        >
          View edit history
          <Drawer
            placement="right"
            onClose={drawer.onClose}
            isOpen={drawer.isOpen}
            size={{ base: 'full', md: 'sm' }}
          >
            <DrawerOverlay />
            <DrawerContent
              bg="gradient.profileDrawer"
              maxW="440px"
            >
              <Suspense
                fallback={
                  <FluentHealthLoader
                    position="absolute"
                    top={0}
                    bottom={0}
                    left={0}
                    right={0}
                    my="auto"
                  />
                }
              >
                {ACTION_BUTTON_RESOLVER_LIST[VIEW_EDIT_HISTORY]({
                  onClose: drawer.onClose,
                  isOpen: drawer.isOpen,
                  name: 'View Edit History',
                })}
              </Suspense>
            </DrawerContent>
          </Drawer>
        </Button>
      )}
      <Flex
        alignItems="flex-start"
        direction="column"
      >
        <Flex
          gap="4px"
          margin="8px"
          direction="column"
        >
          <Text
            fontSize="13"
            textTransform="uppercase"
            fontWeight="medium"
            letterSpacing="1.56px"
            lineHeight="16px"
          >
            My Health History
          </Text>
          {!isPublicMode && (
            <Text
              fontSize="16"
              fontWeight="normal"
              letterSpacing="-0.32px"
              lineHeight="24px"
              color="fluentHealthText.200"
            >
              The information you enter is for general reference/storage. When you choose to share your profile, medical
              practitioners may access it to help you better.
            </Text>
          )}
        </Flex>
        <Card
          bgColor="transparent"
          borderRadius="xl"
          boxShadow="0px"
          w="full"
        >
          <VStack
            alignItems="left"
            p="2"
          >
            {Object.entries(MY_HEALTH_PROFILE_MAP).map(([key, value]) => (
              <DrawerSection
                key={key}
                route={value.route}
                name={value.name}
                active={ehrId}
                subEhrId={subEhrId || ''}
                action={action}
              />
            ))}
          </VStack>
        </Card>
      </Flex>
    </Suspense>
  );
}

export default ProfileSidebarCTAs;
