import React, { PropsWithChildren } from 'react';
import { ChakraProps, Tab, TabList, TabPanel, TabPanels, Tabs, TabsProps } from '@chakra-ui/react';

export function RoundedTabPanels({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <TabPanels
      className="hide-scrollbar"
      {...props}
    >
      {children}
    </TabPanels>
  );
}

export function RoundedTabPanel({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <TabPanel
      p="0"
      {...props}
    >
      {children}
    </TabPanel>
  );
}

export function RoundedTab({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <Tab
      flex="1"
      fontSize="sm"
      _selected={{
        color: 'fluentHealthSecondary.100',
        borderRadius: '3xl',
        bg: 'fluentHealthSecondary.300',
      }}
      {...props}
    >
      {children}
    </Tab>
  );
}

export function RoundedTabList({ children, ...props }: PropsWithChildren & ChakraProps) {
  return (
    <TabList
      display="flex"
      justifyContent="space-between"
      borderColor="fluentHealthSecondary.500"
      borderRadius="3xl"
      lineHeight="5"
      fontWeight="normal"
      color="fluentHealthSecondary.100"
      backgroundColor="fluentHealthSecondary.400"
      pb="2px"
      {...props}
    >
      {children}
    </TabList>
  );
}

export function RoundedTabs({ children, ...props }: PropsWithChildren & TabsProps) {
  return (
    <Tabs
      pt={{ base: '0', md: '5' }}
      overflowY="scroll"
      height="full"
      sx={{
        '::-webkit-scrollbar': {
          display: 'none',
        },
      }}
      {...props}
    >
      {children}
    </Tabs>
  );
}
