import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { Container, Flex, Stack, useDisclosure, useToast } from '@chakra-ui/react';
import { Edit3, Trash as TrashIcon } from 'react-feather';
import { QuestionnaireResponse } from '@gql/graphql';
import { medplumApi } from '@user/lib/medplum-api';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, PATIENT_DEFINED, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import dayjs from 'dayjs';
import { recordSurgeryEvents } from '@user/lib/events-analytics-manager';
import { useSharePatient } from '@user/lib/state';

import {
  Card,
  CardHeading,
  CardPerformedLabel,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHeading,
  SidebarHelperTooltip,
} from './SidebarComponents';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../components/ui/Menu';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { FormSkeleton } from '../../../../components/ui/Form';
import ProfileSurgeryForm from './ProfileSurgeryForm';
import { SidebarEmptyState } from './SidebarEmptyState';
import { MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { FACT_CODE_SYSTEM, deleteIdentifier } from '@lib/constants';
import { ISidebarProps } from '@lib/models/misc';
import { useProcedureList } from '../../lib/medplum-state';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from './ConsentModal';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from './LinkedDocumentsCard';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

function ProcedureCard({
  title,
  performedDate,
  onEdit,
  onRemove,
  isPublicMode = false,
  isLoading,
  isCustomEntry,
  report,
}: PropsWithChildren<{
  title: string;
  performedDate: string;
  onEdit?: () => void;
  onRemove?: () => void;
  isPublicMode: boolean;
  isLoading?: boolean;
  isCustomEntry?: boolean;
  report?: any[];
}>) {
  const deleteModal = useDisclosure();
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={onRemove}
            isLoading={isLoading}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card>
        <Stack>
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading>
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {!performedDate.includes('Invalid Date') && <CardPerformedLabel>{performedDate}</CardPerformedLabel>}
          {(report?.length ?? 0) > 0 && (
            <Flex
              direction="column"
              gap="2px"
            >
              <LinkedDocumentsLabel />
              <LinkedDocumentsCard records={useExtractDocumentResource(report)} />
            </Flex>
          )}
        </Stack>
      </Card>
    </>
  );
}

export default function ProfileSidebarSurgeries({ onClose, action }: ISidebarProps) {
  const toast = useToast();
  const navigate = useNavigate();
  const [selectedSurgery, setSelectedSurgery] = React.useState<QuestionnaireResponse | null>(null);
  const surgeryModal = useDisclosure();
  const { PROFILE, EHR, PROCEDURES } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const { authenticatedUser } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const patientId = !isPublicMode ? authenticatedUser?.id : useSharePatient()?.patient?.id;

  const { procedureList: procedureListOfData, deleteSurgeryProcedureTask } = useProcedureList(patientId);

  const [procedureOptions, setProcedureOptions]: any = useState<any[]>([]);
  const [procedureStatus, setProcedureStatus]: any = useState<any[]>([]);
  const [showloader, setShowLoader] = useState(false);
  const { trackEventInFlow } = useAnalyticsService();
  useEffect(() => {
    if (isPublicMode) return;
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.PROCEDURES),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.PROCEDURES_STATUS),
    ]).then(([data, status]) => {
      setProcedureOptions(data.map((e: any) => ({ label: e.display, value: e.code })));
      setProcedureStatus(status);
    });
  }, []);

  const onAddHandler = () => {
    setSelectedSurgery(null);
    recordSurgeryEvents(trackEventInFlow, {
      EventName: 'SurgeriesAddStarted',
      sur_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${PROCEDURES}/${ADD}`);
  };

  const onEditHandler = (surgery: QuestionnaireResponse) => {
    setSelectedSurgery(surgery);
    surgeryModal.onOpen();
    recordSurgeryEvents(trackEventInFlow, {
      EventName: 'SurgeriesInteracted',
      sur_entry_point: 'my_health_profile',
    });
  };

  const onRemoveHandler = async (surgery: any) => {
    setShowLoader(true);
    const deleteTask = 'Delete Procedure';
    const identifier = `${deleteIdentifier}:procedure`;
    const payload: any = {
      surgeryProcedureId: surgery!.id,
      deleteTask,
      identifier,
    };
    try {
      setSelectedSurgery(surgery);
      await deleteSurgeryProcedureTask(payload);
      recordSurgeryEvents(trackEventInFlow, {
        EventName: 'SurgeriesRemoved',
        sur_entry_point: 'my_health_profile',
        sur_type: surgery?.code?.coding?.[0]?.code,
      });
      toast({
        title: 'Surgery / Procedure has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: 'An error occurred while removing the surgery and procedure',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setShowLoader(false);
    }
  };
  useEffect(() => {
    if (action === ADD) {
      surgeryModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    surgeryModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'procedures'));
  };
  const loader = (incoming: boolean) => {
    setShowLoader(incoming);
  };
  return (
    <>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title="Surgeries and/or Procedures"
        showModalFooter={false}
        isCentered
        {...surgeryModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          {showloader ? (
            <FormSkeleton />
          ) : (
            <ProfileSurgeryForm
              surgery={selectedSurgery}
              closeDialog={closeFn}
              procedureOptions={procedureOptions}
              procedureStatus={procedureStatus}
              loader={loader}
            />
          )}
        </Suspense>
      </Modal>
      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Stack
          py="4"
          minWidth="288px"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Surgeries and/or Procedures</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {procedureListOfData?.length === 0 ? (
            <SidebarEmptyState
              title="Update surgery or procedure details"
              imageSrc="/empty-card-procedure.png"
              onClick={onAddHandler}
              actionButtonText="Add"
              completeInfoText={isPublicMode ? undefined : '+8% to complete your profile'}
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
              isPublicMode={isPublicMode}
            />
          ) : (
            <>
              {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
              <Stack
                spacing={4}
                overflowY="scroll"
                height="full"
                className="hide-scrollbar"
              >
                {procedureListOfData?.map((answer: any) => (
                  <ProcedureCard
                    key={answer.id}
                    title={answer?.code?.coding[0]?.display}
                    performedDate={`${dayjs(answer?.performedPeriod?.start)?.format('DD-MM-YYYY')}`}
                    onEdit={() => onEditHandler(answer)}
                    onRemove={() => onRemoveHandler(answer)}
                    isPublicMode={isPublicMode}
                    isLoading={showloader}
                    isCustomEntry={answer.code?.coding?.[0]?.code === `pro:${PATIENT_DEFINED}`}
                    report={answer?.report}
                  />
                ))}
              </Stack>
            </>
          )}
          {!isPublicMode && (
            <SidebarHelperTooltip
              text="What is a surgery and/or a procedure?"
              tooltipText="Surgery refers to manual and/or surgical procedures that can be elective or emergency, performed by a medical practitioner in a hospital or day care centre. According to John Hopkins Medicine, a surgery can treat an illness or injury, rectify deformities and defects, diagnose and cure diseases, relieve pain, remove obstructions, reposition structures to their normal position, redirect blood vessels, transplant tissue or whole organs, implant mechanical or electronic devices, or prolong life."
            />
          )}
        </Stack>
      </Container>
    </>
  );
}
