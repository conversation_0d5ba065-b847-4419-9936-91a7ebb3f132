import React, { useState } from 'react';
import { Button, Flex, HStack, Image, useTheme, useToast } from '@chakra-ui/react';
import { DropzoneState, FileRejection } from 'react-dropzone';
import { medplumApi } from '@user/lib/medplum-api';
import { recordProfilePictureEvents } from '@user/lib/events-analytics-manager';

import { Patient } from '@lib/models/patient';
import { DragAndDropArea, DragAndDropErrors, useDragAndDropArea } from 'src/components/ui/DragAndDrop';
// import { useAuthService } from '@lib/state';
// import { usePatient } from '../../lib/medplum-state';
// import { fileToBase64 } from '@lib/utils/utils';
// import { AnalyticsEventName, AnalyticsFlow, EventPropsNames } from '@lib/analyticsService';
import { useAnalyticsService } from '@lib/state';
import { removeNullValues } from '@lib/utils/utils';

import { ReactComponent as AvatarIcon } from '@assets/icons/Avatar.svg';

const ACCEPTED_FILE_TYPES = {
  'image/png': ['.png'],
  'image/jpeg': ['.jpg', '.jpeg'],
};

const MAX_FILES = 1;
const MAX_FILE_SIZE = 2_000_000; // 25MB

export default function UploadProfileImageForm({
  patient,
  updatePatient,
  deletePatientData,
  onCloseDialog,
}: {
  patient: Patient;
  updatePatient: (payload: any) => void;
  deletePatientData: (payload: any) => void;
  onCloseDialog: (updateMade?: boolean) => void;
}) {
  const theme = useTheme();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRemoved, setIsRemoved] = useState<boolean>(false);
  const [uploadType, setUploadType] = useState<string>('add');
  const [fileRejections, setFileRejections] = useState<DropzoneState['fileRejections']>([]);
  const avatarUrl = (patient?.photo?.length && patient.photo[0].url) || '';
  const { files, setUploadedFiles, removeFileHandler } = useDragAndDropArea({ maxFiles: MAX_FILES });
  const [photoUrl, setPhotoUrl] = useState<string>(avatarUrl);
  const [binaryId, setBinaryId] = useState<string>(avatarUrl);
  const { trackEventInFlow } = useAnalyticsService();

  const handleFilesDrop = async (droppedFiles: File[]) => {
    setUploadedFiles(droppedFiles);

    const fileData: { data: { url: string; id: string } } =
      droppedFiles?.length > 0 && (await medplumApi.uploadFileHandler(droppedFiles[0]));
    if (fileData && fileData?.data) {
      setPhotoUrl(fileData.data.url);
      setBinaryId(`Binary/${fileData.data.id}`);
    }
    setFileRejections([]);
  };

  const removeFiles = (removedFile: File) => {
    removeFileHandler(removedFile);
    setPhotoUrl('');
    const payload = [
      {
        op: 'replace',
        path: '/photo',
        value: [],
      },
    ];
    deletePatientData(payload);
    setIsRemoved(true);
    setFileRejections([]);

    recordProfilePictureEvents(trackEventInFlow, {
      EventName: 'ProfilePictureRemoved',
    });
  };

  const removeResources = (obj: unknown): any => {
    if (Array.isArray(obj)) {
      // If it's an array, recursively process each element
      return obj.map(removeResources);
    }
    if (typeof obj === 'object' && obj !== null) {
      // If it's an object, recursively process its keys
      return Object.entries(obj).reduce((acc: any, [key, value]) => {
        if (key !== 'resource') {
          // Skip the 'resource' key
          acc[key] = removeResources(value); // Recursively process the value
        }
        return acc;
      }, {});
    }
    return obj; // Return non-object values as is
  };

  async function onSubmit() {
    try {
      setIsLoading(true);
      const cleanLink = removeNullValues(patient?.link);
      const cleanTelecom = removeNullValues(patient?.telecom ?? {});
      const link = removeResources(cleanLink);
      const communication = removeNullValues(patient?.communication || []);
      const gender = patient?.gender;
      if (photoUrl) {
        const payload = {
          resourceType: 'Patient',
          name: [
            {
              given: patient?.name[0]?.given,
              family: patient?.name[0]?.family,
            },
          ],
          telecom: cleanTelecom,
          id: patient?.id,
          birthDate: patient?.birthDate,
          ...(communication && { communication }),
          // communication: patient?.communication,
          extension: removeNullValues(patient?.extension),
          ...(gender && { gender }),
          active: true,
          ...(link && { link }),
          photo: [
            {
              url: binaryId,
            },
          ],
        };
        updatePatient(payload);
        setIsRemoved(false);
      }
      // trackEventInFlow(AnalyticsFlow.ProfilePictureManaged, AnalyticsEventName.ProfilePictureManaged, {
      //   [EventPropsNames.Action]: uploadType === 'add' ? 'add' : 'edit',
      //   [EventPropsNames.CompletedSuccess]: true,
      // });

      recordProfilePictureEvents(trackEventInFlow, {
        pp_action: uploadType === 'add' ? 'add' : 'edit',
        EventName: 'ProfilePictureUploadCompleted',
      });

      toast({
        title: `Successfully ${uploadType === 'add' ? 'added' : 'edited'} the profile photo`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      onCloseDialog(true);
      setIsLoading(false);
    } catch (err) {
      recordProfilePictureEvents(trackEventInFlow, {
        pp_action: uploadType === 'add' ? 'add' : 'edit',
        pp_upload_error: true,
        EventName: 'ProfilePictureUploadError',
      });
      toast({
        title: 'Something went wrong!',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
      setIsLoading(false);
    }
  }

  return (
    <Flex
      direction="column"
      height="100%"
    >
      <Flex
        justifyContent="center"
        my="4"
      >
        <Image
          src={photoUrl}
          fallback={<AvatarIcon style={{ color: theme.colors.fluentHealthSecondary[300] }} />}
          rounded="full"
          width="200px"
          height="200px"
          objectFit="cover"
        />
      </Flex>
      {photoUrl ? (
        <DragAndDropArea
          // Styles
          display="flex"
          justifyContent="center"
          gap="6px"
          // D&D area props
          maxFiles={1}
          maxSize={MAX_FILE_SIZE}
          onFilesDrop={(f: File[]) => {
            handleFilesDrop(f);
            recordProfilePictureEvents(trackEventInFlow, {
              pp_action: 'edit',
              EventName: 'ProfilePictureUploadStarted',
            });
            setUploadType(isRemoved ? 'add' : 'replace');
          }}
          onDropRejected={(fileRej: FileRejection[]) => {
            setFileRejections(fileRej);
            if (fileRej.length > 0 && fileRej[0].errors.length > 0) {
              // trackEventInFlow(AnalyticsFlow.ProfilePictureManaged, AnalyticsEventName.ProfilePictureManaged, {
              //   [EventPropsNames.UploadSizeError]: fileRej[0].errors[0].code === 'file-too-large' && true,
              //   [EventPropsNames.CompletedSuccess]: false,
              // });
              recordProfilePictureEvents(trackEventInFlow, {
                pp_action: uploadType === 'add' ? 'add' : 'edit',
                EventName: 'ProfilePictureUploadError',
              });
            }
          }}
          acceptedFileTypes={ACCEPTED_FILE_TYPES}
        >
          {() => (
            <>
              <Button
                variant="outline"
                color="fluentHealthText.100"
                padding="16px 20px"
                borderColor="iris.500"
              >
                Change picture
              </Button>
              <Button
                variant="outline"
                onClick={(event) => {
                  event.stopPropagation();
                  removeFiles(files[0]);
                }}
                color="fluentHealthText.100"
                padding="16px 20px"
                borderColor="iris.500"
              >
                Remove
              </Button>
            </>
          )}
        </DragAndDropArea>
      ) : (
        <DragAndDropArea
          // Styles
          display="flex"
          justifyContent="center"
          maxFiles={1}
          gap="6px"
          maxSize={MAX_FILE_SIZE}
          // D&D area props ;.-ṁ
          onFilesDrop={(f: File[]) => {
            handleFilesDrop(f);
            recordProfilePictureEvents(trackEventInFlow, {
              pp_action: 'add',
              EventName: 'ProfilePictureUploadStarted',
            });
            setUploadType('add');
          }}
          onDropRejected={(fileRej: FileRejection[]) => {
            setFileRejections(fileRej);
            if (fileRej.length > 0 && fileRej[0].errors.length > 0) {
              // trackEventInFlow(AnalyticsFlow.ProfilePictureManaged, AnalyticsEventName.ProfilePictureManaged, {
              //   [EventPropsNames.UploadSizeError]: fileRej[0].errors[0].code === 'file-too-large' && true,
              //   [EventPropsNames.CompletedSuccess]: false,
              // });
              recordProfilePictureEvents(trackEventInFlow, {
                pp_action: uploadType === 'add' ? 'add' : 'edit',
                EventName: 'ProfilePictureUploadError',
              });
            }
          }}
          acceptedFileTypes={ACCEPTED_FILE_TYPES}
        >
          {() => (
            <Button
              variant="outline"
              color="fluentHealthText.100"
              padding="16px 20px"
              borderColor="iris.500"
              width="max-content"
            >
              Upload picture
            </Button>
          )}
        </DragAndDropArea>
      )}
      <Flex justify="center">
        {fileRejections.length > 0 && (
          <DragAndDropErrors
            fileRejections={fileRejections}
            maxSize={MAX_FILE_SIZE}
            mb="8px"
          />
        )}
      </Flex>
      <HStack
        flex="row"
        justifyContent="flex-end"
        paddingTop="8px"
        paddingRight="16px"
        paddingBottom="8px"
        paddingLeft="16px"
      >
        <Button
          width="124px"
          gap="8px"
          type="submit"
          isLoading={isLoading}
          onClick={onSubmit}
          isDisabled={files.length === 0}
          marginTop="52px"
        >
          Save
        </Button>
      </HStack>
    </Flex>
  );
}
