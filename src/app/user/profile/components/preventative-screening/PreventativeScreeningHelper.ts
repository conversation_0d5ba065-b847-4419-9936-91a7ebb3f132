import { LOINC_ORG } from '@user/lib/constants';

import { FHIR_HL7_ACT_CODE_CODESYSTEM } from 'src/constants/medplumConstants';
import { Observation, Procedure } from 'src/gql/graphql';

export type ProcedureInput = {
  identifier: { system: string; value: string };
  status: string;
  code: { system: string; code: string; display: string };
  performedDateTime: string;
  category: { system: string; code: string; display: string };
  subject: string;
  encounter?: string;
  recorder?: string;
  report?: string[];
  note?: string;
};

type ObservationInput = {
  component: { system: string; code: string; display: string }[];
  encounter: string;
  subject: string;
  partOf?: string[];
};

export function createFhirResource(
  type: 'Procedure' | 'Observation',
  inputData: ProcedureInput | ObservationInput
): Procedure | Observation {
  switch (type) {
    case 'Procedure': {
      const data = inputData as ProcedureInput;
      const procedure: Procedure = {
        resourceType: 'Procedure',
        identifier: [data.identifier],
        status: data.status,
        code: { coding: [data.code] },
        performedDateTime: data.performedDateTime,
        category: { coding: [data.category] },
        subject: { reference: data.subject },
        recorder: { reference: data.recorder },
        report: data.report ? data.report.map((ref) => ({ reference: ref })) : undefined,
        ...(data.note && { note: [{ text: data.note }] }),
      };
      return procedure;
    }

    case 'Observation': {
      const data = inputData as ObservationInput;
      const observation: Observation = {
        resourceType: 'Observation',
        status: 'final',
        category: [
          {
            coding: [
              {
                system: FHIR_HL7_ACT_CODE_CODESYSTEM,
                code: 'laboratory',
                display: 'Laboratory',
              },
            ],
          },
        ],
        code: {
          coding: [
            {
              system: LOINC_ORG,
              code: '1234-5',
              display: 'Observation Code',
            },
          ],
        },
        subject: { reference: data.subject },
        encounter: { reference: data.encounter },
        component: data.component.map((comp) => ({
          code: { coding: [comp] },
        })),
        partOf: data.partOf ? data.partOf.map((ref) => ({ reference: ref })) : undefined,
      };
      return observation;
    }

    default:
      throw new Error(`Invalid resource type: ${type}`);
  }
}
