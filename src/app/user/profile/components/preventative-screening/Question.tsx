import {
  Box,
  Button,
  ChakraProps,
  Container,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  HStack,
  Heading,
  Spacer,
  Text,
  VStack,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { PlusCircle as AddIcon, ChevronRight } from 'react-feather';
import { StylesConfig } from 'react-select';

import { YesOrNoAnswer } from '@lib/models/misc';
import { SELECT_STYLES as BASE_SELECT_STYLES, Select, SelectOptionProps } from 'src/components/ui/Select';

const SELECT_OPTIONS = [
  { label: YesOrNoAnswer.Yes, value: YesOrNoAnswer.Yes },
  { label: YesOrNoAnswer.No, value: YesOrNoAnswer.No },
] as SelectOptionProps[];

export function QuestionButton({
  answer,
  selectedAnswer,
  onClick,
}: {
  answer: YesOrNoAnswer | null;
  selectedAnswer: YesOrNoAnswer | null;
  onClick: () => void;
}) {
  const buttonStyle = {
    fontSize: 'lg',
    px: '36',
    fontWeight: 'normal',
    variant: 'ghost',
    border: '1px solid',
    borderColor: 'periwinkle.200',
    py: '4',
    backgroundColor: 'transparent',
    color: 'initial',
    _hover: {
      backgroundColor: 'periwinkle.200',
      color: 'periwinkle.700',
    },
  };
  return (
    <Button
      {...buttonStyle}
      backgroundColor={selectedAnswer === answer ? 'periwinkle.200' : 'transparent'}
      borderColor={selectedAnswer === answer ? 'iris.300' : 'periwinkle.200'}
      color={selectedAnswer === answer ? 'periwinkle.700' : 'initial'}
      onClick={onClick}
    >
      {answer}
    </Button>
  );
}

export function QuestionContent({
  title,
  answer,
  onSubmit,
  onAnswerLater,
}: {
  title: string;
  answer: YesOrNoAnswer | null;
  onSubmit: (data: any) => void;
  onAnswerLater: () => void;
}) {
  const [selectedAnswer, setSelectedAnswer] = useState<YesOrNoAnswer | null>(answer);
  const onButtonSelect = (select: YesOrNoAnswer) => {
    setSelectedAnswer(select);
  };

  return (
    <Container>
      <Flex
        flexDirection="column"
        alignItems="center"
      >
        <Heading
          fontSize="2xl"
          textAlign="center"
          mx="20"
        >
          {title}
        </Heading>
        <VStack mt="10">
          <QuestionButton
            answer={YesOrNoAnswer.Yes}
            selectedAnswer={selectedAnswer}
            onClick={() => onButtonSelect(YesOrNoAnswer.Yes)}
          />
          <Spacer />
          <QuestionButton
            answer={YesOrNoAnswer.No}
            selectedAnswer={selectedAnswer}
            onClick={() => onButtonSelect(YesOrNoAnswer.No)}
          />
        </VStack>
        <Button
          mt="4"
          color="gray.300"
          variant="quiet"
          fontSize="lg"
          fontWeight="normal"
          onClick={() => onAnswerLater()}
        >
          Answer later
        </Button>
        <Button
          mt="12"
          rightIcon={<ChevronRight />}
          onClick={() => onSubmit(selectedAnswer)}
          maxWidth="92px"
          pr="2"
          pl="5"
          isDisabled={selectedAnswer === null}
        >
          Next
        </Button>
      </Flex>
    </Container>
  );
}

// Overwrite font size for single value only.
const SELECT_STYLES: StylesConfig = {
  ...BASE_SELECT_STYLES,
  singleValue: (base, props) => ({
    ...BASE_SELECT_STYLES.singleValue!(base, props),
    fontSize: '16px',
  }),
};

export function QuestionBottomBar({
  labelText,
  questionWatch,
  options,
  onChange,
  ...props
}: ChakraProps & {
  labelText: string;
  questionWatch: any;
  options?: any;
  onAdd?: () => void;
  onChange?: (params: any) => void;
}) {
  const [newCheck, setNewCheck] = useState(!!questionWatch?.value);
  return (
    <Box
      pt="5"
      {...props}
    >
      {newCheck ? (
        <FormControl>
          <FormLabel marginTop="20px">{labelText}</FormLabel>
          <Select
            options={options || SELECT_OPTIONS}
            value={questionWatch}
            onChange={onChange}
            isClearable={false}
            isSearchable={false}
            menuPlacement="auto"
            styles={SELECT_STYLES}
          />
        </FormControl>
      ) : (
        <>
          <HStack justifyContent="space-between">
            <Text
              fontSize="lg"
              lineHeight="1"
              color="iris.500"
            >
              {labelText}
            </Text>
            <Button
              style={{
                margin: '0px',
              }}
              width="max-content"
              variant="quiet"
              fontSize="md"
              color="fluentHealth.500"
              rightIcon={<AddIcon width="16px" />}
              onClick={() => setNewCheck(true)}
            />
          </HStack>
          <Divider />
        </>
      )}
    </Box>
  );
}
