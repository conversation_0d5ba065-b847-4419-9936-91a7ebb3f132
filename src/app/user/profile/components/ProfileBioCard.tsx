// Package modules
import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import {
  Box,
  Button,
  Card,
  Center,
  ChakraProps,
  Flex,
  Grid,
  GridItem,
  Heading,
  IconButton,
  Skeleton,
  Spacer,
  Text,
} from '@chakra-ui/react';
import { Edit3 as PenIcon, PlusCircle } from 'react-feather';
// Local modules
// import { PATIENT_PROGRESS_FIELDS_MAP, PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP } from '@user/lib/constants';
import { usePatient, usePatientSettingsAll } from '@user/lib/medplum-state';
import AddDropdown from '@components/AddDropdown';
import { ObservationNames, ObservationStatus, ObservationType, observationTypes } from '@src/types/observation';
import AddInputField from '@components/AddInputField';
import { ObservationPayload } from '@user/lib/models/patient';
import { ActiveCard, ProfileEventAction } from '@src/types/profile';
import { enumMesurment, enumUnit } from '@user/lib/constants';
import { healthProfileBasicInfoEvents } from '@user/lib/events-analytics-manager';
import { medplumApi } from '@user/lib/medplum-api';
import { SelectOptionProps } from '@components/ui/Select';

import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { evalConditions, removeNulls } from '@lib/utils/utils';
import { getAge } from 'src/utils/utils';
import {
  convertCmToFeetAndInches,
  convertFeetAndInchesToCm,
  convertWeightKgToLbs,
  convertWeightLbsToKg,
  heightOptions,
} from '@lib/utils/observationsUtils';
import {
  FH_STRUCTURE_DEFINITION_PATIENT_ETHNICITY,
  FH_STRUCTURE_DEFINITION_PATIENT_GENDER_IDENTITY,
  // LANGUAGE_URN,
} from 'src/constants/medplumConstants';
import { findFHIRExtension } from '../../settings/SettingsPage';
import { FACT_CODE_SYSTEM } from '@lib/constants';

export function ProfileBioCardSkeleton() {
  const skeletons = Array.from({ length: 6 }, (_, index) => (
    <Skeleton
      key={index}
      width="calc(50% - 1px)"
      height="62px"
      borderRadius="4px"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.500"
    />
  ));

  return (
    <Flex
      wrap="wrap"
      gap="2px"
      bgColor="white"
      borderRadius="xl"
      border="1px solid"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      px="4px"
      py="4px"
    >
      {skeletons}
    </Flex>
  );
}

function AddInfo({
  ctaText = ProfileEventAction.Add,
  // labelText,
  onClick,
}: {
  ctaText?: string;
  // labelText?: number;
  onClick?: Function;
}) {
  return (
    <Flex
      alignItems="center"
      justifyContent="space-between"
      padding="0 4px"
      width="100%"
    >
      <Button
        variant="ghost"
        color="white"
        padding={0}
        lineHeight="1.5"
        iconSpacing="4px"
        rightIcon={
          <PlusCircle
            size={16}
            color="white"
            opacity={0.5}
          />
        }
        onClick={() => onClick?.()}
      >
        {ctaText}
      </Button>
      {/* Removing for Beta */}
      {/* {labelText && (
        <Flex
          backgroundColor="iris.100"
          height="24px"
          padding="2px 4px"
          borderRadius="5px"
        >
          <Text
            color="iris.600"
            fontWeight="500"
            fontSize="16px"
          >
            +{labelText}%
          </Text>
        </Flex>
      )} */}
    </Flex>
  );
}

export function ProfileBioCardItem({
  title,
  label,
  options,
  children,
  defaultValue,
  unit,
  isInputField,
  handleChange,
  handleSelect,
  handleClear,
  showClearButton,
  cardType,
  activeCard,
  setActiveCard,
  ...props
}: PropsWithChildren<
  ChakraProps & {
    title: string;
    label?: string;
    options?: string[];
    defaultValue?: string;
    unit?: string;
    isInputField?: boolean;
    handleChange?: (value: string) => void;
    handleSelect?: (value: string) => void;
    handleClear?: (value: string) => void;
    showClearButton?: boolean;
    cardType?: ActiveCard;
    activeCard?: ActiveCard;
    setActiveCard?: React.Dispatch<React.SetStateAction<ActiveCard>>;
  }
>) {
  const isAdding = cardType && activeCard === cardType;
  const determineContent = () => {
    if (!isAdding) {
      return children;
    }

    const handleSetIsAdding = () => {
      if (setActiveCard) setActiveCard(ActiveCard.None);
    };

    if (isInputField) {
      return (
        <AddInputField
          defaultValue={defaultValue || ''}
          onSelect={handleSelect}
          onClear={handleClear}
          setIsAdding={handleSetIsAdding}
          isAdding={isAdding}
          maxCharacters={3}
          maxDecimalPoints={2}
          onInputChange={handleChange}
        />
      );
    }

    return (
      <AddDropdown
        options={options}
        defaultValue={defaultValue || ''}
        onSelect={handleSelect}
        onClear={handleClear}
        setIsAdding={handleSetIsAdding}
        isAdding={isAdding}
        showClearButton={showClearButton}
      />
    );
  };

  const content = determineContent();

  return (
    <GridItem
      p="3"
      {...props}
      borderColor="periwinkle.400"
    >
      <Box
        role="group"
        position="relative"
      >
        <Flex
          direction="column"
          gap="20px"
        >
          <Flex alignItems="center">
            <Text
              fontSize="sm"
              color="white"
              lineHeight="1"
              opacity="70%"
            >
              {title}
            </Text>
            {label && (
              <>
                <Spacer />
                <Text
                  fontSize="sm"
                  color="white"
                  lineHeight="1"
                >
                  {label}
                </Text>
              </>
            )}
          </Flex>
          {content}
        </Flex>
      </Box>
    </GridItem>
  );
}

function ProfileBioCardItemValue(props: PropsWithChildren<ChakraProps>) {
  return (
    <Heading
      fontSize={{ base: 'md', md: 'xl' }}
      lineHeight="short"
      color="white"
      {...props}
    />
  );
}

export function ProfileBioCardItemNoneValue() {
  return (
    <Center
      width="max-content"
      height="27px"
    >
      <Box
        width="18px"
        height="1px"
        bgColor="white"
      />
    </Center>
  );
}

async function handleAddObservation(
  addPatientObservation: any,
  status: ObservationStatus | undefined,
  value: number | string,
  observation: ObservationType,
  unit?: string,
  previousValue?: string,
  valueCode?: string,
  valueDisplay?: string,
  valueText?: string
): Promise<void> {
  const payload: ObservationPayload = {
    name: observation.name as ObservationNames,
    status: status ?? ObservationStatus.Final,
    system: observation.system,
    value,
    unit,
    code: observation.code,
    codeDisplay: observation.codeDisplay,
    categoryCode: observation.categoryCode,
    categoryDisplay: observation.categoryDisplay,
    unitSystem: observation.unitSystem,
    valueCodeUrl: observation?.valueCodeUrl,
    valueCode,
    valueDisplay,
    valueText,
    ...(previousValue && { previousValue }),
  };

  await addPatientObservation(payload);
}

// eslint-disable-next-line complexity
export function ProfileBioCard() {
  const { authenticatedUser } = useAuthService();
  const pubSettings = usePublicSettings();
  const { isPublicMode, myPatient } = pubSettings;
  const patientId = !isPublicMode ? authenticatedUser?.id : myPatient?.id;
  const {
    patient,
    addPatientObservation,
    updatePatientBloodTypeObservation,
    deletePatientObservation,
    updatePatientDetail,
  } = usePatient(patientId);
  const { data } = usePatientSettingsAll(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();
  const [bloodGroup, setBloodGroup] = useState<SelectOptionProps[]>([]);
  const [ethnicityGrp, setEthnicityGrp] = useState<SelectOptionProps[]>([]);
  const [genderGrp, setGenderGrp] = useState<SelectOptionProps[]>([]);
  const [sexAssigned, setSexAssigned] = useState<SelectOptionProps[]>([]);

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.BLOOD_GROUP),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.ETHNICITY),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.GENDER),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.SEX_ASSIGNED),
    ]).then(([inBlood_type, inEthnicity, Gender, Sex_Assigned]) => {
      setBloodGroup(inBlood_type.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setEthnicityGrp(inEthnicity.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setGenderGrp(Gender.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setSexAssigned(Sex_Assigned.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
    });
  }, []);
  const [heightObj, setHeightObj] = useState({ value: '', subValue: '', unit: '' });
  const [weightObj, setWeightObj] = useState({ value: '', subValue: '', unit: '' });
  const heightInch = useMemo(() => {
    return patient?.height?.[0]?.status === ObservationStatus.Cancelled ? 0 : patient?.height?.[0]?.valueQuantity.value;
  }, [patient?.height]);
  const [height, setHeight] = useState(heightInch || '');
  const weightKg = useMemo(() => {
    return patient?.weight?.[0]?.status === ObservationStatus.Cancelled ? 0 : patient?.weight?.[0]?.valueQuantity.value;
  }, [patient?.weight]);

  const [weight, setWeight] = useState(weightKg || '');
  const [bloodType, setBloodType] = useState(patient?.bloodType?.[0]?.valueCodeableConcept?.text || '');
  const genderIdentity = genderGrp.find((item) => item.value === patient?.genderIdentity?.[0]?.valueCode)?.label || '';
  const patientGenderAtBirth: string = useMemo(() => {
    const found = sexAssigned.find((item) => item.value === patient?.gender)?.label;
    return typeof found === 'string' ? found : found !== undefined ? String(found) : '';
  }, [patient?.gender, sexAssigned]);
  const ethnicity = ethnicityGrp.find((item) => item.value === patient?.ethnicity?.[0]?.valueCode)?.label;

  // const preferredLanguageCode = useMemo(() => {
  //   return patient?.communication
  //     ?.find((comm: any) => comm.preferred)
  //     ?.language?.coding?.filter((coding: any) => coding.system === LANGUAGE_URN)?.[0]?.code;
  // }, [patient]);

  // const preferredLanguage = useMemo(() => {
  //   return preferredLanguageCode ? languageValueSet.find((lang) => lang.value === preferredLanguageCode)?.label : '';
  // }, [preferredLanguageCode, languageValueSet]);

  const handleWeightChange = (value: string) => {
    setWeight(Number(value));
  };

  const handleHeightChange = (value: string) => {
    setHeight(Number(value));
  };

  const [activeCard, setActiveCard] = useState<ActiveCard>(ActiveCard.None);
  const onSelectObservation = async (
    observationName: ObservationNames,
    value: string,
    status: ObservationStatus,
    convertValueFn: ((value: string) => any) | null,
    setIsAdding: React.Dispatch<React.SetStateAction<ActiveCard>>
  ) => {
    const observation = observationTypes.find((o) => o.name === observationName);
    if (!observation) return;

    setIsAdding(ActiveCard.None);
    let convertedValue = value;

    if (convertValueFn) {
      convertedValue = convertValueFn(value);
    }

    if (observationName === ObservationNames.BloodType) {
      const selectedBloodTypeValueset = bloodGroup.find((o) => o.label === value);
      const observationId = patient?.bloodType?.[0]?.id;

      if (bloodType && observationId) {
        const payload = {
          valueCodeableConceptSystem: observation?.valueCodeUrl,
          valueCodeableConceptCode: selectedBloodTypeValueset?.value,
          valueCodeableConceptDisplay: selectedBloodTypeValueset?.label,
          valueCodeableConceptText: selectedBloodTypeValueset?.label,
        };
        await updatePatientBloodTypeObservation({
          observationId,
          payload,
          previousValue: patient?.bloodType?.[0]?.valueCodeableConcept?.text,
        });
      } else {
        await handleAddObservation(
          addPatientObservation,
          status,
          value,
          observation,
          undefined,
          patient?.bloodType?.[0]?.valueCodeableConcept?.text,
          selectedBloodTypeValueset?.value?.toString(),
          selectedBloodTypeValueset?.label?.toString(),
          selectedBloodTypeValueset?.label?.toString()
        );
      }
      setBloodType(value);
    } else {
      await handleAddObservation(
        addPatientObservation,
        status,
        convertedValue,
        observation,
        patient?.[observationName.toLowerCase()]?.[0]?.valueQuantity?.unit || observation?.unit,
        observation.name === 'Height'
          ? convertValueFn && convertValueFn(heightObj.value)
          : parseInt(weightObj.value ?? '0', 10)
      );
    }
  };

  const onBloodTypeClearObservation = async (observationId: string) => {
    await deletePatientObservation({ observationId, previousValue: bloodType });
  };
  const handleHeightSelect = (value: string) => {
    const calculatedValue = heightObj.unit === enumUnit.CM ? convertCmToFeetAndInches(parseFloat(value)) : value;
    if (heightObj.unit !== enumUnit.CM) {
      setHeight(convertFeetAndInchesToCm(calculatedValue));
    }
    onSelectObservation(
      ObservationNames.Height,
      calculatedValue,
      ObservationStatus.Final,
      convertFeetAndInchesToCm,
      setActiveCard
    );
    healthProfileBasicInfoEvents(trackEventInFlow, {
      EventName: height ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
      patientId,
      height: value,
    });
  };

  const handleHeightClear = (value: string) => {
    onSelectObservation(
      ObservationNames.Height,
      value,
      ObservationStatus.Cancelled,
      convertFeetAndInchesToCm,
      setActiveCard
    );
    handleHeightChange('');
  };

  const handleWeightSelect = (value: string) => {
    const calculatedValue = weightObj.unit === enumUnit.POUNDS_TEXT ? convertWeightLbsToKg(value) : value;
    setWeight(Number(calculatedValue));

    onSelectObservation(
      ObservationNames.Weight,
      calculatedValue,
      ObservationStatus.Final,
      (v) => Number(v),
      setActiveCard
    );
    healthProfileBasicInfoEvents(trackEventInFlow, {
      EventName: weight ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
      patientId,
      weight,
    });
  };

  const handleWeightClear = (value: string) => {
    onSelectObservation(ObservationNames.Weight, value, ObservationStatus.Cancelled, (v) => Number(v), setActiveCard);
  };

  const handleBloodTypeSelect = (value: string) => {
    onSelectObservation(ObservationNames.BloodType, value, ObservationStatus.Final, null, setActiveCard);
    healthProfileBasicInfoEvents(trackEventInFlow, {
      EventName: bloodType ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
      patientId,
      bloodgroup: value,
    });
  };

  const handleBloodTypeClear = (value: string) => {
    const observationId = patient?.bloodType?.[0]?.id;
    if (value && observationId) onBloodTypeClearObservation(observationId);
    setActiveCard(ActiveCard.None);
    setBloodType('');
  };

  /**
   * Handles the selection of a gender at birth from the provided options.
   *
   * @param value - The selected value from the gender at birth dropdown.
   */
  const handlePatientGenderAtBirthSelect = (value: string) => {
    const selectedGenderIdentity = sexAssigned.find((item: any) => item.label === value)?.value;
    // TODO: Implement toast or error notification to inform the user about the failure to update patient gender.
    if (!selectedGenderIdentity) return;
    setActiveCard(ActiveCard.None);
    updatePatientDetail({
      payload: { patientGender: String(selectedGenderIdentity) },
      previousValue: patientGenderAtBirth?.toLocaleLowerCase(),
    });

    healthProfileBasicInfoEvents(trackEventInFlow, {
      EventName: patientGenderAtBirth ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
      patientId,
      sab: value,
    });
  };

  const handlePatientExtnSelect = (type: string, value: string): void => {
    let selectedValue = '';
    let selectedUrl = '';
    if (value !== 'clear') {
      if (type === 'ethnicity') {
        selectedValue = String(ethnicityGrp.find((item) => item.label === value)?.value ?? '');
        selectedUrl = FH_STRUCTURE_DEFINITION_PATIENT_ETHNICITY;
      } else {
        selectedValue = String(genderGrp.find((item) => item.label === value)?.value ?? '');
        selectedUrl = FH_STRUCTURE_DEFINITION_PATIENT_GENDER_IDENTITY;
      }
    } else {
      const payload = patient.extension.filter((extn: any) => extn.url !== FH_STRUCTURE_DEFINITION_PATIENT_ETHNICITY);
      const x = removeNulls(payload);
      updatePatientDetail({
        payload: { extension: x },
        previousValue:
          type === 'ethnicity'
            ? typeof ethnicity === 'string'
              ? ethnicity
              : ethnicity !== undefined
              ? String(ethnicity)
              : ''
            : typeof genderIdentity === 'string'
            ? genderIdentity
            : genderIdentity !== undefined
            ? String(genderIdentity)
            : '',
        ...(type === 'ethnicity' && value === 'clear' && { isCleared: true }),
      });
      return;
    }

    setActiveCard(ActiveCard.None);
    const payload = [
      ...(patient.extension?.filter((extn: any) => extn.url !== selectedUrl) || []),
      {
        url: selectedUrl,
        valueCode: selectedValue,
        ...(type === 'ethnicity' && {
          value: ethnicityGrp.find((item) => item.value === selectedValue)?.label,
        }),
        ...(type === 'genderIdentity' && {
          value: genderGrp.find((item) => item.value === selectedValue)?.label,
        }),
      },
    ];
    const x = removeNulls(payload);
    const payloadVal =
      type === 'ethnicity'
        ? typeof ethnicity === 'string'
          ? ethnicity
          : ethnicity !== undefined
          ? String(ethnicity)
          : ''
        : typeof genderIdentity === 'string'
        ? genderIdentity
        : genderIdentity !== undefined
        ? String(genderIdentity)
        : '';
    updatePatientDetail({ payload: { extension: x }, previousValue: payloadVal });
    if (type === 'ethnicity') {
      healthProfileBasicInfoEvents(trackEventInFlow, {
        EventName: ethnicity ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
        patientId,
        ethnicity: value,
      });
    } else {
      healthProfileBasicInfoEvents(trackEventInFlow, {
        EventName: genderIdentity ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
        patientId,
        gender: value,
      });
    }
  };

  /**
   * Handles the selection of a preferred language from the provided options.
   *
   * @param value - The selected value from the preferred language dropdown.
   */
  // const handlePreferredLanguageSelect = (value: string): void => {
  //   // If the current language is the same, then no need to update it
  //   if (value === preferredLanguage) return;
  //   const payload = patient?.communication ? [...patient.communication] : [];
  //   const selectedLanguage = languageValueSet.find((lang: any) => lang?.label === value);
  //   // TODO: Implement toast or error notification to inform the user about the failure to update preferred language.
  //   if (!selectedLanguage) return;
  //   // Find the index of the preferred language in the payload based on LANGUAGE_URN
  //   const preferredIndex = payload.findIndex((item) =>
  //     item.language.coding.some((coding: any) => coding.system === LANGUAGE_URN)
  //   );

  //   if (preferredIndex !== -1) {
  //     // If the preferred language exists, update its code and display
  //     payload[preferredIndex].language.coding = payload[preferredIndex].language.coding.map((coding: any) => {
  //       return {
  //         ...coding,
  //         display: selectedLanguage.label,
  //         code: selectedLanguage.value,
  //       };
  //     });
  //     payload[preferredIndex].preferred = true;
  //   } else {
  //     // If the preferred language doesn't exist, add it
  //     payload.push({
  //       language: {
  //         coding: [
  //           {
  //             system: LANGUAGE_URN,
  //             code: selectedLanguage.value,
  //             display: selectedLanguage.label,
  //           },
  //         ],
  //       },
  //       preferred: true,
  //     });
  //   }
  //   setActiveCard(ActiveCard.None);
  //   updatePatientDetail({ payload: { communication: payload }, previousValue: preferredLanguage });
  //   healthProfileBasicInfoEvents(trackEventInFlow, {
  //     EventName: preferredLanguage ? 'HealthProfileBasicInfoEdited' : 'HealthProfileBasicInfoAdded',
  //     patientId,
  //     language: value,
  //   });
  // };

  /**
   * Handles the clearing of the preferred language.
   *
   * @param value - The value of the language to be cleared.
   */
  // const handlePreferredLanguageClear = (value: string) => {
  //   const selectedLanguage = languageValueSet.find((lang: any) => lang?.label === value);
  //   // TODO: Implement toast or error notification to inform the user about the failure to update gender identity.
  //   if (!selectedLanguage) return;
  //   setActiveCard(ActiveCard.None);
  //   let payload = patient?.communication ? [...patient.communication] : [];
  //   if (!preferredLanguage) return;
  //   payload = payload.map((item) => {
  //     const updatedItem = { ...item };
  //     if (updatedItem.language.coding.some((coding: any) => coding.system === LANGUAGE_URN)) {
  //       updatedItem.preferred = false;
  //     }
  //     return updatedItem;
  //   });
  //   updatePatientDetail({ payload: { communication: payload }, previousValue: preferredLanguage });
  // };

  useEffect(() => {
    const preferredHeightUnit: string =
      findFHIRExtension(enumMesurment.HEIGHT_UNIT, data, '')?.extnVal?.extension?.find(
        (extnValue: any) => extnValue.url === 'preference'
      )?.valueCoding.code === enumUnit.CM
        ? enumUnit.CM
        : enumUnit.INCHES_TEXT;
    const preferredWeightUnit: string =
      findFHIRExtension(enumMesurment.WEIGHT_UNIT, data, '')?.extnVal?.extension?.find(
        (extnValue: any) => extnValue.url === 'preference'
      )?.valueCoding.code === enumUnit.KG
        ? enumUnit.KG
        : enumUnit.POUNDS_TEXT;

    setHeightObj({
      value:
        preferredHeightUnit === enumUnit.CM
          ? `${parseFloat(height).toFixed(2)} ${patient?.height?.[0]?.valueQuantity?.unit}`
          : convertCmToFeetAndInches(height),
      subValue:
        preferredHeightUnit === enumUnit.CM
          ? convertCmToFeetAndInches(height)
          : `${parseFloat(height).toFixed(2)} ${patient?.height?.[0]?.valueQuantity?.unit}`,
      unit: preferredHeightUnit,
    });

    setWeightObj({
      value:
        preferredWeightUnit === enumUnit.KG
          ? `${weight} ${enumUnit.KG}`
          : `${convertWeightKgToLbs(weight)} ${enumUnit.POUNDS_TEXT}`,
      subValue:
        preferredWeightUnit === enumUnit.KG
          ? `${convertWeightKgToLbs(weight)} ${enumUnit.POUNDS_TEXT}`
          : `${weight} ${enumUnit.KG}`,
      unit: preferredWeightUnit,
    });
  }, [patient]);

  return (
    <Card
      bgColor="white"
      borderRadius="xl"
      border="1px solid"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      bg="royalBlue.500"
      h="360px"
    >
      <Grid
        templateColumns="repeat(2, 1fr)"
        templateRows="repeat(4, 88px)"
      >
        <ProfileBioCardItem
          title="Height"
          label={height ? heightObj.subValue : ''}
          borderBottom="1px solid"
          borderRight="1px solid"
          options={heightOptions}
          defaultValue={heightObj.value.split(' ')[0]}
          unit={heightObj.unit}
          handleSelect={handleHeightSelect}
          handleChange={handleHeightChange}
          handleClear={handleHeightClear}
          showClearButton
          cardType={ActiveCard.Height}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
          isInputField={heightObj.unit === enumUnit.CM}
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && height,
              !isPublicMode && height,
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <Heading
                  fontSize={{ base: 'md', md: 'xl' }}
                  lineHeight="short"
                  color="white"
                >
                  {`${height ? heightObj.value : ''}`}
                </Heading>
                <Box>
                  {!isPublicMode && (
                    <IconButton
                      aria-label="Edit Height"
                      size="sm"
                      pos="absolute"
                      display={{ base: 'block', md: 'none' }}
                      _groupHover={{ display: 'block' }}
                      onClick={() => {
                        setActiveCard(ActiveCard.Height);
                        healthProfileBasicInfoEvents(trackEventInFlow, {
                          EventName: 'HealthProfileBasicInfoEdited',
                          patientId,
                          height,
                        });
                      }}
                      icon={<PenIcon size={16} />}
                    />
                  )}
                </Box>
              </Flex>,
            ],
            ['AND', isPublicMode, !height, <ProfileBioCardItemNoneValue />],
            [
              'AND',
              !isPublicMode,
              !height,
              <AddInfo
                // labelText={PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP[PATIENT_PROGRESS_FIELDS_MAP.HEIGHT]}
                onClick={() => {
                  setActiveCard(ActiveCard.Height);
                  healthProfileBasicInfoEvents(trackEventInFlow, {
                    EventName: 'HealthProfileBasicInfoAdded',
                    patientId,
                    height,
                  });
                }}
              />,
            ],
          ])}
        </ProfileBioCardItem>
        <ProfileBioCardItem
          title="Weight"
          label={weight ? weightObj.subValue : ''}
          borderBottom="1px solid"
          borderRight="1px solid"
          defaultValue={weightObj.value.split(' ')[0]}
          isInputField
          unit={weightObj.unit}
          handleChange={handleWeightChange}
          handleSelect={handleWeightSelect}
          handleClear={handleWeightClear}
          showClearButton
          cardType={ActiveCard.Weight}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && weight,
              !isPublicMode && weight,
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <Heading
                  fontSize={{ base: 'md', md: 'xl' }}
                  lineHeight="short"
                  color="white"
                >
                  {weight ? weightObj.value : ''}
                </Heading>
                <Box>
                  {!isPublicMode && (
                    <IconButton
                      aria-label="Edit Weight"
                      size="sm"
                      pos="absolute"
                      display={{ base: 'block', md: 'none' }}
                      _groupHover={{ display: 'block' }}
                      onClick={() => {
                        setActiveCard(ActiveCard.Weight);
                        healthProfileBasicInfoEvents(trackEventInFlow, {
                          EventName: 'HealthProfileBasicInfoEdited',
                          patientId,
                          weight,
                        });
                      }}
                      icon={<PenIcon size={16} />}
                    />
                  )}
                </Box>
              </Flex>,
            ],
            [
              'AND',
              !isPublicMode,
              !weight,
              <AddInfo
                // labelText={PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP[PATIENT_PROGRESS_FIELDS_MAP.WEIGHT]}
                onClick={() => {
                  setActiveCard(ActiveCard.Weight);
                  healthProfileBasicInfoEvents(trackEventInFlow, {
                    EventName: 'HealthProfileBasicInfoAdded',
                    patientId,
                    weight,
                  });
                }}
              />,
            ],
          ])}
        </ProfileBioCardItem>
        <ProfileBioCardItem
          title="Age"
          label={patient?.birthDate ? dayjs(patient.birthDate).format('DD/MM/YYYY') : undefined}
          borderBottom="1px solid"
          borderRight="1px solid"
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && patient?.birthDate,
              !isPublicMode && patient?.birthDate,
              <ProfileBioCardItemValue>{getAge(patient?.birthDate)}</ProfileBioCardItemValue>,
            ],
            ['AND', isPublicMode, !patient?.birthDate, <ProfileBioCardItemNoneValue />],
            ['AND', !isPublicMode, !patient?.birthDate, <AddInfo />],
          ])}
        </ProfileBioCardItem>
        <ProfileBioCardItem
          title="Blood group"
          borderBottom="1px solid"
          borderRight="1px solid"
          options={bloodGroup.map((group) => group.label).filter((label): label is string => typeof label === 'string')}
          defaultValue={bloodType || ''}
          handleSelect={handleBloodTypeSelect}
          handleClear={handleBloodTypeClear}
          showClearButton
          cardType={ActiveCard.BloodType}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && bloodType,
              !isPublicMode && bloodType,
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <ProfileBioCardItemValue textTransform="uppercase">{bloodType}</ProfileBioCardItemValue>
                <Box>
                  {!isPublicMode && (
                    <IconButton
                      aria-label="Edit Blood Group"
                      size="sm"
                      pos="absolute"
                      background="none"
                      display={{ base: 'block', md: 'none' }}
                      _groupHover={{ display: 'block' }}
                      onClick={() => {
                        setActiveCard(ActiveCard.BloodType);
                        healthProfileBasicInfoEvents(trackEventInFlow, {
                          EventName: 'HealthProfileBasicInfoEdited',
                          patientId,
                          bloodgroup: bloodType,
                        });
                      }}
                      icon={<PenIcon size={16} />}
                    />
                  )}
                </Box>
              </Flex>,
            ],
            ['AND', isPublicMode, !bloodType, <ProfileBioCardItemNoneValue />],
            [
              'AND',
              !isPublicMode,
              !bloodType,
              <AddInfo
                // labelText={PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP[PATIENT_PROGRESS_FIELDS_MAP.BLOOD_GROUP]}
                onClick={() => {
                  setActiveCard(ActiveCard.BloodType);
                  healthProfileBasicInfoEvents(trackEventInFlow, {
                    EventName: 'HealthProfileBasicInfoAdded',
                    patientId,
                    bloodgroup: bloodType,
                  });
                }}
              />,
            ],
          ])}
        </ProfileBioCardItem>
        <ProfileBioCardItem
          title="Gender"
          borderRight="1px solid"
          borderBottom="1px solid"
          options={genderGrp.map((group) => group.label).filter((label): label is string => typeof label === 'string')}
          defaultValue={
            typeof genderIdentity === 'string'
              ? genderIdentity
              : genderIdentity !== undefined
              ? String(genderIdentity)
              : ''
          }
          cardType={ActiveCard.Gender}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
          handleSelect={(value) => {
            handlePatientExtnSelect('genderIdentity', value);
          }}
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && genderIdentity,
              !isPublicMode && genderIdentity,
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <ProfileBioCardItemValue
                  isTruncated
                  maxWidth="120px"
                  textTransform="capitalize"
                >
                  {genderIdentity}
                </ProfileBioCardItemValue>
                <Box>
                  {!isPublicMode && (
                    <IconButton
                      aria-label="Edit Gender"
                      size="sm"
                      pos="absolute"
                      display={{ base: 'block', md: 'none' }}
                      _groupHover={{ display: 'block' }}
                      onClick={() => {
                        setActiveCard(ActiveCard.Gender);
                        healthProfileBasicInfoEvents(trackEventInFlow, {
                          EventName: 'HealthProfileBasicInfoEdited',
                          patientId,
                          gender:
                            typeof genderIdentity === 'string'
                              ? genderIdentity
                              : genderIdentity !== undefined
                              ? String(genderIdentity)
                              : undefined,
                        });
                      }}
                      icon={<PenIcon size={16} />}
                    />
                  )}
                </Box>
              </Flex>,
            ],
            ['AND', isPublicMode, !genderIdentity, <ProfileBioCardItemNoneValue />],
            [
              'AND',
              !isPublicMode,
              !genderIdentity,
              <AddInfo
                onClick={() => {
                  setActiveCard(ActiveCard.Gender);
                  healthProfileBasicInfoEvents(trackEventInFlow, {
                    EventName: 'HealthProfileBasicInfoAdded',
                    patientId,
                    gender:
                      typeof genderIdentity === 'string'
                        ? genderIdentity
                        : genderIdentity !== undefined
                        ? String(genderIdentity)
                        : undefined,
                  });
                }}
              />,
            ],
          ])}
        </ProfileBioCardItem>
        <ProfileBioCardItem
          title="Sex assigned at birth"
          borderRight="1px solid"
          borderBottom="1px solid"
          options={sexAssigned.map((item) => item.label).filter((label): label is string => typeof label === 'string')}
          defaultValue={patientGenderAtBirth}
          cardType={ActiveCard.PatientGenderAtBirth}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
          handleSelect={handlePatientGenderAtBirthSelect}
        >
          {evalConditions([
            [
              'OR',
              isPublicMode && patientGenderAtBirth,
              !isPublicMode && patientGenderAtBirth,
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                <ProfileBioCardItemValue
                  isTruncated
                  maxWidth="120px"
                >
                  {patientGenderAtBirth}
                </ProfileBioCardItemValue>
              </Flex>,
            ],
            ['AND', isPublicMode, !patientGenderAtBirth, <ProfileBioCardItemNoneValue />],
            [
              'AND',
              !isPublicMode,
              !patientGenderAtBirth,
              <AddInfo
                onClick={() => {
                  setActiveCard(ActiveCard.PatientGenderAtBirth);
                  healthProfileBasicInfoEvents(trackEventInFlow, {
                    EventName: 'HealthProfileBasicInfoAdded',
                    patientId,
                    sab: patientGenderAtBirth,
                  });
                }}
              />,
            ],
          ])}
        </ProfileBioCardItem>
        <GridItem colSpan={2}>
          <ProfileBioCardItem
            height="88px"
            title="Ethnicity"
            borderRight="1px solid"
            options={ethnicityGrp
              .map((group) => group.label)
              .filter((label): label is string => typeof label === 'string')
              .sort((a, b) => a.localeCompare(b))}
            defaultValue={
              typeof ethnicity === 'string' ? ethnicity : ethnicity !== undefined ? String(ethnicity) : undefined
            }
            cardType={ActiveCard.Ethnicity}
            activeCard={activeCard}
            setActiveCard={setActiveCard}
            handleSelect={(value) => {
              handlePatientExtnSelect('ethnicity', value);
            }}
            handleClear={() => {
              handlePatientExtnSelect('ethnicity', 'clear');
            }}
            showClearButton
          >
            {evalConditions([
              [
                'OR',
                isPublicMode && ethnicity,
                !isPublicMode && ethnicity,
                <Flex
                  direction="row"
                  paddingRight="6"
                  height="14"
                  justifyContent="space-between"
                >
                  <ProfileBioCardItemValue isTruncated>{ethnicity}</ProfileBioCardItemValue>
                  <Box>
                    {!isPublicMode && (
                      <IconButton
                        aria-label="Edit Ethnicity"
                        size="sm"
                        pos="absolute"
                        display={{ base: 'block', md: 'none' }}
                        _groupHover={{ display: 'block' }}
                        onClick={() => {
                          setActiveCard(ActiveCard.Ethnicity);
                          healthProfileBasicInfoEvents(trackEventInFlow, {
                            EventName: 'HealthProfileBasicInfoEdited',
                            patientId,
                            ethnicity:
                              typeof ethnicity === 'string'
                                ? ethnicity
                                : ethnicity !== undefined
                                ? String(ethnicity)
                                : undefined,
                          });
                        }}
                        icon={<PenIcon size={16} />}
                      />
                    )}
                  </Box>
                </Flex>,
              ],
              ['AND', isPublicMode, !ethnicity, <ProfileBioCardItemNoneValue />],
              [
                'AND',
                !isPublicMode,
                !ethnicity,
                <AddInfo
                  // labelText={PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP[PATIENT_PROGRESS_FIELDS_MAP.ETHNICITY]}
                  onClick={() => {
                    setActiveCard(ActiveCard.Ethnicity);
                    healthProfileBasicInfoEvents(trackEventInFlow, {
                      EventName: 'HealthProfileBasicInfoAdded',
                      patientId,
                      ethnicity:
                        typeof ethnicity === 'string'
                          ? ethnicity
                          : ethnicity !== undefined
                          ? String(ethnicity)
                          : undefined,
                    });
                  }}
                />,
              ],
            ])}
          </ProfileBioCardItem>
        </GridItem>
        {/* <GridItem colSpan={2}>
          <ProfileBioCardItem
            height="88px"
            title="Preferred language for consults"
            borderRight="1px solid"
            borderBottom="1px solid"
            options={preferredLanguagesOptions}
            defaultValue={preferredLanguage}
            cardType={ActiveCard.PreferredLanguage}
            activeCard={activeCard}
            setActiveCard={setActiveCard}
            handleSelect={handlePreferredLanguageSelect}
            handleClear={handlePreferredLanguageClear}
            showClearButton
          >
            {evalConditions([
              [
                'OR',
                isPublicMode && preferredLanguage,
                !isPublicMode && preferredLanguage,
                <Flex
                  direction="row"
                  paddingRight="6"
                  height="14"
                  justifyContent="space-between"
                >
                  <ProfileBioCardItemValue isTruncated>{preferredLanguage}</ProfileBioCardItemValue>
                  <Box>
                    {!isPublicMode && (
                      <IconButton
                        aria-label="Edit Patient Preferred Language"
                        size="sm"
                        pos="absolute"
                        display={{ base: 'block', md: 'none' }}
                        _groupHover={{ display: 'block' }}
                        onClick={() => {
                          setActiveCard(ActiveCard.PreferredLanguage);
                          healthProfileBasicInfoEvents(trackEventInFlow, {
                            EventName: 'HealthProfileBasicInfoEdited',
                            patientId,
                            language: preferredLanguage,
                          });
                        }}
                        icon={<PenIcon size={16} />}
                      />
                    )}
                  </Box>
                </Flex>,
              ],
              ['AND', isPublicMode, !preferredLanguage, <ProfileBioCardItemNoneValue />],
              [
                'AND',
                !isPublicMode,
                !preferredLanguage,
                <AddInfo
                  // labelText={PATIENT_PROGRESS_PERCENTAGE_VALUES_MAP[PATIENT_PROGRESS_FIELDS_MAP.PREFERRED_LANGUAGE]}
                  onClick={() => {
                    setActiveCard(ActiveCard.PreferredLanguage);
                    healthProfileBasicInfoEvents(trackEventInFlow, {
                      EventName: 'HealthProfileBasicInfoAdded',
                      patientId,
                      language: preferredLanguage,
                    });
                  }}
                />,
              ],
            ])}
          </ProfileBioCardItem>
        </GridItem> */}
      </Grid>
    </Card>
  );
}
