import { useFormContext } from 'react-hook-form';
import React, { useCallback, useEffect, useState } from 'react';
import { Text, UseDisclosureReturn, useDisclosure } from '@chakra-ui/react';
import { useSearchParams } from 'react-router-dom';

import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { SearchableSelect, SelectOptionProps } from '../../../../components/ui/Select';
import { useExtractDocumentResource, useMedicalRecordListLazyPagination } from '../../../medical-records/lib/state';
import { MedicalRecordLabel } from './LinkedDocumentsCard';
import { useAuthService } from '@lib/state';
import { debounce } from '@lib/utils/utils';
import { AddMedicalRecordFlow } from 'src/app/medical-records/components/add-mr-flow/AddMedicalRecordFlow';
import { getModifiedParams } from 'src/app/medical-records/lib/utils';
import { documentUploadWorkflow } from 'src/app/medical-records/lib/constants';

export type MedicalRecordSelectOption = SelectOptionProps<AttachedMedicalRecord>;

export const formatMedicalRecordsToSelectOptions = (recordList: any) => {
  return recordList?.map((record: any) => {
    return {
      label: <MedicalRecordLabel record={record} />,
      value: record?.id ?? record?.id,
      payload: record,
    };
  });
};
export function NoOptionsMessageComponent() {
  return (
    <>
      <Text
        sx={{
          width: '100%',
          textAlign: 'left',
          color: 'Grays.400',
          cursor: 'pointer',
          fontSize: '11px',
          fontWeight: '500',
          padding: '5px 0',
        }}
      >
        Uh-oh! No records added yet.
      </Text>
      <Text
        sx={{
          width: '100%',
          textAlign: 'left',
          color: 'fluentHealthText.100',
          cursor: 'pointer',
          fontSize: '18px',
        }}
      >
        Go to the Health Record Hub to add your documents and unlock powerful health insights. Your medical patterns are
        waiting to be discovered!
      </Text>
    </>
  );
}
export function MedicalRecordSelect({
  labelText = 'Link health records',
  fieldName = 'external_reports',
  onSelectExtra, // Note: added for handling extra functions like - event integration etc
  familyMemberId,
}: {
  labelText?: string;
  fieldName?: string;
  onSelectExtra?: (value?: any) => void | undefined;
  familyMemberId?: string;
}) {
  const form = useFormContext();
  const field = form.watch(fieldName);
  const stepperModal: UseDisclosureReturn = useDisclosure();
  const [isWaiting, setIsWaiting] = useState(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedSetKeyword = debounce(setKeyword, 500);
  const { authenticatedUser } = useAuthService();
  const [searchParams] = useSearchParams();

  const { medicalRecordList, hasNextPage } = useMedicalRecordListLazyPagination(
    authenticatedUser.id,
    getModifiedParams(searchParams),
    'myDocumentReferenceList',
    documentUploadWorkflow.documentCompleted.completed
  );

  useEffect(() => {
    let timeoutId: any;
    if (keyword) {
      setIsWaiting(false);
    } else {
      timeoutId = setTimeout(() => {
        setIsWaiting(true);
      }, 2000);
    }
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [keyword, medicalRecordList]);
  const filteredMedicalRecordList = medicalRecordList.filter((task) => {
    const relatedRefs = task.focus?.resource?.context?.related ?? [];

    return familyMemberId
      ? relatedRefs.some((r: { reference?: string }) =>
          r.reference?.startsWith(`FamilyMemberHistory/${familyMemberId}`)
        )
      : !relatedRefs.some((r: { reference?: string }) => r.reference?.startsWith('FamilyMemberHistory/'));
  });

  const attachedMedicalRecord = useExtractDocumentResource(filteredMedicalRecordList);

  const selectValue = formatMedicalRecordsToSelectOptions(field);
  const selectOptions = formatMedicalRecordsToSelectOptions(attachedMedicalRecord);

  const onSelect = useCallback((value: SelectOptionProps[] | any) => {
    const singleVal = value
      .filter((record: SelectOptionProps) => record.value !== -1)
      .map((record: SelectOptionProps) => record.payload);
    form.setValue(fieldName, singleVal);
    form.trigger(fieldName);
  }, []);
  const onBlur = useCallback(() => {
    if (onSelectExtra) onSelectExtra(form.getValues(fieldName));
  }, [fieldName, form, onSelectExtra]);
  const noOptionsMessageFn = useCallback(() => <NoOptionsMessageComponent />, []);

  return (
    <>
      <SearchableSelect
        placeholder={labelText}
        value={selectValue}
        options={selectOptions}
        onSearch={debouncedSetKeyword}
        onChange={onSelect}
        // onLoadMore={onLoadMore}
        hasNextPage={hasNextPage}
        onBlur={onBlur}
        isLoading={!isWaiting}
        filterOption={(option: SelectOptionProps, searchText: string) => {
          if (option?.payload?.title) {
            return option?.payload?.title?.includes(searchText);
          }
          return true;
        }}
        maxMenuHeight={222}
        closeMenuOnSelect={false}
        openMenuOnFocus
        controlShouldRenderValue={false}
        isClearable={false}
        isSearchable
        isMulti
        noOptionsMessage={noOptionsMessageFn}
      />
      <Text
        fontSize="sm"
        color="charcoal.60"
        fontWeight="medium"
      >
        Link health records here to unlock personalised health guidance
      </Text>
      {stepperModal.isOpen && (
        <AddMedicalRecordFlow
          stepperModal={stepperModal}
          isSidebarOpen
        />
      )}
    </>
  );
}
