import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

import { SidebarHelperTooltip } from './SidebarComponents';

export function HideFromSharingCard() {
  return (
    <Flex
      flexDirection="row"
      gap="8px"
      alignItems="center"
      justifyContent="center"
      borderRadius="8px"
      width="auto"
      px="16px"
      py="4px"
    >
      <Text
        color="periwinkle.700"
        fontSize="14px"
      >
        Hidden from sharing
      </Text>
      <SidebarHelperTooltip
        text="Hidden from sharing"
        showText={false}
        tooltipText="You have the right to keep certain conditions private. You can do this when adding a condition or whenever you share your profile. Please keep in mind that conditions that are hidden from sharing will mean your medical practitioner/doctor will not see those conditions, which may impact diagnosis when your profile is shared with them."
        containerProps={{
          p: 0,
          m: 0,
          ml: '-6px',
        }}
      />
    </Flex>
  );
}
