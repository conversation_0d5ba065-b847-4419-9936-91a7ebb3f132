import React, { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Link,
  Radio,
  Stack,
  Switch,
  Text,
  useDisclosure,
  useOutsideClick,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import dayjs from 'dayjs';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { NavLink, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useMasterConditionResponseList } from '@user/lib/medplum-state';
import { recordConditionEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF, NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { medplumApi } from '@user/lib/medplum-api';

import { Condition, ConditionShareProfile } from '@lib/models/condition';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { DatePickerField } from 'src/components/ui/Form';
import { IModal, useModalContext } from '../../../../components/Modal';
import { GroupedRadioButtons, GroupedRadioButtonsDivider } from '../../../../components/ui/Form/Radio';
import { useIsMobile } from '../../../../components/ui/hooks/device.hook';
import { SearchableSelect, SelectOptionProps } from '../../../../components/ui/Select';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from './ConsentModal';
import { LinkedDocumentsCard } from './LinkedDocumentsCard';
import { MedicalRecordSelect } from './MedicalRecordSelect';
import { ShareEntryCheckbox, SidebarHelperTooltip } from './SidebarComponents';
import { isDuplicatePresentCondition, isEmptyObject } from '@lib/utils/utils';
import { FHIR_CODE_SYSTEM_URL } from '@lib/constants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import {
  FHIR_HL7_STRUCTURE_DEFINITION_CONDITION,
  FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
  SNOMED_URL,
} from 'src/constants/medplumConstants';
import { TERMINOLOGY_CODE_SYSTEM } from 'src/app/medical-records/lib/constants';

type ConditionFormValues = Condition;
const MAX_ALLOWED_CHARACTERS = 500;
const customOption = { label: 'My condition is not listed', value: 'MY_CONDITION_NOT_LISTED' };

function getInitialFormData(condition: Condition | null): ConditionFormValues {
  const { id = '', onsetPeriod, code, note, evidence = [], extension = [], clinicalStatus } = condition || {};
  const primary = code?.coding?.[0] ?? { code: '', display: '' };
  const ext = extension?.[0]?.valueCodeableConcept?.coding?.find(Boolean) || { code: '', display: '' };
  const isCustom = primary.code === `cn:${PATIENT_DEFINED}`;
  return {
    id,
    condition: {
      value: isCustom ? customOption.value : primary.code,
      label: isCustom ? customOption.label : primary.display,
    },
    notes: note?.[0]?.text || '',
    diagnosis_date: dayjs(onsetPeriod?.start).isValid() ? onsetPeriod?.start : '',
    custom_condition: isCustom ? primary.display : '',
    condition_type: {
      value: ext.code,
      label: ext.display,
    },
    is_shareable: condition?.meta?.tag?.[0]?.code !== ConditionShareProfile.shareProfileFalse,
    clinicalStatus: clinicalStatus?.coding?.[0]?.code !== 'inactive',
    end_date: dayjs(onsetPeriod?.end).isValid() ? onsetPeriod?.end : '',
    external_reports: evidence ? useExtractDocumentResource(evidence) : [],
  };
}

const conditionSchema = (showCustomInput: boolean, answerList: any[], condition: any) =>
  z.object({
    id: z.string().optional(),
    condition: z.object({
      value: z
        .string()
        .min(1, 'This is a required field')
        .refine((value) => {
          const duplicateId = isDuplicatePresentCondition(value, answerList, condition);
          return !duplicateId;
        }, 'Condition already exists'),
      label: z.string().min(1, 'Condition already exists'),
    }),
    custom_condition: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
    notes: z.string().max(MAX_ALLOWED_CHARACTERS).optional(),
    condition_type: z
      .object({
        value: z.string().optional(),
        label: z.string().optional(),
      })
      .optional(),
    diagnosis_date: z.string().optional(),
    is_shareable: z.boolean(),
    external_reports: z.array(z.any()).optional(),
    clinicalStatus: z.boolean().optional(),
    end_date: z.string().optional(),
  });

export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      width="100%"
      align="left"
      color="fluentHealthText.100"
      sx={{ cursor: 'pointer' }}
      onClick={onClick}
    >
      My condition is not listed
    </Text>
  );
}
export function ConditionSelect({
  onAfterSelect,
  conditionOptions,
  setShowCustomInput,
}: {
  onAfterSelect?: (value: SelectOptionProps) => void;
  conditionOptions: SelectOptionProps[];
  setShowCustomInput: (flag: boolean) => void;
}): JSX.Element {
  const form: any = useFormContext();
  const [conditionValue, setConditionValue] = useState<SelectOptionProps | null>(form.watch('condition'));
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleNoOptionsClick = useCallback(() => {
    setConditionValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('condition', customOption);
  }, [setShowCustomInput]);

  const handleChange = useCallback(
    (option: any) => {
      const selectedValue = option?.value || '';
      const selectedLabel = option?.label || '';

      setConditionValue(option || null);
      setShowCustomInput(false);

      if (option) {
        form.setValue('condition', { value: selectedValue, label: selectedLabel });
        form.trigger('condition');
      } else {
        form.setValue('condition', '');
        form.trigger('condition');
      }
      onAfterSelect?.(option);
    },
    [form, onAfterSelect]
  );

  const handleFocus = useCallback(() => {
    if (conditionValue?.value === customOption.value) {
      setConditionValue(null);
      form.setValue('condition', '');
      setShowCustomInput(false);
    }
  }, [conditionValue, form, setShowCustomInput]);

  const noOptionsMessageFn = useCallback(
    () => <NoOptionsMessageComponent onClick={handleNoOptionsClick} />,
    [handleNoOptionsClick]
  );

  return (
    <FormControl
      variant="floating"
      isInvalid={!!form.formState.errors?.condition}
    >
      <SearchableSelect
        labelText="Select condition*"
        value={conditionValue}
        options={conditionOptions}
        onChange={handleChange}
        onFocus={handleFocus}
        menuIsOpen={menuIsOpen}
        onMenuOpen={() => setMenuIsOpen(true)}
        onMenuClose={() => setMenuIsOpen(false)}
        inputValue={inputValue}
        onInputChange={setInputValue}
        noOptionsMessage={noOptionsMessageFn}
      />
      <FormErrorMessage> {form?.formState?.errors?.condition?.value?.message} </FormErrorMessage>
    </FormControl>
  );
}
export const createCondition = async (patientId: string, addedCondition: any, _sharingDisable = false) => {
  const {
    diagnosis_date,
    notes,
    is_shareable,
    clinicalStatus,
    end_date,
    condition_type,
    custom_condition,
    external_reports,
    id,
  } = addedCondition;

  const data: any = {
    resourceType: 'Condition',
    meta: {
      tag: [
        {
          system: FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
          code: !_sharingDisable && is_shareable ? 'share-profile:true' : 'share-profile:false',
          display: !_sharingDisable && is_shareable ? 'True' : 'False',
        },
      ],
    },
    extension: [
      {
        url: FHIR_HL7_STRUCTURE_DEFINITION_CONDITION,
        valueCodeableConcept: {
          coding: [
            {
              system: SNOMED_URL,
              code: condition_type?.value,
              display: condition_type?.label,
            },
          ],
        },
      },
    ],
    identifier: [{ system: `${FHIR_CODE_SYSTEM_URL}/FACT`, value: 'cn' }],
    clinicalStatus: {
      coding: [
        {
          system: `${TERMINOLOGY_CODE_SYSTEM}/condition-clinical`,
          code: clinicalStatus ? 'active' : 'inactive',
          display: clinicalStatus ? 'Active' : 'Inactive',
        },
      ],
    },
    subject: { reference: `Patient/${patientId}` },
  };

  if (diagnosis_date) {
    data.onsetPeriod = end_date ? { start: diagnosis_date, end: end_date } : { start: diagnosis_date };
  }
  if (notes) data.note = [{ text: notes }];
  if (id) data.id = id;

  let newCoding: any = [];
  if (addedCondition?.condition?.value !== customOption.value) {
    const codeValue = addedCondition?.condition?.value || addedCondition?.code?.coding?.[0]?.code;
    newCoding = await medplumApi.valueSetList.getFHIRCodingFromCMS(codeValue);
  } else {
    newCoding = [
      {
        system: 'http://fluentinhealth/fact',
        code: `cn:${PATIENT_DEFINED}`,
        display: custom_condition,
      },
    ];
  }
  data.code = { coding: newCoding };

  if (external_reports?.length) {
    data.evidence = external_reports.map((report: any) => ({
      detail: [{ reference: `${DOCUMENT_REF}/${report?.id}` }],
    }));
  }
  return data;
};
export default function ProfileConditionForm({
  condition,
  conditionOptions,
  conditionTypeOptions,
}: {
  condition: any | null;
  name: string;
  conditionOptions: SelectOptionProps[];
  conditionTypeOptions: any[];
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const { trackEventInFlow } = useAnalyticsService();
  const isMobile = useIsMobile();
  const consentModal = useDisclosure();
  const [isLoading] = useState<boolean>(false);
  const [searchTextString] = useState<string>('');
  const isEditing = !!condition;
  const resultsPopover = useDisclosure();
  const datePickerPopover = useDisclosure();
  const endDatePickerPopover = useDisclosure();
  const { modalDisclosure, setModalProps } = useModalContext();
  const { authenticatedUser } = useAuthService();
  const { answerList, addConditions, updateConditions } = useMasterConditionResponseList(authenticatedUser?.id);
  const [isFormValid, setIsFormValid] = useState(false);
  const [showCustomInput, setShowCustomInput] = useState(
    condition?.code.coding[0].code === `cn:${PATIENT_DEFINED}` || false
  );
  const form = useForm<ConditionFormValues>({
    mode: 'onChange',
    defaultValues: getInitialFormData(condition),
    resolver: zodResolver(conditionSchema(showCustomInput, answerList, condition)),
  });
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid },
  } = form;
  const dateField = form.watch('diagnosis_date');
  const externalReportsField = form.watch('external_reports') || [];
  const noteField = form.watch('notes');
  const conditionInputRef: RefObject<any> = useRef();
  useOutsideClick({
    ref: conditionInputRef,
    handler: () => {
      if (searchTextString.length > 0) return false;
      resultsPopover.onClose();
      return true;
    },
  });

  const onSubmit = async (addedCondition: any) => {
    try {
      const payloadCondition = await createCondition(authenticatedUser?.id, addedCondition);
      if (!isEmptyObject(condition)) {
        await updateConditions({ conditionId: condition?.id, payloadCondition });
      } else {
        await addConditions(payloadCondition);
      }
      recordConditionEvents(trackEventInFlow, {
        co_entry_point: 'my health profile',
        EventName: isEditing ? 'ConditionEdited' : 'ConditionAddCompleted',
        co_name: addedCondition?.condition?.display ?? '',
        co_start_date: dayjs(addedCondition?.diagnosis_date).format('YYYY/MM/DD') ?? null,
        co_end_date: dayjs(addedCondition?.end_date).format('YYYY/MM/DD') ?? null,
        co_status: addedCondition?.clinicalStatus.toString(),
        co_notes: addedCondition?.notes,
        co_chronicity: addedCondition?.condition_type?.display?.toLocaleLowerCase() === 'acute' ? 'acute' : 'chronic',
        co_records_added: !!addedCondition?.external_reports?.length,
        co_shared: Boolean(addedCondition?.external_reports?.length),
      });
      toast({
        title: `Successfully ${isEditing ? 'edit' : 'add'}ed the condition`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
    } catch (_err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      modalDisclosure.onClose();
    }
    if (!isEditing) {
      navigate(NavigationHelper.getEhrView(false, 'conditions'));
    }
    modalDisclosure.onClose();
  };

  const handleConsentModalSubmit = useCallback(() => {
    handleSubmit(onSubmit)();
    consentModal.onClose();
  }, [onSubmit, handleSubmit]);
  const onPrimaryButtonClick = useCallback(() => {
    const conditionValue = form.getValues('condition');
    if (!condition && conditionValue?.value === 'cn:aids') {
      consentModal.onOpen();
    } else {
      handleSubmit(onSubmit)();
    }
  }, [onSubmit, handleSubmit]);
  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('diagnosis_date', dayjs(date).format('YYYY-MM-DD'));
      if (!condition) {
        recordConditionEvents(trackEventInFlow, {
          co_entry_point: 'my health profile',
          EventName: 'ConditionAddInProgStartDate',
          co_start_date: dayjs(date).format('YYYY/MM/DD'),
        });
      }
    } else {
      form.setValue('diagnosis_date', '');
    }
    datePickerPopover.onClose();
  };
  const datePickerClearHandler = () => {
    form.setValue('diagnosis_date', '');
    datePickerPopover.onClose();
  };
  const removeAttachedMedicalRecordHandler = (record: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item) => item.id !== record.id)
    );
  };
  const onChangeConditionTypes = useCallback((option: SelectOptionProps | any) => {
    if (option) {
      form.setValue('condition_type', { value: option.value, label: option.label });
      form.trigger('condition_type');
    } else {
      form.setValue('condition_type', { value: '', label: '' });
      form.trigger('condition_type');
    }
  }, []);
  useEffect(() => {
    setModalProps((prevState: IModal) => ({
      ...prevState,
      onPrimaryButtonClick,
      primaryButtonEnabled: isValid && isFormValid,
      isPrimaryButtonLoading: isSubmitting || isLoading,
    }));
  }, [isValid, isLoading, isSubmitting, form, isFormValid]);
  const handleKeyPressLetters = (e: React.KeyboardEvent) => {
    if (!/[A-Za-z ]/.test(e.key)) {
      e.preventDefault();
    }
  };
  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);
  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        gap="8"
        mt={6}
      >
        <ConditionSelect
          setShowCustomInput={setShowCustomInput}
          onAfterSelect={(e) => {
            if (!condition) {
              recordConditionEvents(trackEventInFlow, {
                co_entry_point: 'my health profile',
                EventName: 'ConditionAddInProgName',
                co_name: String(e?.value),
              });
            }
          }}
          conditionOptions={conditionOptions}
        />
        {showCustomInput && (
          <FormControl
            variant="floating"
            isInvalid={!!form.formState.errors.custom_condition}
            isRequired
          >
            <Input
              onKeyDown={handleKeyPressLetters}
              placeholder=" "
              defaultValue={form.watch('custom_condition')}
              {...form.register('custom_condition')}
              onChange={(e) => {
                form.setValue('custom_condition', e?.target?.value);
                form.trigger('custom_condition');
              }}
              onBlurCapture={(e) => {
                if (e.target.value) {
                  recordConditionEvents(trackEventInFlow, {
                    co_entry_point: 'my health profile',
                    EventName: 'ConditionAddInProgName',
                    co_name: `CustomEntry - ${e.target.value}`,
                  });
                }
              }}
            />
            <FormLabel>Enter the condition*</FormLabel>
            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>
        )}
        <Flex
          mb="5"
          alignItems="center"
          gap="12px"
        >
          <GroupedRadioButtons
            defaultValue={form.getValues('condition_type')?.value}
            onChange={(selectedValue: string) => {
              const selectedOption = conditionTypeOptions.find(
                (option) => String(option?.code) === String(selectedValue)
              );
              onChangeConditionTypes({
                value: selectedOption?.code,
                label: selectedOption?.display,
              });
              if (!condition) {
                recordConditionEvents(trackEventInFlow, {
                  co_entry_point: 'my health profile',
                  EventName: 'ConditionAddInProgChronicity',
                  co_chronicity: selectedOption?.display,
                });
              }
            }}
          >
            {conditionTypeOptions
              ?.slice()
              .reverse()
              .map((option, index) => (
                <React.Fragment key={option.code}>
                  <Radio value={option.code}>{option.display}</Radio>
                  {index < conditionTypeOptions.length - 1 && <GroupedRadioButtonsDivider />}
                </React.Fragment>
              ))}
          </GroupedRadioButtons>
          <SidebarHelperTooltip
            text="Acute and chronic conditions"
            tooltipText="A chronic health condition is a long-lasting medical condition that persists over an extended period, often requiring ongoing management and treatment to control its symptoms and effects on the individual's health. An acute health condition is a sudden and severe medical issue characterised by rapid onset of symptoms that typically demand immediate attention and may resolve or escalate quickly without ongoing management."
            containerProps={{ p: 0, m: 0, ml: '-6px' }}
            showText={false}
          />
        </Flex>

        <DatePickerField
          name="diagnosis_date"
          labelText="Date of diagnosis"
          errorText="This field is required"
          rules={{ required: true }}
          isInvalid={
            form.formState.touchedFields.diagnosis_date && form.control._formValues.diagnosis_date.length === 0
          }
          datePickerChangeHandler={datePickerChangeHandler}
          datePickerClearHandler={datePickerClearHandler}
          datePickerPopover={datePickerPopover}
          isClearDateButtonDisabled={dateField?.length === 0}
          selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
          popoverProps={{ placement: 'bottom-start' }}
          maxDate={new Date()}
        />

        <Flex
          mt={2}
          alignItems="center"
          justifyContent="space-between"
          borderBottom="1px solid var(--chakra-colors-iris-500)"
          color="var(--chakra-colors-iris-500)"
        >
          <FormLabel
            fontSize="18px"
            fontWeight={400}
            mb="1"
          >
            Do you still have this condition?
          </FormLabel>
          <Switch
            size="md"
            mb={2}
            isChecked={form.watch('clinicalStatus')}
            onChange={(e) => {
              form.setValue('clinicalStatus', e.target.checked);
              recordConditionEvents(trackEventInFlow, {
                EventName: 'ConditionAddInProgStatus',
                co_status: e.target.checked.toString(),
              });
            }}
          />
        </Flex>
        {!form.watch('clinicalStatus') && (
          <Flex mt={4}>
            <DatePickerField
              name="end_date"
              labelText="End date"
              datePickerChangeHandler={(date: Date | null) => {
                if (dayjs(date).isValid()) form.setValue('end_date', dayjs(date).format('YYYY-MM-DD'));
                else form.setValue('end_date', '');
                recordConditionEvents(trackEventInFlow, {
                  EventName: 'ConditionAddInProgEndDate',
                  co_end_date: dayjs(date).format('YYYY/MM/DD'),
                });
                endDatePickerPopover.onClose();
              }}
              datePickerClearHandler={() => form.setValue('end_date', '')}
              datePickerPopover={endDatePickerPopover}
              isClearDateButtonDisabled={form.watch('end_date')?.length === 0}
              selected={dayjs(form.watch('end_date')).isValid() ? dayjs(form.watch('end_date')).toDate() : null}
              popoverProps={{ placement: 'bottom-start' }}
              maxDate={new Date()}
            />
          </Flex>
        )}
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.notes}
        >
          <Input
            id="notes"
            placeholder=" "
            {...register('notes')}
            onBlur={(e) => {
              if (!condition)
                recordConditionEvents(trackEventInFlow, {
                  co_entry_point: 'my health profile',
                  EventName: 'ConditionAddInProgNotes',
                  co_notes: e.target.value,
                });
            }}
          />
          <FormLabel fontWeight="normal">Add your notes here</FormLabel>
          <FormErrorMessage>
            {(noteField?.length || 0) > MAX_ALLOWED_CHARACTERS
              ? `The maximum number of characters allowed is ${MAX_ALLOWED_CHARACTERS}`
              : 'This field is required'}
          </FormErrorMessage>
        </FormControl>
        <Stack spacing={2}>
          <MedicalRecordSelect
            labelText="Link health records"
            onSelectExtra={(e) => {
              if (!condition)
                recordConditionEvents(trackEventInFlow, {
                  co_entry_point: 'my health profile',
                  EventName: 'ConditionAddInProgRecordsAdded',
                  co_records_added: !!e.length,
                  co_shared: e.length !== 0,
                });
            }}
          />
          {externalReportsField?.length > 0 && (
            <LinkedDocumentsCard
              records={externalReportsField}
              onRemove={removeAttachedMedicalRecordHandler}
              showRemoveButton
            />
          )}
        </Stack>
      </Box>
      <ShareEntryCheckbox
        entityName="condition"
        tooltipText="You have the right to keep certain conditions private. You can do this when adding a condition or whenever you share your profile. Please keep in mind that hidden conditions may impact diagnosis when your profile is shared with a doctor."
        isDisabled={isSubmitting}
        {...(isMobile ? { mt: '32px' } : { position: 'absolute', bottom: '26px' })}
        onChange={(e) => {
          if (!condition) {
            recordConditionEvents(trackEventInFlow, {
              co_entry_point: 'my health profile',
              EventName: 'ConditionAddInProgShared',
              co_shared: e.target.checked,
            });
          }
        }}
      />
      <ConsentModal
        showCloseButton={false}
        minWidth="338px"
        modalContentProps={{ width: '338px', py: '32px' }}
        {...consentModal}
      >
        <ConsentModalHeading mb="16px">We care about your privacy</ConsentModalHeading>
        <ConsentModalContent>
          <Text>
            You are sharing information about your HIV status voluntarily. Fluent does not mandate such details. You can
            choose to hide this information later.
          </Text>
          <Text>
            By continuing, you acknowledge that you have read the
            <Link
              as={NavLink}
              to="/terms-n-conditions/view"
              fontWeight="400"
              textDecoration="underline"
            >
              Terms of Use
            </Link>
            and
            <Link
              as={NavLink}
              to="/privacy-policy/view"
              fontWeight="400"
              textDecoration="underline"
            >
              Privacy Policy
            </Link>
            .
          </Text>
        </ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            size="md"
            onClick={consentModal.onClose}
          >
            Cancel
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={handleConsentModalSubmit}>I understand</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
    </FormProvider>
  );
}
