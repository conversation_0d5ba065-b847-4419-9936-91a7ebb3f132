import { Edit3, Trash as TrashIcon } from 'react-feather';
import { Container, Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { recordMedicationsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { medplumApi } from '@user/lib/medplum-api';
import { useDebounce } from 'usehooks-ts';

import { Medication } from '@lib/models/medication';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { MODAL_VARIANTS, Modal, ModalProvider, useModal } from '../../../../../components/Modal';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../../components/ui/Menu';
import MedicationForm from './MedicationForm';
import {
  Card,
  CardHeading,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHeading,
  SidebarHelperTooltip,
} from '../SidebarComponents';
import { FormSkeleton } from '../../../../../components/ui/Form';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { useMasterQuestionnaireList, useMedication } from 'src/app/user/lib/medplum-state';
import { MEDPLUM_QUESTIONNAIRE, deleteIdentifier } from '@lib/constants';
import {
  MEDICATION_SUPPLEMENT,
  NavigationHelper,
  PATIENT_DEFINED,
  ROUTE_ACTIONS,
  ROUTE_VARIABLES,
} from 'src/app/user/lib/constants';
import { getValueSetByMasterList } from '@lib/utils/utils';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from '../LinkedDocumentsCard';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../ConsentModal';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { ISidebarProps } from '@lib/models/misc';

function formatDosage(dosage: any[]): string {
  const timingOrder = ['MORN', 'AFT', 'EVE', 'NIGHT'];
  return timingOrder
    .map((time) => {
      const dose = dosage?.find((d) => d.timing?.repeat?.when?.includes(time));
      return dose?.doseAndRate?.[0]?.doseQuantity?.value || 0;
    })
    .join('-');
}

function MedicationCard({
  title,
  dosage,
  children,
  frequency,
  onRemove,
  onEdit,
  isPublicMode = false,
  isLoading,
  isCustomEntry,
}: PropsWithChildren<{
  title: string;
  children: any;
  dosage: string;
  frequency: string | undefined;
  onRemove: () => void;
  onEdit: () => void;
  isPublicMode: boolean;
  isLoading?: boolean;
  isCustomEntry?: boolean;
}>) {
  const deleteModal = useDisclosure();
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={onRemove}
            isLoading={isLoading}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card
        mt="4"
        pb="12px"
      >
        <Flex
          gap="8px"
          direction="column"
        >
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading maxWidth="90%">
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {dosage !== '0-0-0-0' && (
            <Text
              bgColor="iris.500"
              color="white"
              rounded="sm"
              width="fit-content"
              borderRadius="4px"
              px="2"
              display="inline-flex"
              alignItems="center"
              maxWidth="50%"
              title={`${dosage}`}
            >
              {dosage}
            </Text>
          )}
          {frequency && (
            <Text
              bgColor="iris.500"
              color="white"
              rounded="sm"
              width="fit-content"
              borderRadius="4px"
              px="2"
              mb="4px"
              display="inline-flex"
              alignItems="center"
              maxWidth="50%"
              title={`${frequency}`}
            >
              {frequency}
            </Text>
          )}

          {children}
        </Flex>
      </Card>
    </>
  );
}

export default function ProfileSidebarMedication({ onClose, action }: ISidebarProps) {
  const toast = useToast();
  const navigate = useNavigate();
  const medicationModal = useModal();
  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();
  const { PROFILE, EHR, MEDICATIONS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const sharePatientId = localStorage.getItem('sharePatientId');

  const { authenticatedUser } = useAuthService();
  const { medicationList, deleteMedication } = useMedication(!isPublicMode ? authenticatedUser?.id : sharePatientId);
  const { masterList } = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.MEDICATION}`);
  const updatedMedicationList = medicationList.filter((med: any) =>
    med.identifier?.some((id: any) => id.value === 'medications')
  );
  const [selectedMedication, setSelectedMedication] = useState<Medication | null>(null);
  const [, setError] = useState<any>(null);
  const [searchText, setSearchText] = useState<string>('');
  const debouncedSearchText = useDebounce(searchText, 500);
  const [submitting, setSubmitting]: any = useState<boolean>();
  const [medicationOptions, setMedicationOptions] = useState<any[]>([]);

  const filteredMasterList = (id: string) => {
    const foundItem = masterList?.[0]?.item?.find((i: any) => i.linkId === id);
    return foundItem || null;
  };

  const medicationAdministeredOptions: any = getValueSetByMasterList(
    masterList,
    filteredMasterList('how-medication-administered')?.answerValueSet ?? ''
  );

  const onAddHandler = () => {
    setSelectedMedication(null);
    recordMedicationsEvents(trackEventInFlow, {
      EventName: 'MedicationsAddStarted',
      me_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${MEDICATIONS}/${ADD}`);
  };
  useEffect(() => {
    if (action === ADD) {
      medicationModal.modalDisclosure.onOpen();
    }
  }, [action]);
  const onEditHandler = (medication: Medication) => {
    setSelectedMedication(medication);
    recordMedicationsEvents(trackEventInFlow, {
      EventName: 'MedicationsInteracted',
    });
    medicationModal.modalDisclosure.onOpen();
  };

  const onRemoveHandler = async (medication: Medication) => {
    try {
      const identifier = `${deleteIdentifier}:medication`;
      await deleteMedication({ medicationId: medication.id, identifier });
      recordMedicationsEvents(trackEventInFlow, {
        EventName: 'MedicationsRemoved',
        me_name: medication?.drug_name,
      });
      toast({
        title: 'Medication removed',
        description: 'Your medication has been removed.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      setError(error);
    }
  };

  useEffect(() => {
    if ((debouncedSearchText.length === 0 || debouncedSearchText.length >= 3) && !isPublicMode) {
      medplumApi.valueSetList
        .getMedicationFromSnomed(isPublicMode, searchParams.get('access_token'), debouncedSearchText || '')
        .then((val) => {
          const data = val.map((e: any) => ({ label: e.display, value: e.code }));
          setMedicationOptions(data);
        });
    }
  }, [debouncedSearchText]);

  const closeFn = () => {
    medicationModal.modalDisclosure.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'medication'));
  };
  const formSubmiting = (incoming: boolean) => {
    setSubmitting(incoming);
  };
  return (
    <>
      <ModalProvider {...medicationModal}>
        <Modal
          variant={MODAL_VARIANTS.PERIWINKLE}
          title="Medications"
          // primaryButtonLabel={selectedCondition ? 'Save' : 'Add'}
          showSecondaryButton={false}
          isCentered
          {...medicationModal.modalProps}
          {...medicationModal.modalDisclosure}
          onClose={closeFn}
        >
          <Suspense fallback={<FormSkeleton />}>
            {submitting ? (
              <FormSkeleton />
            ) : (
              <MedicationForm
                medication={selectedMedication}
                closeDialog={closeFn}
                medicationOptions={medicationOptions}
                medicationAdministeredOptions={medicationAdministeredOptions}
                onSearch={setSearchText}
                formSubmiting={formSubmiting}
              />
            )}
          </Suspense>
          <Suspense fallback={<FormSkeleton />} />
        </Modal>
      </ModalProvider>
      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Stack
          py="4"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Medications</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {updatedMedicationList?.length === 0 && (
            <SidebarEmptyState
              actionButtonText="Add"
              title="Update medication details"
              imageSrc="/empty-card-medication.png"
              completeInfoText={isPublicMode ? undefined : '+6% to complete your profile'}
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
              isPublicMode={isPublicMode}
            />
          )}
          {!isPublicMode && updatedMedicationList?.length !== 0 && (
            <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>
          )}

          {updatedMedicationList?.map((answer: any) => {
            const title = answer?.medicationCodeableConcept?.coding?.[0]?.display;
            const dosage = formatDosage(answer?.dosage);
            const frequency =
              answer?.dosage?.[0]?.timing?.repeat?.boundsDuration?.value &&
              answer?.dosage?.[0]?.timing?.repeat?.boundsDuration?.value !== 0
                ? `${answer.dosage[0].timing.repeat.boundsDuration.value} ${answer.dosage[0].timing.repeat.boundsDuration.unit}`
                : undefined;
            return (
              <MedicationCard
                key={answer.id}
                title={title}
                dosage={dosage}
                frequency={frequency}
                onEdit={() => onEditHandler(answer)}
                onRemove={() => onRemoveHandler(answer)}
                isPublicMode={isPublicMode}
                isCustomEntry={answer.medicationCodeableConcept?.coding?.[0]?.code === `me:${PATIENT_DEFINED}`}
              >
                {answer.derivedFrom?.length > 0 && (
                  <Flex
                    direction="column"
                    gap="2px"
                  >
                    <LinkedDocumentsLabel />
                    <LinkedDocumentsCard records={useExtractDocumentResource(answer.derivedFrom)} />
                  </Flex>
                )}
              </MedicationCard>
            );
          })}

          <Spacer />
          {!isPublicMode && (
            <SidebarHelperTooltip
              text="What is a medication?"
              tooltipText="The WHO explains that medications are compounds manufactured to diagnose, cure, mitigate, treat, or prevent diseases. These are critical for maintaining health, minimising adverse drug reactions, and identifying side effects. Include any current medications/supplements you are taking and those previously recommended for long-term ailments. Discuss this list with your doctors to ensure that they are taken optimally and to guide any future prescriptions."
            />
          )}
        </Stack>
      </Container>
    </>
  );
}
