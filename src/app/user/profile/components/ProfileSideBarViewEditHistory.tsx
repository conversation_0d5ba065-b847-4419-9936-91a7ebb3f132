import { Box, Container, Divider, Flex, HStack, Heading, Image, Stack, Text, VStack } from '@chakra-ui/react';
import { useAuditEventList, usePatientSettingsAll } from '@user/lib/medplum-state';
import React from 'react';
import { enumMesurment, enumUnit } from '@user/lib/constants';

import { ISidebarProps } from '@lib/models/misc';
import { SidebarCloseButton, SidebarHeading } from './SidebarComponents';
import { useAuthService } from '@lib/state';
import { findFHIRExtension } from '../../settings/SettingsPage';
import { convertCmToFeetAndInches, convertWeightKgToLbs } from '@lib/utils/observationsUtils';

function UpdateCard({ updates, data }: any) {
  type Agent = {
    type: {
      coding: Array<{ code: string }>;
    };
    who: {
      resource: {
        name: Array<{
          given: Array<string>;
          family: string;
        }>;
      };
    };
  };

  type EntityDetail = {
    valueString: string | null;
  };
  type Entity = {
    what: {
      resource: {
        code: {
          coding: Array<{ display: string }>;
        };
      };
    };
    type: {
      code: string;
    };
    detail: EntityDetail[];
  };
  type subtype = {
    code: string;
  };

  type Update = {
    agent: Agent[];
    entity: Entity[];
    subtype: subtype[];
  };

  const dataCreation = (update: Update): any => {
    const { agent, entity } = update;
    const preferredHeightUnit: string =
      findFHIRExtension(enumMesurment.HEIGHT_UNIT, data, '')?.extnVal?.extension?.find(
        (extnValue: any) => extnValue.url === 'preference'
      )?.valueCoding.code === enumUnit.CM
        ? enumUnit.CM
        : enumUnit.INCHES_TEXT;
    const preferredWeightUnit: string =
      findFHIRExtension(enumMesurment.WEIGHT_UNIT, data, '')?.extnVal?.extension?.find(
        (extnValue: any) => extnValue.url === 'preference'
      )?.valueCoding.code === enumUnit.KG
        ? enumUnit.KG
        : enumUnit.POUNDS_TEXT;
    // Determine who made the change
    const changedBy = agent
      .map((el) => {
        if (el.type.coding[0].code === 'PAT') {
          return 'You';
        }
        const name = el.who?.resource?.name[0];
        return name ? `${name.given[0]} ${name.family}` : 'Unknown';
      })
      .join(', ');

    // Determine what changed
    const changedWhat = entity?.[0]?.what?.resource?.code?.coding?.[0]?.display?.toLowerCase()
      ? entity?.[0]?.what?.resource?.code?.coding?.[0]?.display?.toLowerCase()
      : update?.subtype?.[0]?.code?.toLowerCase();

    // Determine the previous value
    const changedFrom = entity
      .filter((en) => en.type.code === 'audit-event-types:previous')
      .flatMap((en) => en.detail.map((d) => d.valueString))
      .filter(Boolean)[0]; // Get the first non-null value

    let fromText = changedFrom ? `${changedFrom}` : null;

    // Determine the current value
    let changedTo = entity
      .filter((en) => en.type.code === 'audit-event-types:current')
      .flatMap((en) => en.detail.map((d) => d.valueString))
      .filter(Boolean)[0]; // Get the first non-null value
    // console.log('data', `${changedBy} updated ${changedWhat}${fromText} to ${changedTo}`);
    // return `${changedBy} updated ${changedWhat}${fromText} to ${changedTo}`;
    const midText = changedTo ? 'updated' : 'cleared';
    if (changedWhat === enumMesurment.BODY_HEIGHT || changedWhat === enumMesurment.BODY_WEIGHT) {
      const isHeight = changedWhat === enumMesurment.BODY_HEIGHT;

      const convertValue = (value: string | number | null | undefined) => {
        if (value == null) return null;

        const num = Number(value);

        if (isHeight) {
          return preferredHeightUnit === enumUnit.CM
            ? `${num.toFixed(2)} ${enumUnit.CM}`
            : convertCmToFeetAndInches(num);
        }
        return preferredWeightUnit === enumUnit.KG
          ? `${num} ${enumUnit.KG}`
          : `${convertWeightKgToLbs(String(num))} ${enumUnit.POUNDS_TEXT}`;
      };

      fromText = convertValue(fromText);
      changedTo = convertValue(changedTo);
    }

    return { changedBy, changedWhat, fromText, changedTo, midText };
  };
  const message = dataCreation(updates);

  return (
    <>
      {/* <Text
        fontSize="sm"
        color="gray.500"
      >
        {date}
      </Text> */}

      <VStack align="start">
        <Box width="100%">
          <HStack
            wrap="wrap"
            fontSize=".95rem"
          >
            <Text>
              {`${message.changedBy} ${message.midText} your ${message.changedWhat}`}
              {message.fromText && (
                <>
                  &nbsp;from&nbsp;
                  <b
                    className={
                      message.changedWhat === enumMesurment.BODY_WEIGHT ||
                      message.changedWhat === enumMesurment.BODY_HEIGHT
                        ? ''
                        : 'capitalize'
                    }
                  >
                    {message.fromText}
                  </b>
                </>
              )}
              {message.changedTo && (
                <>
                  &nbsp;to&nbsp;
                  <b
                    className={
                      message.changedWhat === enumMesurment.BODY_WEIGHT ||
                      message.changedWhat === enumMesurment.BODY_HEIGHT
                        ? ''
                        : 'capitalize'
                    }
                  >
                    {message.changedTo}
                  </b>
                </>
              )}
            </Text>
          </HStack>
        </Box>
      </VStack>
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function ProfileSidebarViewEditHistory({ onClose, name }: ISidebarProps) {
  const { authenticatedUser } = useAuthService();
  const { auditEventList } = useAuditEventList(authenticatedUser?.id);
  const { data } = usePatientSettingsAll(authenticatedUser?.id);

  const mergeEventsByDate = (auditEvents: any[]) => {
    return auditEvents?.reduce((acc, event) => {
      const recordedDate = new Date(event.recorded).toISOString().split('T')[0]; // Get date part
      if (!acc[recordedDate]) {
        acc[recordedDate] = {
          date: recordedDate,
          events: [],
        };
      }
      acc[recordedDate].events.push(event);
      return acc;
    }, {});
  };

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      overflowX="hidden"
      className="hide-scrollbar"
    >
      <Stack py="4">
        <Flex justifyContent="space-between">
          <SidebarHeading>Edit History</SidebarHeading>
          <SidebarCloseButton onClick={onClose} />
        </Flex>
      </Stack>
      <VStack
        align="start"
        height="calc(100% - 100px)"
      >
        {Object.keys(mergeEventsByDate(auditEventList)).length > 0 ? (
          Object.values(mergeEventsByDate(auditEventList)).map((list: any) => {
            const formattedDate = new Date(list?.date).toLocaleDateString('en-IN').replace(/-/g, '/');
            return (
              <React.Fragment key={formattedDate}>
                <Text
                  fontSize="sm"
                  color="gray.500"
                >
                  {formattedDate}
                </Text>
                <Box
                  bg="Periwinkle-100"
                  boxShadow="0px 1px 4px 0px rgba(73, 90, 228, 0.12)"
                  borderRadius="12px"
                  p={5}
                  mb="16px !important"
                  width="100%"
                  maxW={['100%', '80%', '60%', '400px']} // Responsive values for maxW
                >
                  <VStack
                    align="start"
                    spacing={3}
                  >
                    {list.events.map((el: any, index: number) => {
                      return (
                        <React.Fragment key={el.id}>
                          <UpdateCard
                            updates={el}
                            data={data}
                            index={index}
                            size={list.events.length}
                          />
                          {index < list.events.length - 1 && <Divider mt={2} />}
                        </React.Fragment>
                      );
                    })}
                  </VStack>
                </Box>
              </React.Fragment>
            );
          })
        ) : (
          <Flex
            height="100%"
            width="100%"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
          >
            <Image
              src="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
              mb="4"
              borderRadius="full"
              objectFit="cover"
            />
            <Heading
              fontSize="2xl"
              textAlign="center"
              mb="12px"
              color="fluentHealthText.100"
              letterSpacing="-.48px"
            >
              Nothing here yet
            </Heading>
            <Text
              align="center"
              px={1}
              fontSize="18px"
              letterSpacing="-.36px"
              color="fluentHealthText.100"
            >
              Your edit history is a work in progress. Stay tuned for changes when you update your personal details.
            </Text>
          </Flex>
        )}

        {/* {Object.values(mergeEventsByDate(auditEventList))?.map((updates: any) => {
          return (
            <UpdateCard
              date="25/08/2021"
              updates={updates}
            />
          );
        })} */}
        {/* {auditEventList.map((audit: any) => (
          <UpdateCard
            date={audit?.recordedDate}
            updates={audit}
          />
        ))} */}
      </VStack>
    </Container>
  );
}
