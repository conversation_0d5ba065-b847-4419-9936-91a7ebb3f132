import React, { Suspense, useEffect, useState } from 'react';
import { ChevronLeft as ChevronLeftIcon } from 'react-feather';
import { Card, Container, Flex, Stack, Text, VStack, useTheme } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

import { LIFESTYLE_NUTRITION, NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '../../../lib/constants';
import { SidebarMenuItem } from '../SidebarMenuItem';
import { ISidebarProps } from '@lib/models/misc';
import { MasterLifestyleCategory } from '@lib/models/lifestyle-and-nutrition';
import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { QuestionList } from './QuestionList';
import { SidebarCloseButton } from '../SidebarComponents';
import { usePublicSettings } from '@lib/state';

export function ProfileSidebarLifestyleAndNutrition({ onClose, subActive, clickOutside }: ISidebarProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { PROFILE, EHR, LIFESTYLE_NUTRITION: LIFESTYLE_NUTRITION_ROUTE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { isPublicMode } = usePublicSettings();

  const [selectedLifestyleCategory, setSelectedLifestyleCategory] = useState<MasterLifestyleCategory | null>(null);

  const handleGoBack = () => {
    if (!selectedLifestyleCategory) {
      return;
    }
    setSelectedLifestyleCategory(null);
    if (isPublicMode) return;
    navigate(NavigationHelper.getEhrView(false, 'lifestyle-nutrition'));
  };

  useEffect(() => {
    if (subActive) {
      const category = LIFESTYLE_NUTRITION.find((l) => l.route === subActive);
      if (category) {
        setSelectedLifestyleCategory({ label: category.value, name: category.name });
      }
    }
  }, [subActive]);

  useEffect(() => {
    if (clickOutside) setSelectedLifestyleCategory(null);
  }, [clickOutside]);

  return (
    <Container
      position="relative"
      height="full"
      overflowY="scroll"
      className="hide-scrollbar"
    >
      <Stack
        py="4"
        height="full"
      >
        <Flex
          justifyContent="space-between"
          fontSize="2xl"
          fontWeight="400"
          pb="16px"
        >
          <Flex
            gap="8px"
            alignItems="center"
            onClick={handleGoBack}
          >
            {selectedLifestyleCategory && (
              <ChevronLeftIcon
                size={24}
                cursor="pointer"
                color={theme.colors.fluentHealthText[100]}
              />
            )}
            <Text>{selectedLifestyleCategory?.label ?? 'Lifestyle and Nutrition'}</Text>
          </Flex>
          <SidebarCloseButton onClick={onClose} />
        </Flex>
        {!selectedLifestyleCategory && (
          <Card
            bgColor="white"
            borderRadius="xl"
            borderWidth="1px"
            borderStyle="solid"
            borderColor="periwinkle.400"
            boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
            gap="0"
            w="full"
          >
            <VStack
              alignItems="left"
              p="2"
              spacing={1}
            >
              {LIFESTYLE_NUTRITION.map(({ name, value, route }: any) => (
                <React.Fragment key={name}>
                  <SidebarMenuItem
                    title={value}
                    onClick={() => {
                      setSelectedLifestyleCategory({ label: value, name });
                      if (!isPublicMode) {
                        navigate(`/${PROFILE}/${EHR}/${LIFESTYLE_NUTRITION_ROUTE}/${route}/${ADD}`);
                      }
                    }}
                  />
                </React.Fragment>
              ))}
            </VStack>
          </Card>
        )}
        <Suspense fallback={<FluentHealthLoader />}>
          {selectedLifestyleCategory && <QuestionList selectedLifestyleCategory={selectedLifestyleCategory} />}
        </Suspense>
      </Stack>
    </Container>
  );
}
