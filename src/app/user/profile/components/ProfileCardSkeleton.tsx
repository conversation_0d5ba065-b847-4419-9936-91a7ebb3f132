// Package modules
import React, { Ref, forwardRef } from 'react';
import { ChakraProps, Flex, Skeleton } from '@chakra-ui/react';

// Local modules
import GenericProfileCard from './GenericProfileCard';

// eslint-disable-next-line @typescript-eslint/naming-convention
export const ProfileCardItemSkeleton = forwardRef((props: ChakraProps, ref: Ref<HTMLDivElement>) => (
  <Flex
    ref={ref}
    gap="24px"
    {...props}
  >
    <Skeleton
      height="300px"
      width="240px"
      borderRadius="8px"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.400"
    />
    <Flex
      direction="column"
      gap="24px"
      flex="1"
    >
      <Skeleton
        height="24px"
        borderRadius="4px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="24px"
        borderRadius="4px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="24px"
        borderRadius="4px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="24px"
        borderRadius="4px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
      <Skeleton
        height="104px"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.400"
      />
    </Flex>
  </Flex>
));

export function ProfileCardSkeleton({ title = '' }) {
  return (
    <GenericProfileCard
      title={title}
      info=""
      actionButton={() => {}}
    >
      <ProfileCardItemSkeleton />
    </GenericProfileCard>
  );
}
