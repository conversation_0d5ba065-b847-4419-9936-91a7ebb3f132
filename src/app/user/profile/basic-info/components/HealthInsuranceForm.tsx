import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputLeftElement,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { Buffer } from 'buffer';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useState } from 'react';
import {
  DRAG_AND_DROP_AREA_VARIANT,
  DragAndDropAlertPrompt,
  DragAndDropArea,
  DragAndDropFilePreview,
  DragPrompt,
  useDragAndDropArea,
} from '@components/ui/DragAndDrop';
import { useHealthInsuranceList } from '@user/lib/medplum-state';
import { SearchableSelect, SelectOptionProps } from '@components/ui/Select';
import { SuggestionOptionProps } from '@components/ui/Form';
import { medplumApi } from '@user/lib/medplum-api';
import { phoneNumberRegex } from '@utils/regex';
import { recordHealthInsuranceEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF, NavigationHelper } from '@user/lib/constants';
import { useNavigate } from 'react-router-dom';

import { useModalContext } from 'src/components/Modal';
// import { AnalyticsEventName, AnalyticsFlow, EventPropsNames } from '@lib/analyticsService';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { handleKeyPressNumbers } from '@lib/utils/utils';
import { FH_UI_CODESYSTEM } from 'src/app/medical-records/lib/constants';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';
import { MEDPLUM_QUESTIONNAIRE } from '@lib/constants';

const URN_ADD_HEALTHCARE_INSURANCE_QUESTIONNAIRESPONSE = 'urn:uuid:3359150e-a83e-4aa3-8b10-14e19c3c58a6';
const URN_ADD_HEALTHCARE_INSURANCE_FILE = 'urn:uuid:dd8e03d9-e432-4f45-9a87-db9f8e289efd';

const MAX_FILES = 1;
const MAX_SIZE = 25000000;

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
};
const DELETE_QUESTIONNAIRE_TASK = 'urn:uuid:6aecda40-4b5b-4778-975e-9b6db4ae6bc8 ';

const getInitialData = (healthInsurance?: any) => {
  const { item: items } = healthInsurance || {};
  const organisationData = items?.find((item: any) => {
    return item.linkId === 'health-insurance-provider-name';
  });
  const organisation = organisationData?.answer[0].valueCoding;
  const contactNumberData = items?.find((item: any) => {
    return item.linkId === 'health-insurance-contact-number';
  });
  const contactNumber = contactNumberData?.answer[0]?.valueString;

  const policyNumberData = items?.find((item: any) => {
    return item.linkId === 'health-insurance-policy-number';
  });
  const policyNumber = policyNumberData?.answer[0]?.valueString;
  const fileReferenceData: any = items?.find((item: any) => {
    return item.linkId === 'health-insurance-reference';
  });
  const fileReference = fileReferenceData?.answer[0]?.valueReference?.resource;
  return {
    company: {
      value: organisation?.code,
      label: organisation?.display,
    },
    contact_number: contactNumber || '',
    policy_number: policyNumber || '',
    file_reference: fileReference || '',
  };
};

const getCompanyOption = (company: any | null): SuggestionOptionProps | null => {
  if (!company) {
    return null;
  }
  const companyText = company?.item?.find((i: any) => i.linkId === 'health-insurance-provider-name')?.answer?.[0]
    ?.valueCoding;
  return {
    label: companyText?.display,
    value: companyText?.code,
  };
};

function CompanySelect({
  company,
  onAfterSelect,
  companyOptions,
  healthInsurance,
  setIsFormValid,
  setFormChanged,
}: {
  company: any | null;
  onAfterSelect?: (value: SuggestionOptionProps) => void;
  companyOptions: SelectOptionProps[];
  healthInsurance: any;
  setIsFormValid: (flag: boolean) => void;
  setFormChanged: (flag: boolean) => void;
}) {
  const form = useFormContext();
  const [companyValue, setCompanyValue] = useState<SuggestionOptionProps | null>(getCompanyOption(company));
  const { trackEventInFlow } = useAnalyticsService();

  const options = companyOptions;

  const onCompanySelect = useCallback((option: SelectOptionProps | any) => {
    if (option) {
      form.setValue('company', option);
      form.trigger('company');
      setCompanyValue(option);
    } else {
      form.setValue('company', '');
      form.trigger('company');
      setCompanyValue(null);
    }
    if (!healthInsurance) {
      recordHealthInsuranceEvents(trackEventInFlow, {
        EventName: 'HealthInsuranceInProgPolicyName',
        hi_policy_provider: option?.value,
      });
    }
    setIsFormValid(true);
    setFormChanged(true);
    onAfterSelect?.(option);
  }, []);

  return (
    <SearchableSelect
      labelText="Name of your health insurance provider*"
      value={companyValue}
      options={options}
      onChange={onCompanySelect}
    />
  );
}

export default function HealthInsuranceForm({
  healthInsurance,
  // answerList,
  companyOptions,
}: {
  healthInsurance: any | null;
  // answerList: any | null;
  companyOptions: SelectOptionProps[];
}) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // const isEditing = !!healthInsurance;
  const removeHealthInsuranceModal = useDisclosure();
  const [, setFilesAvailable] = useState<boolean>(false);
  const navigate = useNavigate();

  const toast = useToast();
  const { files, uploadFilesHandler, removeFileHandler } = useDragAndDropArea({
    maxFiles: MAX_FILES,
  });
  const [isFormValid, setIsFormValid] = useState(false);
  const [formChanged, setFormChanged] = useState(false);

  const { authenticatedUser } = useAuthService();
  const { addInsurance, updateInsurance, deleteInsurance } = useHealthInsuranceList(authenticatedUser?.id);
  const { id: patientId } = authenticatedUser || {};
  const { trackEventInFlow } = useAnalyticsService();

  const { modalDisclosure, setModalProps } = useModalContext();

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        company: z
          .object({
            label: z.string(),
            value: z.string(),
          })
          .refine((data) => data.label && data.value, {
            message: 'Company is required',
          }),
        policy_number: z.string().min(1, { message: 'Policy number is required' }),
        contact_number: z
          .string()
          .optional()
          .refine((value) => !value || phoneNumberRegex.test(value), {
            message: 'Contact number is invalid',
          }),
      })
    ),
    defaultValues: getInitialData(healthInsurance),
  });
  const {
    handleSubmit,
    // control,
    formState: { isSubmitting, isValid },
    reset,
    register,
    // setError: setFormError,
    // watch,
  } = form;

  const isEditForm = healthInsurance !== null;

  async function onSubmit(values: any) {
    try {
      const uploadedFile = files.length > 0 ? files[0] : null;
      const fileRes = uploadedFile ? await medplumApi.uploadFileHandler(uploadedFile) : {};
      const { data: fileData, config } = fileRes || {};
      const { contentType, id: fileId } = fileData || {};
      const { name: fileName } = uploadedFile || {};
      const { data: configData } = config || {};
      const { size } = configData || {};
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { company, policy_number, contact_number } = values;
      // const { company } = values;

      let payload: any = {
        resourceType: 'Bundle',
        type: 'transaction',
        entry: [
          // Conditionally add DocumentReference entry if uploadedFile is present
          ...(uploadedFile
            ? [
                {
                  fullUrl: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                  resource: {
                    status: 'active',
                    content: [
                      {
                        attachment: {
                          contentType,
                          url: `Binary/${fileId}`,
                          title: fileName,
                          size,
                          id: fileId,
                        },
                      },
                    ],
                    resourceType: DOCUMENT_REF,
                    subject: {
                      reference: `Patient/${patientId}`,
                    },
                  },
                  request: {
                    method: 'POST',
                    url: DOCUMENT_REF,
                  },
                },
              ]
            : []),

          // Always add the QuestionnaireResponse entry
          {
            fullUrl: URN_ADD_HEALTHCARE_INSURANCE_QUESTIONNAIRESPONSE,
            resource: {
              resourceType: 'QuestionnaireResponse',
              status: 'active',
              item: [
                {
                  linkId: 'health-insurance-provider-name',
                  answer: [
                    {
                      valueCoding: {
                        code: company?.value,
                        display: company?.label,
                        system: FH_UI_CODESYSTEM,
                      },
                    },
                  ],
                },
                {
                  linkId: 'health-insurance-policy-number',
                  answer: [
                    {
                      valueString: policy_number,
                    },
                  ],
                },

                // Conditionally add the reference to DocumentReference if uploadedFile is present
                ...(uploadedFile
                  ? [
                      {
                        linkId: 'health-insurance-reference',
                        answer: [
                          {
                            valueReference: {
                              reference: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                            },
                          },
                        ],
                      },
                    ]
                  : []),
              ],
              questionnaire: `${MEDPLUM_QUESTIONNAIRE}/HealthInsurance`,
              subject: {
                reference: `Patient/${patientId}`,
              },
            },
            request: {
              method: 'POST',
              url: 'QuestionnaireResponse',
            },
          },

          // Conditionally add the Linkage entry if uploadedFile is present
          ...(uploadedFile
            ? [
                {
                  resource: {
                    resourceType: 'Linkage',
                    item: [
                      {
                        type: 'source',
                        resource: {
                          reference: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                        },
                      },
                      {
                        type: 'alternate',
                        resource: {
                          reference: URN_ADD_HEALTHCARE_INSURANCE_QUESTIONNAIRESPONSE,
                        },
                      },
                    ],
                  },
                  request: {
                    method: 'POST',
                    url: 'Linkage',
                  },
                },
              ]
            : []),
        ],
      };
      if (contact_number) {
        payload.entry.forEach((el: any) => {
          if (el?.resource?.resourceType === 'QuestionnaireResponse') {
            el?.resource?.item.push({
              linkId: 'health-insurance-contact-number',
              answer: [
                {
                  valueString: contact_number,
                },
              ],
            });
          }
        });
      }

      if (healthInsurance) {
        payload = {
          resourceType: 'Bundle',
          type: 'transaction',
          entry: [
            ...(uploadedFile
              ? [
                  {
                    fullUrl: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                    resource: {
                      status: 'active',
                      content: uploadedFile
                        ? [
                            {
                              attachment: {
                                contentType,
                                url: `Binary/${fileId}`,
                                title: fileName,
                                size,
                                id: fileId,
                              },
                            },
                          ]
                        : [],
                      resourceType: DOCUMENT_REF,
                      subject: {
                        reference: `Patient/${patientId}`,
                      },
                    },
                    request: {
                      method: 'POST',
                      url: DOCUMENT_REF,
                    },
                  },
                ]
              : []),
            {
              resource: {
                resourceType: 'QuestionnaireResponse',
                status: 'active',
                item: [
                  {
                    linkId: 'health-insurance-provider-name',
                    answer: [
                      {
                        valueCoding: {
                          code: company?.value,
                          display: company?.label,
                          system: FH_UI_CODESYSTEM,
                        },
                      },
                    ],
                  },
                  {
                    linkId: 'health-insurance-policy-number',
                    answer: [
                      {
                        valueString: policy_number,
                      },
                    ],
                  },

                  ...(uploadedFile
                    ? [
                        {
                          linkId: 'health-insurance-reference',
                          answer: [
                            {
                              valueReference: {
                                reference: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                              },
                            },
                          ],
                        },
                      ]
                    : []),
                ],
                questionnaire: `${MEDPLUM_QUESTIONNAIRE}/HealthInsurance`,
                subject: {
                  reference: `Patient/${patientId}`,
                },
                id: healthInsurance?.id,
              },
              request: {
                method: 'PUT',
                url: `QuestionnaireResponse/${healthInsurance?.id}`,
              },
            },
            ...(uploadedFile
              ? [
                  {
                    resource: {
                      resourceType: 'Linkage',
                      item: [
                        {
                          type: 'source',
                          resource: {
                            reference: URN_ADD_HEALTHCARE_INSURANCE_FILE,
                          },
                        },
                        {
                          type: 'alternate',
                          resource: {
                            reference: URN_ADD_HEALTHCARE_INSURANCE_QUESTIONNAIRESPONSE,
                          },
                        },
                      ],
                    },
                    request: {
                      method: 'POST',
                      url: 'Linkage',
                    },
                  },
                ]
              : []),
          ],
        };
        if (contact_number) {
          payload.entry.forEach((el: any) => {
            if (el?.resource?.resourceType === 'QuestionnaireResponse') {
              el?.resource?.item.push({
                linkId: 'health-insurance-contact-number',
                answer: [
                  {
                    valueString: contact_number,
                  },
                ],
              });
            }
          });
        }

        await updateInsurance(payload);
        toast({
          title: 'Successfully edited the health insurance',
          status: 'success',
          duration: 4000,
          isClosable: true,
        });
      } else {
        // Create Health Insurance
        await addInsurance(payload);
      }
      recordHealthInsuranceEvents(trackEventInFlow, {
        EventName: healthInsurance ? 'HealthInsuranceEditCompleted' : 'HealthInsuranceAddCompleted',
        hi_insurance_number: true,
        hi_mobile_number: !!contact_number.length,
        hi_policy_provider: company?.value,
        hi_record_added: !!uploadedFile,
      });
    } catch (error) {
      toast({
        title: (error as any).message ?? 'Something went wrong!',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
    if (!isEditForm) reset();
    navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
    modalDisclosure.onClose();
  }

  const onRemoveInsurance = async () => {
    const identifier = 'urn:fh-workflow:task:delete:health-insurance';
    if (healthInsurance?.id) {
      setIsLoading(true);
      // const { item: items } = healthInsurance || {};
      // const fileReferenceData: any = items?.find((item: any) => {
      //   return item.linkId === 'health-insurance-reference';
      // });
      // const documentReference = fileReferenceData?.answer[0]?.valueReference?.reference;
      const healthInsuranceResponseUpdate = Buffer.from(
        JSON.stringify([
          {
            op: 'add',
            path: '/meta/tag',
            value: [
              {
                system: FH_UI_CODESYSTEM,
                code: 'delete',
                display: 'Marked for deletion',
              },
            ],
          },
        ])
      ).toString('base64');
      const payload = {
        resourceType: 'Bundle',
        type: 'transaction',
        entry: [
          {
            fullUrl: DELETE_QUESTIONNAIRE_TASK,
            resource: {
              resourceType: 'Task',
              status: 'requested',
              intent: 'option',
              priority: 'routine',
              identifier: [
                {
                  value: identifier,
                },
              ],
              input: [
                {
                  type: {
                    coding: [
                      {
                        code: identifier,
                      },
                    ],
                  },
                  valueReference: {
                    reference: `QuestionnaireResponse/${healthInsurance?.id}`,
                  },
                },
              ],
            },
            request: {
              method: 'POST',
              url: 'Task',
            },
          },
          {
            resource: {
              resourceType: 'Binary',
              contentType: 'application/json-patch+json',
              data: `{{${healthInsuranceResponseUpdate}}}`,
            },
            request: {
              method: 'PATCH',
              url: `QuestionnaireResponse/${healthInsurance?.id}`,
            },
          },
        ],
      };

      await deleteInsurance(payload);
      recordHealthInsuranceEvents(trackEventInFlow, {
        EventName: 'HealthInsuranceRemoved',
      });
      setIsLoading(false);
      modalDisclosure.onClose();
      setModalProps((prevState) => ({ ...prevState, isPrimaryButtonLoading: false }));

      toast({
        title: 'Record successfully deleted!',
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    setModalProps((prevState: any) => ({
      ...prevState,
      primaryButtonEnabled: isValid && isFormValid && (formChanged || !isEditForm),
      isPrimaryButtonLoading: isSubmitting || isLoading,
      onPrimaryButtonClick: handleSubmit(onSubmit),
      onTertiaryButtonClick: removeHealthInsuranceModal.onOpen,
    }));
  }, [isEditForm, isValid, isSubmitting, isLoading, handleSubmit, files, formChanged, isFormValid]);

  async function createFile(fileValues: any) {
    const { content } = fileValues || {};
    const { attachment } = content[0] || {};

    const { url, title } = attachment || {};
    const response = await fetch(url, { mode: 'no-cors' });
    const data = await response.blob();
    const metaData = {
      type: 'application/pdf',
      // size,
    };
    const file = new File([data], title, metaData);
    return file;
  }

  const { file_reference: fileValues } = form.formState.defaultValues || {};

  useEffect(() => {
    async function fetchFiles() {
      if (fileValues) {
        const response = await createFile(fileValues);
        setFilesAvailable(true);
        // setFiles([response]);
        uploadFilesHandler([response]);
      }
    }
    fetchFiles();
  }, [fileValues]);
  const { content } = (fileValues && fileValues) || {};
  const { attachment } = (content && content[0]) || {};
  const { size } = attachment || {};
  useEffect(() => {
    const subscription = form.watch((value, { type }) => {
      if (type === 'change') {
        setFormChanged(true);
        setIsFormValid(true);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);
  return (
    <FormProvider {...form}>
      <ConsentModal {...removeHealthInsuranceModal}>
        <ConsentModalHeading>Are you sure you want to remove this entry?</ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            isLoading={isLoading}
            onClick={onRemoveInsurance}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={removeHealthInsuranceModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Flex
        direction="column"
        gap="34px"
        pt="24px"
      >
        <CompanySelect
          company={healthInsurance}
          companyOptions={companyOptions}
          healthInsurance={healthInsurance}
          setIsFormValid={setIsFormValid}
          setFormChanged={setFormChanged}
        />
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.policy_number}
        >
          <Input
            id="policy_number"
            placeholder=" "
            {...register('policy_number')}
            onBlurCapture={(e) => {
              setIsFormValid(true);

              if (!healthInsurance) {
                recordHealthInsuranceEvents(trackEventInFlow, {
                  EventName: 'HealthInsuranceInProgPolicyNumber',
                  hi_insurance_number: !!e.target.value.length,
                });
              }
            }}
          />
          <FormLabel>Policy number*</FormLabel>
          <FormErrorMessage>{String(form.formState.errors.policy_number?.message)}</FormErrorMessage>
        </FormControl>
        <FormControl
          variant="floatingTel"
          isInvalid={!!form.formState.errors.contact_number}
        >
          <Controller
            name="contact_number"
            control={form.control}
            render={({ field }) => (
              <InputGroup className={field.value ? 'has__value' : ''}>
                <InputLeftElement width="auto">IN +91</InputLeftElement>
                <Input
                  placeholder=" "
                  onKeyDown={handleKeyPressNumbers}
                  {...register('contact_number', { minLength: 6 })}
                  onBlurCapture={(e) => {
                    if (!healthInsurance) {
                      recordHealthInsuranceEvents(trackEventInFlow, {
                        EventName: 'HealthInsuranceInProgMobileNumber',
                        hi_mobile_number: !!e.target.value.length,
                      });
                    }
                    setIsFormValid(true);
                  }}
                />
                <FormLabel>Contact number</FormLabel>
              </InputGroup>
            )}
          />
          <FormErrorMessage>{String(form.formState.errors.contact_number?.message)}</FormErrorMessage>
        </FormControl>
        {files && files.length > 0 ? (
          <Flex
            direction="column"
            gap="12px"
          >
            {files.map((file: any) => (
              <DragAndDropFilePreview
                key={file.name}
                file={file}
                size={size}
                onRemove={(e) => {
                  if (!healthInsurance) {
                    recordHealthInsuranceEvents(trackEventInFlow, {
                      EventName: 'HealthInsuranceRecordAdded',
                      hi_record_added: false,
                    });
                  }
                  removeFileHandler(e);
                  setFormChanged(true);
                  setIsFormValid(true);
                }}
              />
            ))}
          </Flex>
        ) : (
          <Box
            mt="12px"
            mb={files.length > 0 ? '40px' : '20px'}
          >
            <Text
              color="gray.200"
              mb="4px"
            >
              {files.length === 0 ? 'Upload file (optional)' : 'File'}
            </Text>
            {files.length === 0 && (
              <DragAndDropArea
                acceptedFileTypes={ACCEPTED_FILE_TYPES}
                onFilesDrop={(e) => {
                  if (!healthInsurance) {
                    recordHealthInsuranceEvents(trackEventInFlow, {
                      EventName: 'HealthInsuranceRecordAdded',
                      hi_record_added: true,
                    });
                  }
                  uploadFilesHandler(e);
                  setFormChanged(true);
                  setIsFormValid(true);
                }}
                disabled={files.length >= MAX_FILES}
                maxFiles={MAX_FILES}
                maxSize={MAX_SIZE}
              >
                {(props) => (
                  <DragPrompt
                    variant={DRAG_AND_DROP_AREA_VARIANT.VERTICAL}
                    height="200px"
                    alertPrompt={
                      <DragAndDropAlertPrompt
                        m="24px"
                        mb="-14px"
                      >
                        PDF file that do not exceed 25 MB.
                      </DragAndDropAlertPrompt>
                    }
                    {...props}
                  />
                )}
              </DragAndDropArea>
            )}
          </Box>
        )}
      </Flex>
    </FormProvider>
  );
}
