// Package modules
import { <PERSON><PERSON>, Card, CardBody, Flex, Heading, IconButton, Text } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import { FileText as FileIcon, Edit3 as PenIcon } from 'react-feather';
// Local modules
import { useIsDesktop } from '@components/ui/hooks/device.hook';
import { recordHealthInsuranceEvents } from '@user/lib/events-analytics-manager';
import { useNavigate, useParams } from 'react-router-dom';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { Modal, ModalProvider, useModal } from 'src/components/Modal';
import GenericProfileCard from '../../components/GenericProfileCard';
// import { HealthInsuranceCompany } from '@lib/models/health-insurance';
import { HealthInsurance } from '../../../lib/models/patient';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import {
  // useHealthInsuranceCompanyList,
  useHealthInsuranceList,
  useMasterQuestionnaireList,
  // useMasterQuestionnaireResponseList,
} from '../../../lib/medplum-state';
import { RemoveButtonLabel } from '../../components/RemoveButtonLabel';
import HealthInsuranceForm from './HealthInsuranceForm';
import { getValueSetByMasterList } from '@lib/utils/utils';
import { FHIR_VALUE_SET_URL, MEDPLUM_QUESTIONNAIRE } from '@lib/constants';

// Helpers
const openFile = (url: string) => {
  window.open(url, '_blank');
};

function HealthInsuranceCard({
  healthInsurances,
  onAddButtonClick,
  onEditButtonClick,
}: {
  healthInsurances: any | null;
  onAddButtonClick: () => void;
  onEditButtonClick: (value: HealthInsurance) => void;
}) {
  const { isPublicMode } = usePublicSettings();
  const isDesktop = useIsDesktop();
  const [hoveredElement, setHoveredElement] = useState<string | null>(null);
  const handleMouseEnter = (contactId: string) => {
    setHoveredElement(contactId);
  };
  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  if (!healthInsurances?.length) {
    return (
      <GenericProfileCard
        title="Health Insurance"
        info="Add information about any health insurance you have."
        actionButtonText="health insurance"
        actionButton={onAddButtonClick}
      />
    );
  }

  return (
    <GenericProfileCard
      title="Health Insurance"
      info="Add information about any health insurance you have."
      actionButton={onAddButtonClick}
    >
      <Flex
        direction="column"
        gap="20px"
      >
        {healthInsurances.map((insurance: any) => {
          const organisation = insurance?.item.find((el: any) => {
            return el.linkId === 'health-insurance-provider-name';
          });
          const contactNumber = insurance?.item.find((el: any) => {
            return el.linkId === 'health-insurance-contact-number';
          });
          const policyNumber = insurance?.item.find((el: any) => {
            return el.linkId === 'health-insurance-policy-number';
          });
          const fileReference = insurance?.item.find((el: any) => {
            return el.linkId === 'health-insurance-reference';
          });
          return (
            <Flex
              bg="white"
              borderRadius="8px"
              key={insurance.id}
              direction={{ base: 'column', md: 'row' }}
              onMouseEnter={() => handleMouseEnter(insurance.id)}
              onMouseLeave={handleMouseLeave}
            >
              <Card
                bgColor="fluentHealthSecondary.500"
                minH={{ base: 'auto', md: '180px' }}
                maxW={{ base: 'auto', md: '225px' }}
                flexBasis="37%"
                role="group"
                boxShadow="none"
                borderRadius="8px"
                cursor={isPublicMode ? 'default' : 'pointer'}
                onMouseEnter={() => handleMouseEnter(insurance.id)}
                {...(isPublicMode ? {} : { onClick: () => onEditButtonClick(insurance) })}
              >
                <CardBody
                  padding="12px 8px 12px 12px"
                  display="flex"
                  flexDirection="column"
                  justifyContent="space-between"
                >
                  <Flex px="2">
                    <Heading
                      fontSize="xl"
                      color="periwinkle.700"
                      lineHeight="7"
                    >
                      {organisation?.answer[0]?.valueCoding?.display
                        ? organisation?.answer[0]?.valueCoding?.display
                        : ''}
                    </Heading>
                  </Flex>

                  <Flex
                    height="28px"
                    px="2"
                    justifyContent="end"
                    alignItems="center"
                    mt={{ base: '1', md: '0' }}
                  >
                    {!isPublicMode && ((isDesktop && hoveredElement === insurance.id) || !isDesktop) && (
                      <IconButton
                        aria-label="Edit emergency contact"
                        variant="ghost"
                        size="sm"
                        pos="absolute"
                        right="22px"
                        bottom="18px"
                        color="fluentHealthSecondary.100"
                        icon={<PenIcon size={16} />}
                      />
                    )}
                  </Flex>
                </CardBody>
              </Card>
              <Flex
                direction="column"
                flexBasis="63%"
                padding={{ base: '16px 20px', md: '16px 16px 16px 24px' }}
                gap="16px"
              >
                <Flex
                  justifyContent="space-between"
                  direction={{ base: 'column', md: 'row' }}
                  gap={{ base: '8px', md: '0' }}
                  paddingBottom={{ base: '2', md: '4' }}
                  borderBottom="1px solid"
                  borderColor="gray.100"
                >
                  <Text color="royalBlue.500">Policy number</Text>
                  <Text color="fluentHealthText.100">
                    {policyNumber?.answer[0]?.valueString ? policyNumber?.answer[0]?.valueString : ''}
                  </Text>
                </Flex>
                <Flex
                  justifyContent="space-between"
                  direction={{ base: 'column', md: 'row' }}
                  gap={{ base: '8px', md: '0' }}
                  paddingBottom={{ base: '2', md: '4' }}
                  borderBottom="1px solid"
                  borderColor="gray.100"
                >
                  <Text color="royalBlue.500">Contact number</Text>
                  <Text color="fluentHealthText.100">
                    {contactNumber?.answer[0]?.valueString ? contactNumber?.answer[0]?.valueString : ''}
                  </Text>
                </Flex>
                <Flex
                  justifyContent="space-between"
                  alignItems="center"
                  direction={{ base: 'column', md: 'row' }}
                >
                  <Text color="royalBlue.500">Files</Text>
                  {fileReference?.answer?.[0]?.valueReference?.resource?.content?.[0]?.attachment?.title ? (
                    <Button
                      leftIcon={<FileIcon size={15} />}
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        openFile(fileReference?.answer?.[0]?.valueReference?.resource?.content[0]?.attachment?.url)
                      }
                    >
                      <Text
                        color="fluentHealthText.100"
                        maxW="150px"
                        overflow="hidden"
                        textOverflow="ellipsis"
                        whiteSpace="nowrap"
                      >
                        {fileReference?.answer?.[0]?.valueReference?.resource?.content[0]?.attachment?.title}
                      </Text>
                    </Button>
                  ) : (
                    <Text color="fluentHealthText.100">N/A</Text>
                  )}
                </Flex>
              </Flex>
            </Flex>
          );
        })}
      </Flex>
    </GenericProfileCard>
  );
}

export function HealthInsuranceSection() {
  const [currentHealthInsurance, setCurrentHealthInsurance] = useState<any | null>(null);
  const healthInsuranceModal = useModal();
  const navigate = useNavigate();
  const params = useParams();
  const { ehrId, action } = params as any;
  const { PROFILE, EHR, HEALTH_INSURANCE } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const { authenticatedUser } = useAuthService();
  const { insuranceList } = useHealthInsuranceList(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();
  const { QuestionnaireResponseList: questionnaireResponse } = insuranceList || {};

  const { masterList } = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/HealthInsurance`);

  const companyOptions: any = getValueSetByMasterList(masterList, `${FHIR_VALUE_SET_URL}/HealthInsuranceCompanies`);
  const onAddButtonClick = () => {
    setCurrentHealthInsurance(null);
    if (NavigationHelper.isDesktop()) {
      navigate(`/${PROFILE}/${EHR}/${HEALTH_INSURANCE}/${ADD}`);
    } else {
      healthInsuranceModal.modalDisclosure.onOpen();
    }
    recordHealthInsuranceEvents(trackEventInFlow, {
      EventName: 'HealthInsuranceAddStarted',
    });
  };

  const onEditButtonClick = (healthInsurance: HealthInsurance) => {
    setCurrentHealthInsurance(healthInsurance);
    healthInsuranceModal.modalDisclosure.onOpen();
    recordHealthInsuranceEvents(trackEventInFlow, {
      EventName: 'HealthInsuranceEditStarted',
      hi_edited_fields: [
        `hi_policy_provider:${healthInsurance?.item?.[0]?.answer?.[0]?.valueCoding?.code ?? ''}`,
        `hi_insurance_number:${healthInsurance?.item?.[1]?.answer?.[0]?.valueString ? 'true' : 'false'}`,
        `hi_mobile_number:${!!healthInsurance?.item?.[2]?.answer?.[0]?.valueString?.length}`,
      ],
    });
  };

  if (insuranceList?.length === 0) {
    return null;
  }
  useEffect(() => {
    if (action === ADD && ehrId === HEALTH_INSURANCE) {
      healthInsuranceModal.modalDisclosure.onOpen();
    }
  }, [action]);

  return (
    <>
      <HealthInsuranceCard
        healthInsurances={questionnaireResponse}
        onAddButtonClick={onAddButtonClick}
        onEditButtonClick={onEditButtonClick}
      />
      <ModalProvider {...healthInsuranceModal}>
        <Modal
          title="Health Insurance"
          primaryButtonLabel={currentHealthInsurance ? 'Save' : 'Add'}
          showSecondaryButton={false}
          tertiaryButtonLabel={<RemoveButtonLabel />}
          tertiaryButtonVariant="quiet"
          showTertiaryButton={!!currentHealthInsurance}
          isCentered
          primaryButtonEnabled
          {...healthInsuranceModal.modalProps}
          {...healthInsuranceModal.modalDisclosure}
          onClose={() => {
            healthInsuranceModal.modalDisclosure.onClose();
            setCurrentHealthInsurance(null);
            if (action === ADD) navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
          }}
        >
          <HealthInsuranceForm
            healthInsurance={currentHealthInsurance}
            // answerList={insuranceList}
            companyOptions={companyOptions}
            // name="HealthInsurance"
          />
        </Modal>
      </ModalProvider>
    </>
  );
}
