// Package modules
import { PropsWithChildren } from 'react';
import { ChakraProps, Grid, GridItem, Text } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { sanitizePhoneNumber } from '@utils/utils';

import { Patient, PatientTelecom } from '@lib/models/patient';
import { Modal, ModalProvider, useModal } from '../../../../../components/Modal';
import GenericProfileCard from '../../components/GenericProfileCard';
import { usePublicSettings } from '@lib/state';
import { usePatient } from '../../../lib/medplum-state';
import { GeneralInfoForm } from './GeneralInfoForm';
import { parsePatientName, truncateName } from '@lib/utils/utils';

type ChakraPropsCustom = ChakraProps & {
  noBorder?: boolean;
};
function GenericProfileGridItem(props: PropsWithChildren<ChakraPropsCustom>) {
  return (
    <GridItem
      minH={{ base: '24px', md: '48px' }}
      borderBottomWidth={props?.noBorder ? { base: '0', md: '0' } : { base: '0', md: '1px' }}
      borderBottomColor={{ base: 'transparent', md: 'fluentHealthText.500' }}
      textAlign="left"
      display="flex"
      alignItems="center"
      {...props}
      sx={{
        '@media screen and (max-width: 48em)': {
          '&:nth-child(even):not(:last-child)': {
            borderBottomWidth: '1px',
            borderBottomColor: 'fluentHealthText.500',
            paddingBottom: '8px',
            marginBottom: '8px',
          },
        },
      }}
    />
  );
}

export function GeneralInfoCard({
  patient,
  onEditButtonClick,
}: {
  patient: Patient;
  onEditButtonClick: (value: Patient) => void;
}) {
  const { isPublicMode, myConsent } = usePublicSettings();
  const isLastNameShared: boolean =
    !isPublicMode || (myConsent && myConsent.find((val: any) => val.meaning === 'lastName'));

  const patientName = parsePatientName(patient?.name);
  const patientFirstName = patientName.split(' ')[0];
  const patientLastName = isLastNameShared ? patientName.split(' ')[1] : '';
  const patientEmail = patient?.telecom
    ? patient.telecom.find((telecom: PatientTelecom) => telecom.system === 'email')?.value
    : 'No email';
  const patientAlternateEmail = patient?.telecom
    ? patient.telecom.find((telecom: PatientTelecom) => telecom.system === 'email' && telecom.use === 'home')?.value
    : '-';
  const patientBirthday = patient?.birthDate || '';

  const patientPhone = sanitizePhoneNumber(
    patient?.telecom?.find((telecom: PatientTelecom) => telecom.system === 'phone' && telecom.use === 'mobile')?.value
  );
  const patientAlternatePhone = patient?.telecom
    ? patient.telecom.find((telecom: PatientTelecom) => telecom.system === 'phone' && telecom.use === 'home')?.value
    : '-';

  const formatDate = (date: string) => {
    return dayjs(date).isValid() ? dayjs(date).format('DD/MM/YYYY') : '';
  };

  return (
    <GenericProfileCard
      title="GENERAL"
      actionButton={() => onEditButtonClick(patient)}
      actionButtonTextPrefix="Edit"
      showActionButtonIcon={false}
    >
      <Grid
        templateColumns={{ base: '1fr', md: '1fr 1fr' }}
        templateRows={{ base: 'auto', md: '1fr' }}
        flexDirection={{ base: 'column', md: 'row' }}
        gap={{ base: '8px', md: '0' }}
      >
        <GenericProfileGridItem>
          <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>First name</Text>
        </GenericProfileGridItem>
        <GenericProfileGridItem>
          <Text color="fluentHealthText.100">{patientFirstName || 'No Name'}</Text>
        </GenericProfileGridItem>
        {patientLastName && (
          <>
            <GenericProfileGridItem>
              <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Last name</Text>
            </GenericProfileGridItem>
            <GenericProfileGridItem>
              <Text color="fluentHealthText.100">{patientLastName || 'No Name'}</Text>
            </GenericProfileGridItem>
          </>
        )}
        <GenericProfileGridItem>
          <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Date of birth (DD/MM/YYYY)</Text>
        </GenericProfileGridItem>
        <GenericProfileGridItem>
          <Text mr={2}>{formatDate(patientBirthday)}</Text>
        </GenericProfileGridItem>
        {!isPublicMode && (
          <>
            <GenericProfileGridItem>
              <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Mobile number</Text>
            </GenericProfileGridItem>
            <GenericProfileGridItem>
              <Text color="fluentHealthText.100">{patientPhone ? `+91 ${patientPhone}` : '-'}</Text>
            </GenericProfileGridItem>
          </>
        )}
        {!isPublicMode && (
          <>
            <GenericProfileGridItem>
              <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Email address</Text>
            </GenericProfileGridItem>
            <GenericProfileGridItem>
              <Text color="fluentHealthText.100">{patientEmail ? truncateName(patientEmail, 30) : '-'}</Text>
            </GenericProfileGridItem>
          </>
        )}
        {!isPublicMode && (
          <>
            <GenericProfileGridItem>
              <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Alternate mobile number</Text>
            </GenericProfileGridItem>
            <GenericProfileGridItem>
              <Text color="fluentHealthText.100">
                {patientAlternatePhone && patientAlternatePhone?.length ? `+91 ${patientAlternatePhone}` : '-'}
              </Text>
            </GenericProfileGridItem>
          </>
        )}
        {!isPublicMode && (
          <>
            <GenericProfileGridItem>
              <Text color={{ base: 'gray.300', md: 'royalBlue.500' }}>Alternate email address</Text>
            </GenericProfileGridItem>
            <GenericProfileGridItem>
              <Text color="fluentHealthText.100">
                {patientAlternateEmail ? truncateName(patientAlternateEmail, 30) : '-'}
              </Text>
            </GenericProfileGridItem>
          </>
        )}
      </Grid>
    </GenericProfileCard>
  );
}

export function GeneralInfoSection() {
  const patientInfoModal = useModal();

  const { patient, updatePatientSettings } = usePatient();

  const onEditButtonClick = () => {
    patientInfoModal.modalDisclosure.onOpen();
  };

  return (
    <>
      <GeneralInfoCard
        patient={patient}
        onEditButtonClick={onEditButtonClick}
      />
      <ModalProvider {...patientInfoModal}>
        <Modal
          title="Basic Info"
          primaryButtonLabel="Save"
          showSecondaryButton={false}
          isCentered
          {...patientInfoModal.modalProps}
          {...patientInfoModal.modalDisclosure}
        >
          <GeneralInfoForm
            patient={patient}
            updatePatient={updatePatientSettings}
          />
        </Modal>
      </ModalProvider>
    </>
  );
}
