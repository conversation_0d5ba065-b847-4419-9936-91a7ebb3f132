// Package modules
import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, Flex, Heading, IconButton, Text } from '@chakra-ui/react';
import { FileText as FileIcon, Edit3 as PenIcon } from 'react-feather';
// Local modules
import { recordAdditionalMedicalDecisionMakerEvents } from '@user/lib/events-analytics-manager';
import { useNavigate, useParams } from 'react-router-dom';
import { NavigationHelper, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { Modal, ModalProvider, useModal } from '../../../../../components/Modal';
import GenericProfileCard from '../../components/GenericProfileCard';
import { useHealthcareProxyList } from '../../../lib/medplum-state';
import { HealthcareProxy } from '../../../lib/models/patient';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { RemoveButtonLabel } from '../../components/RemoveButtonLabel';
import HealthcareProxyForm from './HealthcareProxyForm';
import { parsePatientName, parsePhoneNo, parseRelation } from '@lib/utils/utils';
import { useIsDesktop } from 'src/components/ui/hooks/device.hook';
import { RelatedPerson } from 'src/gql/graphql';

const openFile = (url: string) => {
  window.open(url, '_blank');
};

const healthProxyText = {
  title: 'Alternative Medical Decision-Maker',
  info: 'Add the details of the person you have authorised to make medical decisions on your behalf.',
};

function HealthcareProxyCard({
  healthcareProxies,
  onAddButtonClick,
  onEditButtonClick,
}: {
  healthcareProxies: RelatedPerson[] | null;
  onAddButtonClick: () => void;
  onEditButtonClick: (value: HealthcareProxy) => void;
}) {
  const isDesktop = useIsDesktop();
  const [hoveredElement, setHoveredElement] = useState<string | null>(null);
  const handleMouseEnter = (contactId: string) => {
    setHoveredElement(contactId);
  };
  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  const { isPublicMode } = usePublicSettings();

  if (!healthcareProxies?.length) {
    return (
      <GenericProfileCard
        title={healthProxyText.title}
        info={healthProxyText.info}
        actionButtonText="alternative medical decision-maker"
        actionButton={onAddButtonClick}
      />
    );
  }

  return (
    <GenericProfileCard
      title={healthProxyText.title}
      info={healthProxyText.info}
      actionButton={onAddButtonClick}
    >
      <Flex
        direction="column"
        gap="20px"
      >
        {healthcareProxies.map((contact: any) => (
          <Flex
            bg="white"
            borderRadius="8px"
            key={contact.id}
            direction={{ base: 'column', md: 'row' }}
            onMouseEnter={() => handleMouseEnter(contact.id)}
            onMouseLeave={handleMouseLeave}
          >
            <Card
              bgColor="periwinkle.50"
              minH={{ base: 'auto', md: '180px' }}
              maxW={{ base: 'auto', md: '225px' }}
              flexBasis="37%"
              boxShadow="none"
              borderRadius="8px"
              role="group"
              cursor={isPublicMode ? 'default' : 'pointer'}
              {...(isPublicMode ? {} : { onClick: () => onEditButtonClick(contact) })}
            >
              <CardBody
                padding="12px 8px 12px 12px"
                display="flex"
                flexDirection="column"
                justifyContent="space-between"
              >
                <Flex
                  direction="column"
                  px="2"
                  gap="1"
                >
                  <Heading
                    fontSize="xl"
                    color="periwinkle.700"
                    lineHeight="7"
                  >
                    {!isPublicMode ? parsePatientName(contact.name, '') : parseRelation(contact.relationship, '-')}
                  </Heading>
                  {!isPublicMode && <Text color="iris.300">{parseRelation(contact.relationship)}</Text>}
                </Flex>

                <Flex
                  height="28px"
                  px="2"
                  justifyContent="end"
                  alignItems="center"
                  mt={{ base: '1', md: '0' }}
                >
                  {!isPublicMode && ((isDesktop && hoveredElement === contact.id) || !isDesktop) && (
                    <IconButton
                      aria-label="Edit emergency contact"
                      variant="ghost"
                      size="sm"
                      pos="absolute"
                      right="22px"
                      bottom="18px"
                      color="fluentHealthSecondary.100"
                      icon={<PenIcon size={16} />}
                      id={contact.id}
                    />
                  )}
                </Flex>
              </CardBody>
            </Card>
            <Flex
              direction="column"
              flexBasis="63%"
              padding={{ base: '16px 20px', md: '16px 16px 16px 24px' }}
              gap="16px"
            >
              <Flex
                justifyContent="space-between"
                direction={{ base: 'column', md: 'row' }}
                gap={{ base: '8px', md: '0' }}
                paddingBottom={{ base: '2', md: '4' }}
                borderBottom="1px solid"
                borderColor="gray.100"
              >
                <Text color={{ base: 'gray.300', md: 'iris.500' }}>Contact number</Text>
                <Text color="fluentHealthText.100">+91 {parsePhoneNo(contact.telecom)}</Text>
              </Flex>
              <Flex
                justifyContent="space-between"
                alignItems="center"
                direction={{ base: 'column', md: 'row' }}
              >
                <Text color={{ base: 'gray.300', md: 'iris.500' }}>Files</Text>
                {contact?.file[0]?.content[0]?.attachment?.url ? (
                  <Button
                    leftIcon={<FileIcon size={15} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => openFile(contact?.file[0]?.content[0]?.attachment?.url)}
                  >
                    <Text
                      color="fluentHealthText.100"
                      maxW="150px"
                      overflow="hidden"
                      textOverflow="ellipsis"
                      whiteSpace="nowrap"
                    >
                      {contact?.file[0]?.content[0]?.attachment?.title}
                    </Text>
                  </Button>
                ) : (
                  <Text color="fluentHealthText.100">N/A</Text>
                )}
              </Flex>
            </Flex>
          </Flex>
        ))}
      </Flex>
    </GenericProfileCard>
  );
}

export function HealthCareProxySection() {
  const { isPublicMode } = usePublicSettings();
  const navigate = useNavigate();
  const params = useParams();
  const { ehrId, action } = params as any;
  const { PROFILE, EHR, HEALTHCARE_PROXY } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const [currentContact, setCurrentContact] = useState<HealthcareProxy | null>(null);
  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const { healthcareProxyList } = useHealthcareProxyList(authenticatedUser?.id);

  const healthCareProxyModal = useModal();

  const onAddButtonClick = () => {
    setCurrentContact(null);
    if (NavigationHelper.isDesktop()) {
      navigate(`/${PROFILE}/${EHR}/${HEALTHCARE_PROXY}/${ADD}`);
    } else {
      healthCareProxyModal.modalDisclosure.onOpen();
    }
    recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, { EventName: 'AMDMAddStarted' });
  };

  const onEditButtonClick = (contact: HealthcareProxy) => {
    setCurrentContact(contact ?? null);
    healthCareProxyModal.modalDisclosure.onOpen();
    recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
      EventName: 'AMDMEditStarted',
    });
  };

  if (isPublicMode && healthcareProxyList.length === 0) {
    return null;
  }

  useEffect(() => {
    if (action === ADD && ehrId === HEALTHCARE_PROXY) {
      healthCareProxyModal.modalDisclosure.onOpen();
    }
  }, [action]);

  return (
    <>
      <HealthcareProxyCard
        healthcareProxies={healthcareProxyList}
        onAddButtonClick={onAddButtonClick}
        onEditButtonClick={onEditButtonClick}
      />
      <ModalProvider {...healthCareProxyModal}>
        <Modal
          title="Alternative Medical Decision-Maker"
          primaryButtonLabel={currentContact ? 'Save' : 'Add'}
          showSecondaryButton={false}
          tertiaryButtonLabel={<RemoveButtonLabel />}
          tertiaryButtonVariant="quiet"
          showTertiaryButton={!!currentContact}
          isCentered
          {...healthCareProxyModal.modalProps}
          {...healthCareProxyModal.modalDisclosure}
          onClose={() => {
            healthCareProxyModal.modalDisclosure.onClose();
            setCurrentContact(null);
            if (action === ADD) navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
          }}
        >
          <HealthcareProxyForm contact={currentContact} />
        </Modal>
      </ModalProvider>
    </>
  );
}
