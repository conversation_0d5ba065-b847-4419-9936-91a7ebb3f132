// Package modules
import React, { useC<PERSON>back, useEffect, useState } from 'react';
import { Button, Grid, GridItem, HStack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { Trash as TrashIcon } from 'react-feather';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
// Local modules
import { medplumApi } from '@user/lib/medplum-api';

import { Select, SelectOptionProps } from '../../../../../components/ui/Select';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { parseRelation } from '@lib/utils/utils';
import {
  AnalyticsEventName,
  AnalyticsFlow,
  AnalyticsService,
  EventPropsNames,
  PatientPropsNames,
} from '@lib/analyticsService';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';
import { RelatedPerson } from 'src/gql/graphql';
import { FACT_CODE_SYSTEM } from '@lib/constants';
// Constants
type FamilyMemberFormType = {
  relation: string;
};

// Helpers
const getInitialFormData = (familyMember: any) => {
  const payload = {
    relation: parseRelation(familyMember?.relationship) ?? '',
  };
  return payload;
};

export function AddFamilyMemberForm({
  familyMember,
  closeDialog,
}: {
  familyMember: RelatedPerson | null;
  closeDialog: (updatesMade?: boolean) => void;
}) {
  // Add/edit form type
  const isEditForm = familyMember !== null;
  const { isPublicMode } = usePublicSettings();

  // Determine copies based on form type
  const buttonText = isEditForm ? 'Save changes' : 'Add family member';

  const toast = useToast();
  const [, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false); // Used for contact remove -- cannot bind to form's isLoading
  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const [relationshipGrp, setRelationshipGrp] = useState<SelectOptionProps[]>([]);
  useEffect(() => {
    Promise.all([medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.RELATIONSHIP)]).then(
      ([relationship]) => {
        setRelationshipGrp(
          relationship.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }))
        );
      }
    );
  }, []);
  const form = useForm({
    mode: 'all',
    resolver: zodResolver(
      z.object({
        relation: z.string().min(1),
      })
    ),
    defaultValues: getInitialFormData(familyMember),
  });
  const {
    handleSubmit,
    formState: { isSubmitting, isValid },
    reset,
    setError: setFormError,
  } = form;

  const relationField = form.watch('relation');
  const removeMemberModal = useDisclosure();

  async function onRemove() {
    try {
      setIsLoading(true);
      removeMemberModal.onOpen();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  async function onSubmit(data: FamilyMemberFormType) {
    try {
      if (isEditForm) {
        // await updatePerson({ personId: familyMember!.id, payload });
        trackEventInFlow(AnalyticsFlow.ManageFamilyMember, AnalyticsEventName.ManageFamilyMemberCompleted, {
          [EventPropsNames.FlowName]: 'Family History',
          [EventPropsNames.MemberID]: familyMember?.id,
          [EventPropsNames.Relation]: familyMember?.relationship,
        });
      } else {
        // const newPerson = await addPerson(payload);
        trackEventInFlow(AnalyticsFlow.ManageFamilyMember, AnalyticsEventName.ManageFamilyMemberCompleted, {
          [EventPropsNames.FlowName]: 'Family History',
          [EventPropsNames.Relation]: data.relation,
        });
      }

      AnalyticsService.instance.identifyUser(authenticatedUser, {
        [PatientPropsNames.HasFamilyMembers]: true,
      });
      toast({
        title: `Successfully ${isEditForm ? 'edit' : 'add'}ed the family member`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      // Clear form if "add" mode
      if (!isEditForm) reset();

      closeDialog(true);
    } catch (err) {
      setError(err);
    }
  }

  // Used in formatting error messages
  function onFormError(data: any) {
    if (data.relation) {
      setFormError('relation', { message: 'No relation selected.' });
    }
  }

  async function onRemoveFamilyMember() {
    try {
      setIsLoading(true);

      // await deletePerson(familyMember!.id);
      toast({
        title: 'Successfully removed the family member',
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      removeMemberModal.onClose();
      closeDialog(true);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  const customRelationSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('relation', value?.value);
    form.trigger('relation');
  }, []);

  return (
    <FormProvider {...form}>
      <ConsentModal {...removeMemberModal}>
        <ConsentModalHeading>Are you sure you want to remove this entry?</ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            isLoading={isLoading}
            onClick={onRemoveFamilyMember}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={removeMemberModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Grid
        height="250px"
        gap="12"
        py="4"
      >
        <GridItem>
          <Select
            labelText="How are they related to you?*"
            value={relationshipGrp.find((item: any) => item.value === relationField)}
            onChange={customRelationSelect}
            options={relationshipGrp}
            isSearchable={false}
            isClearable
          />
        </GridItem>
      </Grid>
      <HStack
        flex="row"
        justifyContent="space-between"
        alignItems="flex-end"
        mt="10"
      >
        {isEditForm && (
          <Button
            variant="quiet"
            opacity="0.6"
            color="fluentHealthComplementary.Red"
            _hover={{
              opacity: '1',
            }}
            leftIcon={<TrashIcon size={16} />}
            onClick={onRemove}
          >
            <Text
              height="5"
              lineHeight="6"
            >
              Remove
            </Text>
          </Button>
        )}
        <Button
          px="20px"
          ml="auto"
          isLoading={isSubmitting || isLoading}
          isDisabled={!isValid}
          onClick={handleSubmit(onSubmit, onFormError)}
          type="submit"
          flexShrink={0}
        >
          {buttonText}
        </Button>
      </HStack>
    </FormProvider>
  );
}
