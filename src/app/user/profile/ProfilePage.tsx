/* eslint-disable no-nested-ternary */
// Package modules
import React, { Suspense, useEffect, useState } from 'react';
import { Box, Container, Flex, useTheme } from '@chakra-ui/react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
// Local modules
// import ProfileSidebarSymptomsCard from './components/ProfileSidebarSymptomsCard';
// import { validate } from 'graphql';
import { recordProfileEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import ProfileSidebarCTAs from './components/ProfileSidebarCTAs';
import { AvatarBioCard, ProgressDisclaimer } from './components/AvatarBioCard';
import { ProfileSharingFlow } from './components/ProfileSharingFlow';
import { ProfileBioCard, ProfileBioCardSkeleton } from './components/ProfileBioCard';
import { BigCircleDecoration, HalfCircleWaveDecoration } from 'src/components/ui/Decorations';
import { ProfilePageSkeleton, ProfilePhotoSkeleton } from './components/ProfilePageSkeleton';
import { TabLink } from 'src/components/ui/Tab';
import { hexOpacity } from 'src/components/theme/utils';
import { useAnalyticsService, usePublicSettings } from '@lib/state';
// import { PUBLIC_URL_PREFIX } from '@lib/constants';
import { useIsTablet, useIsTabletReactive } from 'src/components/ui/hooks/device.hook';
import { BasicInfoPage } from './basic-info/BasicInfoPage';
import { FamilyHistoryPage } from './family-history/FamilyHistoryPage';
import { HealthProfilePage } from './health-profile/HealthProfilePage';
import { CareTeamPage } from './care-team/CareTeamPage';

// Constants
const { PROFILE, EHR, FAMILY_HISTORY, BASIC, CARE_TEAM } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;
const PROFILE_TAB_LINKS: Record<string, string> = {
  BASIC_INFO: `/${PROFILE}/${BASIC}/${VIEW}`,
  FAMILY_HISTORY: `/${PROFILE}/${FAMILY_HISTORY}/${VIEW}`,
  HEALTH_PROFILE: `/${PROFILE}/${EHR}/${VIEW}`,
  CARE_TEAM: `/${PROFILE}/${CARE_TEAM}`,
};

// TODO: Use enum instead of hardcoded tab name

function MobileProfilePage({
  setSelectedTab,
  selectedTab = 0,
}: {
  selectedTab: number;
  setSelectedTab: (val: number) => void;
}) {
  const { isPublicMode } = usePublicSettings();
  // const isCareTeamTab = !isPublicMode;
  const { trackEventInFlow } = useAnalyticsService();
  // const resolvedTabLinks = getResolvedTabLinks(isPublicMode);
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const isTablet = useIsTabletReactive();

  useEffect(() => {
    if ((!isTablet && pathname === PROFILE_TAB_LINKS.HEALTH_PROFILE) || pathname === `/${PROFILE}`) {
      navigate(`/${PROFILE}/${BASIC}/${VIEW}`);
    }
  }, [isTablet]);

  return (
    <Flex
      direction="column"
      pb="100px"
    >
      <Box px="16px">
        <Box
          position="relative"
          bgColor="fluentHealth.500"
          bgImage="/mobile-background-profile-cover.png"
          bgPos="center"
          mt="5"
          borderRadius="4xl"
          w="100%"
          h="332px"
        >
          <AvatarBioCard />
          <ProfileSharingFlow />
        </Box>
        <ProgressDisclaimer />
      </Box>

      <Flex
        direction="column"
        w="100%"
        mt="6"
      >
        <Flex
          px="16px"
          gap="8px"
          overflowX="auto"
          className="hide-scrollbar"
        >
          <TabLink
            height="48px"
            to={!isPublicMode ? PROFILE_TAB_LINKS.BASIC_INFO : ''}
            onClick={() => {
              setSelectedTab(0);
              recordProfileEvents(trackEventInFlow, {
                EventName: 'MyProfileTabInteracted',
                pi_tab_name: 'Basic info',
              });
            }}
            isActive={isPublicMode ? selectedTab === 0 : pathname.includes(PROFILE_TAB_LINKS.BASIC_INFO)}
          >
            Basic Info
          </TabLink>
          <TabLink
            height="48px"
            to={!isPublicMode ? PROFILE_TAB_LINKS.HEALTH_PROFILE : ''}
            onClick={() => {
              setSelectedTab(1);
              recordProfileEvents(trackEventInFlow, {
                EventName: 'MyProfileTabInteracted',
                pi_tab_name: 'Health Profile',
              });
            }}
            isActive={isPublicMode ? selectedTab === 1 : pathname.includes(PROFILE_TAB_LINKS.HEALTH_PROFILE)}
          >
            My Health profile
          </TabLink>
          <TabLink
            height="48px"
            to={!isPublicMode ? PROFILE_TAB_LINKS.FAMILY_HISTORY : ''}
            onClick={() => {
              setSelectedTab(2);
              recordProfileEvents(trackEventInFlow, {
                EventName: 'MyProfileTabInteracted',
                pi_tab_name: 'Family History',
              });
            }}
            isActive={isPublicMode ? selectedTab === 2 : pathname.includes(PROFILE_TAB_LINKS.FAMILY_HISTORY)}
          >
            Family History
          </TabLink>
          {/* {isCareTeamTab && (
            <TabLink
              height="48px"
              to={!isPublicMode ? PROFILE_TAB_LINKS.CARE_TEAM : ''}
              onClick={() => {
                setSelectedTab(3);
                recordProfileEvents(trackEventInFlow, {
                  EventName: 'MyProfileTabInteracted',
                  pi_tab_name: "Care Team",
                });
              }}
              isActive={isPublicMode ? selectedTab === 3 : pathname.includes(PROFILE_TAB_LINKS.CARE_TEAM)}
            >
              My Care Team
            </TabLink>
          )} */}
        </Flex>
        <Box
          bgColor="periwinkle.100"
          p={{ base: '16px', md: '20px' }}
          borderRadius="0 0 20px 20px"
        >
          {!isPublicMode ? (
            <Outlet />
          ) : (
            <>
              {selectedTab === 0 && <BasicInfoPage />}
              {selectedTab === 1 && <HealthProfilePage />}
              {selectedTab === 2 && <FamilyHistoryPage />}
              {/* {isCareTeamTab && selectedTab === 3 && <CareTeamPage />} */}
            </>
          )}
        </Box>
        {/* <Box
          mx="16px"
          mt="24px"
        >
          <ProfileSidebarSymptomsCard />
        </Box> */}
      </Flex>
    </Flex>
  );
}

function ProfilePage() {
  const theme = useTheme();
  // const [isPublicMode, setIsPublicMode] = useState(false);
  // const dataSet = usePublicSettings();
  // const navigate = useNavigate();
  // debugger;
  // const [dataVal, setDataVal] = useState(usePublicSettings());
  // useEffect(() => {
  //   const x = dataSet;
  //   setDataVal(x);
  // }, []);
  const { isPublicMode } = { isPublicMode: true };
  const { trackEventInFlow } = useAnalyticsService();
  const isCareTeamTab = !isPublicMode;
  // useEffect(() => {
  //   setIsPublicMode(isPubMode);
  // }, []);
  // const isPublicMode = true;
  // const PROFILE_TresolvedTabLinksAB_LINKS = getResolvedTabLinks(isPublicMode);
  const isTablet = useIsTablet();
  const { pathname } = useLocation();
  const getInitialTab = () => {
    if (pathname.includes(PROFILE_TAB_LINKS.CARE_TEAM)) {
      return 3;
    }
    if (pathname.includes(PROFILE_TAB_LINKS.HEALTH_PROFILE)) {
      return 1;
    }
    if (pathname.includes(PROFILE_TAB_LINKS.FAMILY_HISTORY)) {
      return 2;
    }
    return 0;
  };

  const [selectedTab, setSelectedTab] = useState(getInitialTab());

  useEffect(() => {
    if (pathname === `/${PROFILE}`) {
      console.log(pathname, PROFILE_TAB_LINKS.BASIC_INFO);
      // navigate(PROFILE_TAB_LINKS.BASIC_INFO);
    }
  }, []);

  if (isTablet) {
    return (
      <MobileProfilePage
        selectedTab={selectedTab}
        setSelectedTab={setSelectedTab}
      />
    );
  }

  return (
    <Container
      as={Flex}
      direction="column"
      pb="10"
    >
      <HalfCircleWaveDecoration />
      <BigCircleDecoration />
      <Suspense fallback={<ProfilePageSkeleton />}>
        <Box
          position="relative"
          bgColor="fluentHealth.500"
          bgImage="/background-profile-cover.png"
          bgPos="center"
          mt="5"
          borderRadius="4xl"
          w="100%"
          h="260px"
        >
          <ProfileSharingFlow />
        </Box>
        <Flex
          zIndex={0}
          direction={{ base: 'column', md: 'row' }}
        >
          {/* SIDEBAR */}
          <Flex
            direction="column"
            gap="20px"
            bgColor="periwinkle.100"
            p="12px 16px 16px 16px"
            mx="5"
            mt="-240px"
            borderRadius="2xl"
            border="1px solid"
            borderColor={hexOpacity(theme.colors.iris[500], 0.2)}
            minWidth="400px"
            width="25%"
            height="fit-content"
          >
            <Suspense fallback={<ProfilePhotoSkeleton />}>
              <AvatarBioCard />
            </Suspense>
            <Suspense fallback={<ProfileBioCardSkeleton />}>
              <ProfileBioCard />
            </Suspense>
            <ProfileSidebarCTAs />
            {/* <ProfileSidebarSymptomsCard /> */}
          </Flex>
          <Flex
            direction="column"
            w="100%"
            mt="6"
          >
            <Flex
              px="60px"
              gap="8px"
            >
              <TabLink
                to={!isPublicMode ? PROFILE_TAB_LINKS.BASIC_INFO : ''}
                onClick={() => {
                  setSelectedTab(0);
                  recordProfileEvents(trackEventInFlow, {
                    EventName: 'MyProfileTabInteracted',
                    pi_tab_name: 'Basic Info',
                  });
                }}
                isActive={selectedTab === 0}
              >
                Basic Info
              </TabLink>
              <TabLink
                to={!isPublicMode ? PROFILE_TAB_LINKS.FAMILY_HISTORY : ''}
                onClick={() => {
                  setSelectedTab(2);
                  recordProfileEvents(trackEventInFlow, {
                    EventName: 'MyProfileTabInteracted',
                    pi_tab_name: 'Family History',
                  });
                }}
                isActive={isPublicMode ? selectedTab === 2 : pathname.includes(PROFILE_TAB_LINKS.FAMILY_HISTORY)}
              >
                Family History
              </TabLink>
              {/* {isCareTeamTab && (
                <TabLink
                  to={!isPublicMode ? PROFILE_TAB_LINKS.CARE_TEAM : ''}
                  onClick={() => {
                    setSelectedTab(3);
                    recordProfileEvents(trackEventInFlow, {
                      EventName: 'MyProfileTabInteracted',
                      pi_tab_name: "Care Team",
                    });
                  }}
                  isActive={isPublicMode ? selectedTab === 3 : pathname.includes(PROFILE_TAB_LINKS.CARE_TEAM)}
                >
                  My Care Team
                </TabLink>
              )} */}
            </Flex>
            <Box
              p="20px"
              bgColor="periwinkle.100"
              borderRadius="40px"
            >
              {!isPublicMode ? (
                <Outlet />
              ) : (
                <>
                  {selectedTab === 0 && <BasicInfoPage />}
                  {selectedTab === 2 && <FamilyHistoryPage />}
                  {isCareTeamTab && selectedTab === 3 && <CareTeamPage />}
                </>
              )}
            </Box>
          </Flex>
        </Flex>
      </Suspense>
    </Container>
  );
}

export default ProfilePage;
