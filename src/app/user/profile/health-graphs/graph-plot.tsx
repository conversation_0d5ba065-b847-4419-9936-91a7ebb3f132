import React, { useEffect, useImperativeHandle, useRef } from 'react';
import * as d3 from 'd3';
import { Box } from '@chakra-ui/react';
import html2canvas from 'html2canvas';

export type GraphDataPoint = {
  title: string;
  date: Date;
  value: number;
  unit: string;
  low: number | null;
  high: number | null;
  comparator?: string;
};

type GraphProps = {
  data: GraphDataPoint[];
};
const innerPadding = 5; // Percentage-based padding for the graph (e.g., 5 = 5%)

const GraphPlot = React.forwardRef<{ downloadGraph: () => void }, GraphProps>(({ data }, ref) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!data || data.length === 0 || !chartRef.current) return;

    const margin = { top: 80, right: 30, bottom: 50, left: 50 };
    const width = (chartRef.current?.offsetWidth || 1200) - margin.left - margin.right;
    const height = (width <= 768 ? Math.max(width, 300) : Math.min(width * 0.5, 600)) - margin.top - margin.bottom;

    d3.select(chartRef.current).selectAll('*').remove();
    const svg = d3
      .select(chartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    const xExtent: any = d3.extent(data, (d) => d.date);
    const yMax = d3.max(data, (d) => Math.max(d.value ? d.value * 1.05 : 0, d.high ?? 0)) || 150;
    const yMin =
      d3.min(data, (d) => {
        const values = [d.value !== null ? d.value : Infinity];
        if (d.low !== null) values.push(d.low);
        return d3.min(values);
      }) || 0;
    const xPadding = (xExtent[1].getTime() - xExtent[0].getTime()) * innerPadding * 0.01;
    const yPadding = (yMax - yMin) * innerPadding * 0.01;

    const xScale = d3
      .scaleTime()
      .domain([new Date(xExtent[0].getTime() - xPadding), new Date(xExtent[1].getTime() + xPadding)])
      .range([0, width]);

    const yScale = d3
      .scaleLinear()
      .domain([Math.max(0, yMin - yPadding), yMax + yPadding])
      .nice()
      .range([height, 0]);

    const isInRange = (d: GraphDataPoint) => {
      if (d.low === null || d.high === null || d.value === null) return false;
      return d.value >= d.low && d.value <= d.high;
    };
    const xAxis = d3
      .axisBottom(xScale)
      .tickValues(data.map((d) => d.date))
      .tickFormat(d3.timeFormat('%d %b %Y') as any);

    const yAxis = d3.axisLeft(yScale);
    svg
      .append('g')
      .attr('transform', `translate(0,${height})`)
      .call(xAxis)
      .selectAll('text')
      .attr('transform', 'rotate(-45)')
      .style('text-anchor', 'end');
    svg.append('g').call(yAxis);

    svg
      .append('text')
      .attr('x', width / 2)
      .attr('y', -40)
      .attr('text-anchor', 'middle')
      .attr('font-size', '24px')
      .attr('text-transform', 'capitalize')
      .text(data[0].title);

    data.forEach((d: GraphDataPoint) => {
      if (d.low !== null && d.high !== null) {
        svg
          .append('rect')
          .attr('x', 0)
          .attr('y', yScale(d.high))
          .attr('width', width)
          .attr('height', yScale(d.low) - yScale(d.high))
          .attr('fill', '#289673')
          .attr('opacity', 0);
      }
    });
    svg
      .append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', '#495AE4')
      .attr('stroke-width', 1.5)
      .attr(
        'd',
        d3
          .line<GraphDataPoint>()
          .curve(d3.curveMonotoneX)
          .x((d) => xScale(d.date))
          .y((d) => yScale(d.value ?? 0))
      );
    svg
      .selectAll('circle')
      .data(data)
      .enter()
      .append('circle')
      .attr('cx', (d: GraphDataPoint) => xScale(d.date))
      .attr('cy', (d: GraphDataPoint) => yScale(d.value))
      .attr('r', 4)
      .attr('fill', '#495AE4');

    svg
      .selectAll('.data-label')
      .data(data)
      .enter()
      .append('g')
      .each(function () {
        const group = d3.select(this);

        group
          .append('text')
          .attr('class', 'data-label-shadow')
          .attr('x', (_d: any) => xScale(_d.date) + 3)
          .attr('y', (_d: any) => yScale(_d.value) - 3)
          .attr('text-anchor', 'start')
          .attr('font-size', '15px')
          .attr('font-weight', '700')
          .attr('letter-spacing', '-1.1px')
          .attr('fill', '#fff')
          .attr('dx', -2)
          .attr('dy', 1)
          .text((_d: any) => (_d.value !== undefined ? _d.value?.toFixed(1) : 'N/A'));
        group
          .append('text')
          .attr('class', 'data-label')
          .attr('x', (_d: any) => xScale(_d.date) + 3)
          .attr('y', (_d: any) => yScale(_d.value) - 3)
          .attr('text-anchor', 'start')
          .attr('font-size', '12px')
          .attr('font-weight', '700')
          .attr('fill', '#000')
          .text((_d: any) => (_d.value !== undefined ? _d.value?.toFixed(1) : 'N/A'));
      });

    svg
      .selectAll('.reference-range')
      .data(data)
      .enter()
      .append('line')
      .filter((d: GraphDataPoint) => d.low !== null && d.high !== null && d.value !== null)
      .attr('class', 'reference-range')
      .attr('x1', (d: GraphDataPoint) => xScale(d.date))
      .attr('x2', (d: GraphDataPoint) => xScale(d.date))
      .attr('y1', (d: GraphDataPoint) => yScale(d.value! < d.low! ? d.value! : d.low!))
      .attr('y2', (d: GraphDataPoint) => yScale(d.value! > d.high! ? d.value! : d.high!))
      .attr('stroke', (d) => (isInRange(d) ? '#289673' : '#CF0A0A'))
      .attr('stroke-width', 1)
      .attr('stroke-dasharray', '5 2');

    svg
      .selectAll('.low-end-line')
      .data(data)
      .enter()
      .append('line')
      .filter((d: GraphDataPoint) => d.low !== null)
      .attr('class', 'low-end-line')
      .attr('x1', (d: GraphDataPoint) => xScale(d.date) - 5)
      .attr('x2', (d: GraphDataPoint) => xScale(d.date) + 5)
      .attr('y1', (d: GraphDataPoint) => yScale(d.low!))
      .attr('y2', (d: GraphDataPoint) => yScale(d.low!))
      .attr('stroke', '#289673')
      .attr('stroke-width', 1);

    svg
      .selectAll('.low-end-text')
      .data(data)
      .enter()
      .append('text')
      .filter((d: GraphDataPoint) => d.low !== null)
      .attr('class', 'low-end-text')
      .attr('x', (d: GraphDataPoint) => xScale(d.date) + 8)
      .attr('y', (d: GraphDataPoint) => yScale(d.low!) + 2)
      .attr('text-anchor', 'start')
      .attr('font-size', '8px')
      .attr('fill', '#289673')
      .text((d: GraphDataPoint) => d.low!.toFixed(1));

    svg
      .selectAll('.high-end-line')
      .data(data)
      .enter()
      .append('line')
      .filter((d: GraphDataPoint) => d.high !== null)
      .attr('class', 'high-end-line')
      .attr('x1', (d: GraphDataPoint) => xScale(d.date) - 5)
      .attr('x2', (d: GraphDataPoint) => xScale(d.date) + 5)
      .attr('y1', (d: GraphDataPoint) => yScale(d.high!))
      .attr('y2', (d: GraphDataPoint) => yScale(d.high!))
      .attr('stroke', '#CF0A0A')
      .attr('stroke-width', 1);

    svg
      .selectAll('.high-end-text')
      .data(data)
      .enter()
      .append('text')
      .filter((_d: GraphDataPoint) => _d.high !== null)
      .attr('class', 'high-end-text')
      .attr('x', (_d: GraphDataPoint) => xScale(_d.date) + 8)
      .attr('y', (_d: GraphDataPoint) => yScale(_d.high! - 1))
      .attr('text-anchor', 'start')
      .attr('font-size', '8px')
      .attr('fill', '#CF0A0A')
      .text((d: GraphDataPoint) => d.high!.toFixed(1));
  }, [data]);

  const downloadAsImage = async () => {
    if (!chartRef.current) return;
    const canvas = await html2canvas(chartRef.current);
    const link = document.createElement('a');
    link.href = canvas.toDataURL('image/png');
    link.download = `${data[0].title}.png`;
    link.click();
  };
  useImperativeHandle(ref, () => ({
    downloadGraph: downloadAsImage,
  }));
  return (
    <Box>
      <Box ref={chartRef} />
    </Box>
  );
});

export default React.memo(GraphPlot);
