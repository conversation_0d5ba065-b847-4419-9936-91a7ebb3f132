import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Heading, SimpleGrid } from '@chakra-ui/react';
import { useMasterObservationResponseList, usePatient } from '@user/lib/medplum-state';
import { SearchableSelect } from '@components/ui/Select';
import { DownloadIcon } from '@chakra-ui/icons';

import { useAuthService } from '@lib/state';
import { parsePatientName } from '@lib/utils/utils';
import GraphPlot from './graph-plot';
// import { PeriodSelector } from '@components/PeriodSelector';

const typeOfObservation = [
  {
    label: 'Body Temperature',
    value: 'vi:body-temperature',
    prepareChartData: (_answerList: any, title: string) =>
      _answerList?.map((obs: any) => ({
        title,
        date: new Date(obs.effectiveDateTime),
        value: obs.valueQuantity?.value ?? null,
        unit: obs.valueQuantity?.unit ?? '',
        low: obs.referenceRange?.[0]?.low?.value ?? null,
        high: obs.referenceRange?.[0]?.high?.value ?? null,
      })),
  },
  {
    label: 'Blood Pressure Diastolic',
    value: 'vi:blood-pressure',
    prepareChartData: (_answerList: any, title: string) => {
      const codeToMatch = title === 'Blood Pressure Diastolic' ? '8462-4' : '8480-6';
      return _answerList?.map((obs: any) => ({
        title,
        date: new Date(obs.effectiveDateTime),
        value: obs.component?.find((c: any) => c.code.coding[0].code === codeToMatch)?.valueQuantity?.value ?? null,
        unit: 'mmHg',
        low: obs.referenceRange?.[0]?.low?.value ?? null,
        high: obs.referenceRange?.[0]?.high?.value ?? null,
      }));
    },
  },
  {
    label: 'Blood Pressure Systolic',
    value: 'vi:blood-pressure',
    prepareChartData: (_answerList: any, title: string) => {
      const codeToMatch = title === 'Blood Pressure Diastolic' ? '8462-4' : '8480-6';
      return _answerList?.map((obs: any) => ({
        title,
        date: new Date(obs.effectiveDateTime),
        value: obs.component?.find((c: any) => c.code.coding[0].code === codeToMatch)?.valueQuantity?.value ?? null,
        unit: 'mmHg',
        low: obs.referenceRange?.[0]?.low?.value ?? null,
        high: obs.referenceRange?.[0]?.high?.value ?? null,
      }));
    },
  },
  {
    label: 'Bone Density',
    value: 'vi:bone-density',
    prepareChartData: (_answerList: any, title: string) => [_answerList, title],
  },
  {
    label: 'Basal Metabolic Rate',
    value: 'vi:basal-metabolic-rate',
    prepareChartData: (_answerList: any, title: string) => [_answerList, title],
  },
  {
    label: 'Body Mass Index',
    value: 'vi:body-mass-index',
    prepareChartData: (_answerList: any, title: string) => [_answerList, title],
  },
];

interface GraphFilterProps {
  getData: (value: string) => void;
  defaultData?: string;
  onTriggerDownload: () => void;
}

export function GraphFilter({ getData, defaultData, onTriggerDownload }: GraphFilterProps) {
  const { patient } = usePatient();
  const patientName = { label: parsePatientName(patient?.name), value: patient?.id };
  return (
    <Box
      px={4}
      py={5}
    >
      <Heading
        as="h5"
        size="sm"
      >
        Select user and metric to display graph
      </Heading>
      <SimpleGrid
        minChildWidth={['100%', '20%']}
        gap="30px"
        pt={6}
      >
        <SearchableSelect
          labelText=" "
          defaultValue={patientName}
          options={[patientName]}
          onChange={() => {}}
          isClearable={false}
        />
        <SearchableSelect
          labelText=" "
          defaultValue={defaultData ? typeOfObservation.find((obs) => obs.label === defaultData) : typeOfObservation[0]}
          options={typeOfObservation}
          onChange={(e: any) => {
            getData(e);
          }}
        />
        {/* Future Todo Date Range Selector */}
        {/* <PeriodSelector
          initialDate={[new Date(), new Date()]}
          onChange={(e) => console.info(e)}
          strategy="absolute"
          fullWidth
        /> */}
        <Button
          variant="outline"
          onClick={onTriggerDownload}
          rightIcon={<DownloadIcon />}
        >
          Download
        </Button>
      </SimpleGrid>
    </Box>
  );
}

export function HealthGraphs() {
  const [observationName, setObservationName] = useState('');
  const [obserationData, setObservationData] = useState<any>([]);
  const { authenticatedUser } = useAuthService();
  const { masterVitalList } = useMasterObservationResponseList(authenticatedUser?.id);
  const graphPlotRef = useRef<{ downloadGraph: () => void }>(null);

  const getObservationsForGraph = async (e: any) => {
    setObservationData(masterVitalList[e?.value]);
    setObservationName(e?.label);
  };
  useEffect(() => {
    setObservationData(masterVitalList['vi:body-temperature']);
    setObservationName('Body Temperature');
  }, []);
  const handleDownloadClick = () => {
    if (graphPlotRef.current) {
      graphPlotRef.current.downloadGraph();
    }
  };

  return (
    <Box
      w={1300}
      maxW="100%"
      mx="auto"
      my="auto"
    >
      <Box
        bg="white"
        p={5}
        pt={6}
        borderRadius={40}
      >
        <GraphFilter
          getData={getObservationsForGraph}
          defaultData={observationName}
          onTriggerDownload={handleDownloadClick}
        />
        {observationName && obserationData?.length ? (
          <GraphPlot
            ref={graphPlotRef}
            data={
              typeOfObservation
                .find((obs) => obs.label === observationName)
                ?.prepareChartData(obserationData, observationName) ?? []
            }
          />
        ) : (
          <Heading
            as="h2"
            textAlign="center"
            size="md"
            p="6"
          >
            Ops ! No Data
          </Heading>
        )}
      </Box>
    </Box>
  );
}
