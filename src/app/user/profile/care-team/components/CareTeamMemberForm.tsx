/* eslint-disable no-nested-ternary */
// Package modules
import React, { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import {
  Box,
  Checkbox,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  Input,
  Radio,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { recordCareTeamEvents } from '@user/lib/events-analytics-manager';

import { usePatient } from 'src/app/user/lib/medplum-state';
import { FluentNetwork, NetworkType } from '@lib/models/care-team-member';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { debounce, evalConditions, parsePatientName, parseValueSet } from '@lib/utils/utils';
import { useModalContext } from '../../../../../components/Modal';
import { GroupedRadioButtons, GroupedRadioButtonsDivider } from '../../../../../components/ui/Form/Radio';
import { SearchableSelect, Select, SelectOptionProps } from '../../../../../components/ui/Select';
import { useCareTeamList, useDoctorsList, useValueSetCareTeam } from '../../../lib/medplum-state';
import { RemoveButtonLabel } from '../../components/RemoveButtonLabel';

type NonFluentNetworkFormValues = {
  firstName: string;
  lastName: string;
  speciality: number | string; // ID
  phoneNumber: string;
  altPhoneNumber: string;
  email: string;
  city: number | string; // ID
  country: number | string; // ID
  is_primary: boolean;
};

type FluentNetworkFormValues = {
  id: string | null;
  fullname: string | null;
  is_primary: boolean;
};

const PHONE_REGEX = /^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$/im;
const formatDoctorListToSuggestionList = (list: any) => {
  return list.map((item: any) => ({
    label: item?.name ? parsePatientName(item.name) : '',
    value: item.id,
    payload: item,
  }));
};

function DoctorSelect({ practitionerId }: { practitionerId: string }) {
  const form = useFormContext();
  const [conditionValue, setConditionValue] = useState<SelectOptionProps | null>(null);
  const [keyword, setKeyword] = useState('');
  const debouncedSetKeyword = debounce(setKeyword, 500);
  const { authenticatedUser } = useAuthService();
  const { careTeamMemberList } = useCareTeamList(authenticatedUser?.id);
  const { doctorList } = useDoctorsList(keyword);

  // Determine the filtered list of doctors
  let filteredDoctors = doctorList.filter(
    (doctor: any) =>
      !careTeamMemberList.find((teamMember: any) => {
        if (teamMember?.managingOrganization?.[0]?.display === FluentNetwork.FH) {
          return teamMember?.participant[0]?.member?.resource?.id === doctor.id;
        }
        return false;
      })
  );
  if (practitionerId) {
    filteredDoctors =
      practitionerId && !filteredDoctors.some((doctor: any) => doctor.id === practitionerId)
        ? [...filteredDoctors, doctorList.find((doctor: any) => doctor.id === practitionerId)].filter(Boolean)
        : filteredDoctors;
  }

  const options = formatDoctorListToSuggestionList(filteredDoctors);

  // Set default selected doctor if practitionerId is present
  useEffect(() => {
    if (practitionerId && doctorList.length > 0) {
      const selectedDoctor = doctorList.find((doctor: any) => doctor.id === practitionerId);
      // console.log(selectedDoctor);

      if (selectedDoctor) {
        const defaultOption: SelectOptionProps = {
          value: selectedDoctor.id,
          label: `${selectedDoctor.name[0].given[0]} ${selectedDoctor.name[0].family}`,
        };
        setConditionValue(defaultOption);
        form.setValue('id', defaultOption.value);
        form.setValue('fullname', defaultOption.label);
      }
    }
  }, [practitionerId]);

  const onChange = useCallback(
    (option: SelectOptionProps | any) => {
      if (option) {
        form.setValue('id', option.value);
        form.setValue('fullname', option.label);
        setConditionValue(option);
        // recordCareTeamEvents(trackEventInFlow, {
        //   EventName: practitionerId ? 'EditCareTeamProgress' : 'AddCareTeamProgress',
        //   entry_point: 'health profile',
        //   practitioner_network: 'fluent network',
        //   primary_doctor: false,
        //   ct_doctor_id: option.value,
        //   ct_doctor_name: option.label,
        //   step_completed: 'doctor name added',
        // });
      } else {
        form.setValue('id', null);
        setConditionValue(null);
      }
      form.trigger('id');
    },
    [form]
  );

  return (
    <SearchableSelect
      labelText="Select doctor"
      value={conditionValue}
      options={options}
      onSearch={debouncedSetKeyword}
      onChange={onChange}
    />
  );
}

function FluentNetworkForm({
  practitionerId,
  is_primary,
}: {
  practitionerId: string;
  is_primary: boolean;
}): JSX.Element {
  const form = useFormContext();
  // const { trackEventInFlow } = useAnalyticsService();
  // useEffect(() => {
  //   recordCareTeamEvents(trackEventInFlow, {
  //     EventName: practitionerId ? 'EditCareTeamStarted' : 'AddCareTeamStarted',
  //     entry_point: 'health profile',
  //     practitioner_network: 'fluent network',
  //     primary_doctor: is_primary || false,
  //     step_completed: practitionerId ? 'edit care team started' : 'add care team started',
  //   });
  // }, [trackEventInFlow, practitionerId, is_primary]);

  return (
    <>
      <FormControl
        variant="floating"
        isInvalid={!!form.formState.errors.clinician}
      >
        <DoctorSelect practitionerId={practitionerId} />
        <FormErrorMessage>This field is required</FormErrorMessage>
      </FormControl>
      <Checkbox
        colorScheme="fluentHealth"
        isDisabled={form.formState.isSubmitting}
        defaultChecked={is_primary}
        {...form.register('is_primary')}
        // onChange={(e) => {
        //   if (e.target.checked) {
        //     recordCareTeamEvents(trackEventInFlow, {
        //       EventName: practitionerId ? 'EditCareTeamProgress' : 'AddCareTeamProgress',
        //       entry_point: 'health profile',
        //       practitioner_network: 'fluent network',
        //       primary_doctor: true,
        //       step_completed: 'primary selected',
        //     });
        //   }
        // }}
      >
        This is my primary doctor
      </Checkbox>
    </>
  );
}

function NonFluentNetworkForm({ isEditForm, is_primary }: { isEditForm: boolean; is_primary: boolean }) {
  const form = useFormContext();
  const { trackEventInFlow } = useAnalyticsService();

  const specialtyField = `sty:${form.watch('speciality')?.toLowerCase()}`;
  const cityField = form.watch('city');

  const { valueSetList } = useValueSetCareTeam();

  const SPECIALTY_OPTION = parseValueSet(valueSetList?.specialtyValueSet);

  const CITY_OPTION_IN = parseValueSet(valueSetList?.citiesINValueSet);
  const CITY_OPTION = CITY_OPTION_IN;

  const customSpecialtySelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('speciality', value?.value);
    form.trigger('speciality');
    if (!isEditForm) {
      recordCareTeamEvents(trackEventInFlow, {
        EventName: 'CareTeamAddInProgSpeciality',
        ct_speciality: value?.value,
      });
    }
  }, []);
  const handleKeyPressLetters = (e: React.KeyboardEvent) => {
    if (!/[A-Za-z]/.test(e.key)) {
      e.preventDefault();
    }
  };
  const customCitySelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('city', value?.value);
    form.trigger('city');
    if (!isEditForm) {
      recordCareTeamEvents(trackEventInFlow, {
        EventName: 'CareTeamAddInProgCity',
        ct_city: value?.label,
      });
    }
  }, []);

  useEffect(() => {
    recordCareTeamEvents(trackEventInFlow, {
      EventName: isEditForm ? 'CareTeamEditStarted' : 'CareTeamAddStarted',
    });
  }, [trackEventInFlow, isEditForm, is_primary]);

  return (
    <>
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap="24px"
      >
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.firstName}
        >
          <Input
            onKeyDown={handleKeyPressLetters}
            placeholder=" "
            {...form.register('firstName')}
            onBlur={(e) => {
              if (!isEditForm) {
                recordCareTeamEvents(trackEventInFlow, {
                  EventName: 'CareTeamAddInProgFirstName',
                  ct_first_name: e.target.value,
                });
              }
            }}
          />
          <FormLabel>First name*</FormLabel>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
        <FormControl
          onKeyDown={handleKeyPressLetters}
          variant="floating"
          isInvalid={!!form.formState.errors.lastName}
        >
          <Input
            placeholder=" "
            {...form.register('lastName')}
            onBlur={(e) => {
              if (!isEditForm) {
                recordCareTeamEvents(trackEventInFlow, {
                  EventName: 'CareTeamAddInProgLastName',
                  ct_last_name: e.target.value,
                });
              }
            }}
          />
          <FormLabel>Last name*</FormLabel>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
      </Grid>
      <Flex
        justifyContent="space-between"
        gap="6"
      >
        <FormControl isInvalid={!!form.formState.errors.speciality}>
          <Select
            labelText="Speciality *"
            value={SPECIALTY_OPTION?.find((item: any) => item?.value === specialtyField)}
            onChange={customSpecialtySelect}
            options={SPECIALTY_OPTION}
            isSearchable
            isClearable
          />
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>

        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.phoneNumber}
        >
          <Input
            placeholder=" "
            {...form.register('phoneNumber')}
            onBlur={(e) => {
              if (!isEditForm && !form.formState.errors.phoneNumber) {
                recordCareTeamEvents(trackEventInFlow, {
                  EventName: 'CareTeamAddInProgPhoneNumber',
                  ct_phone_number: e.target.value,
                });
              }
            }}
          />
          <FormLabel>Contact number</FormLabel>
          <FormErrorMessage>{form.formState.errors.phoneNumber?.message?.toString()}</FormErrorMessage>
        </FormControl>
      </Flex>
      <Flex
        justifyContent="space-between"
        gap="6"
      >
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.altPhoneNumber}
        >
          <Input
            placeholder=" "
            {...form.register('altPhoneNumber')}
            onBlur={(e) => {
              if (!isEditForm && !form.formState.errors.altPhoneNumber) {
                recordCareTeamEvents(trackEventInFlow, {
                  EventName: 'CareTeamAddInProgAltPhoneNumber',
                  ct_alt_phone_number: e.target.value,
                });
              }
            }}
          />
          <FormLabel>Alternate contact number</FormLabel>
          <FormErrorMessage>{form.formState.errors.altPhoneNumber?.message?.toString()}</FormErrorMessage>
        </FormControl>
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.email}
        >
          <Input
            placeholder=" "
            type="email"
            {...form.register('email')}
            onBlur={(e) => {
              if (!isEditForm && !form.formState.errors.email) {
                recordCareTeamEvents(trackEventInFlow, {
                  EventName: 'CareTeamAddInProgEmail',
                  ct_email: e.target.value,
                });
              }
            }}
          />
          <FormLabel>Email address</FormLabel>
          <FormErrorMessage>{form.formState.errors.email?.message?.toString()}</FormErrorMessage>
        </FormControl>
      </Flex>
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap="6"
      >
        <FormControl
          variant="floating"
          isInvalid={!!form.formState.errors.country}
        >
          <Input
            value="India"
            readOnly
            disabled
            {...form.register('country')}
          />
          <FormLabel>Country</FormLabel>
        </FormControl>
        <FormControl isInvalid={!!form.formState.errors.city}>
          <Select
            labelText="City"
            value={CITY_OPTION?.find((item: any) => item?.value === cityField)}
            onChange={customCitySelect}
            options={CITY_OPTION}
            isSearchable
            isClearable
          />
        </FormControl>
      </Grid>
      <Checkbox
        colorScheme="fluentHealth"
        isDisabled={form.formState.isSubmitting}
        {...form.register('is_primary')}
        onChange={(e) => {
          if (!isEditForm) {
            recordCareTeamEvents(trackEventInFlow, {
              EventName: 'CareTeamAddInProgPrimary',
              primary_doctor: Boolean(e.target.checked),
            });
          }
        }}
      >
        {isEditForm ? 'Is this your primary doctor?' : 'Is this your primary doctor?'}
      </Checkbox>
    </>
  );
}

export default function CareTeamMemberForm({
  member,
  visibleSection,
}: {
  member: any;
  visibleSection?: NetworkType | null; // From which section (FH/Non-FH) was "Add" clicked
}) {
  // Add/edit form type
  const isEditForm = !!member;

  const { clearPatientState } = usePatient();
  const [isLoading, setIsLoading] = useState<boolean>(false); // Used for contact remove -- cannot bind to form's isLoading
  const [network, setNetwork] = useState<NetworkType | string>(() => {
    if (isEditForm) {
      if (member && !member.managingOrganization) {
        return NetworkType.NonFH;
      }
      return member?.type_of_network ?? NetworkType.FH;
    }
    return visibleSection ?? NetworkType.FH;
  });

  const practitioner = member?.member?.resource;
  const email = practitioner?.telecom?.find((telecom: any) => telecom.system === 'email')?.value;
  const phoneNumber = practitioner?.telecom?.find((telecom: any) => telecom.system === 'phone')?.value;
  const altPhoneNumber = practitioner?.telecom?.find((telecom: any) => telecom.use === 'other')?.value;

  const modal = useModalContext();
  const removeMemberModal = useDisclosure();
  const toast = useToast();

  const { authenticatedUser } = useAuthService();
  const { careTeamMemberList, addMember, editMember, addNonFluentMenber, editNonFluentMenber } = useCareTeamList(
    authenticatedUser.id
  );
  const { trackEventInFlow } = useAnalyticsService();

  const fluentNetworkForm = useForm({
    mode: 'onChange',
    defaultValues: {
      is_primary: member?.is_primary ?? false,
      fullname: null,
      id: null, // null for edit mode
    },
    resolver: zodResolver(
      z.object({
        is_primary: z.boolean(),
        fullname: z.string(),
        id: z.string().min(1),
      })
    ),
  });

  const name = member ? parsePatientName(practitioner?.name) : ' ';
  const [firstName = '', lastName = ''] = name.split(' ');
  const nonFluentNetworkForm = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1),
        lastName: z.string().min(1),
        speciality: z.string().min(1),
        phoneNumber: z
          .string()
          .max(0, 'Invalid phone number')
          .or(z.string().regex(PHONE_REGEX, 'Invalid phone number')),
        altPhoneNumber: z
          .string()
          .max(0, 'Invalid phone number')
          .or(z.string().regex(PHONE_REGEX, 'Invalid phone number')),
        email: z.string().min(1, 'This field is required').email('Invalid email').or(z.literal('')).optional(),
        city: z.string().min(1).optional(),
        country: z.string().min(1),
        is_primary: z.boolean(),
      })
    ),
    defaultValues: {
      firstName,
      lastName,
      speciality: practitioner?.practitionerRole?.[0]?.specialty?.[0]?.coding?.[0]?.display,
      phoneNumber,
      altPhoneNumber,
      email,
      city: practitioner?.address?.[0]?.city,
      country: practitioner?.address?.[0]?.country || 'IN',
      is_primary: member?.is_primary ?? false,
    },
  });

  async function onRemove() {
    try {
      setIsLoading(true);
      removeMemberModal.onOpen();
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  }

  async function fluentNetworkFormSubmitHandler(data: FluentNetworkFormValues) {
    try {
      setIsLoading(true);
      await (isEditForm ? editMember({ formValues: data, subMember: member }) : addMember({ formValues: data }));
      await clearPatientState();
      // recordCareTeamEvents(trackEventInFlow, {
      //   EventName: member ? 'EditCareTeamCompleted' : 'AddCareTeamCompleted',
      //   entry_point: 'health profile',
      //   practitioner_network: 'fluent network',
      //   primary_doctor: data.is_primary || false,
      //   ct_doctor_name: data.fullname?.toString(),
      //   step_completed: 'my care team saved',
      // });

      toast({
        title: `Successfully ${isEditForm ? 'edit' : 'add'}ed the Care Team member`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      if (!isEditForm) fluentNetworkForm.reset();

      modal.modalDisclosure.onClose();
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  }

  async function nonFluentNetworkFromSubmitHandler(data: NonFluentNetworkFormValues) {
    try {
      setIsLoading(true);
      if (isEditForm) {
        const practitionerId = practitioner?.id;
        const practitionerRoleId = practitioner?.practitionerRole?.[0]?.id;
        await editNonFluentMenber({
          formValues: { ...data, subMember: member },
          practitionerId,
          practitionerRoleId,
          careTeamMemberList,
        });
      } else {
        await addNonFluentMenber({ formValues: data, careTeamMemberList });
      }
      await clearPatientState();
      recordCareTeamEvents(trackEventInFlow, {
        EventName: isEditForm ? 'CareTeamEditCompleted' : 'CareTeamAddCompleted',
        primary_doctor: data.is_primary || false,
        ct_first_name: data.firstName,
        ct_last_name: data.lastName,
        ct_speciality: data?.speciality?.toString(),
        ct_alt_phone_number: data?.altPhoneNumber,
        ct_city: data?.city?.toString(),
        ct_email: data?.email,
        ct_phone_number: data?.phoneNumber,
      });

      toast({
        title: `Successfully ${isEditForm ? 'edit' : 'add'}ed the Care Team member`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      // Clear form if "add" mode
      if (!isEditForm) nonFluentNetworkForm.reset();

      modal.modalDisclosure.onClose();
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    const primaryButtonEnabled =
      network === NetworkType.FH ? fluentNetworkForm.formState.isValid : nonFluentNetworkForm.formState.isValid;
    const onPrimaryButtonClick =
      network === NetworkType.FH
        ? fluentNetworkForm.handleSubmit(fluentNetworkFormSubmitHandler)
        : nonFluentNetworkForm.handleSubmit(nonFluentNetworkFromSubmitHandler);

    modal.setModalProps((prevState) => ({
      ...prevState,
      onPrimaryButtonClick,
      primaryButtonEnabled,
      isPrimaryButtonLoading: isLoading,
      // On edit mode
      tertiaryButtonVariant: 'quiet',
      tertiaryButtonLabel: <RemoveButtonLabel />,
      onTertiaryButtonClick: onRemove,
      ...(isEditForm ? { showTertiaryButton: true } : { showTertiaryButton: false }),
    }));
  }, [
    isEditForm,
    isLoading,
    network,
    fluentNetworkForm.formState.isValid,
    nonFluentNetworkForm.formState.isValid,
    fluentNetworkForm.handleSubmit,
    nonFluentNetworkForm.handleSubmit,
  ]);

  return (
    <Flex direction="column">
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        gap="8"
        py="4"
      >
        {false && (
          // {!isEditForm && (
          <GroupedRadioButtons
            borderColor="fluentHealthComplementary.Green"
            value={network}
            onChange={setNetwork}
            isDisabled={isEditForm}
          >
            <Radio value={NetworkType.FH}>Fluent Network</Radio>
            <GroupedRadioButtonsDivider />
            <Radio value={NetworkType.NonFH}>Non-Fluent Network</Radio>
          </GroupedRadioButtons>
        )}
        {evalConditions([
          [
            'AND',
            network === NetworkType.NonFH,
            <FormProvider {...nonFluentNetworkForm}>
              <NonFluentNetworkForm
                isEditForm={isEditForm}
                is_primary={member?.is_primary}
              />
            </FormProvider>,
          ],
          [
            'AND',
            network === NetworkType.FH,
            <FormProvider {...fluentNetworkForm}>
              <FluentNetworkForm
                practitionerId={member?.participant?.[0]?.member?.resource?.id}
                is_primary={member?.is_primary}
              />
            </FormProvider>,
          ],
        ])}
      </Box>
    </Flex>
  );
}
