// Package modules
import React, { useEffect, useState } from 'react';
import { Divider, Flex, useDisclosure } from '@chakra-ui/react';

// Local modules
import GenericProfileCard from '../../components/GenericProfileCard';
import { CareTeamMember, NetworkType } from '@lib/models/care-team-member';
import { parseValueSet } from '@lib/utils/utils';
import { RoundedTabPanel, RoundedTabPanels, RoundedTabs } from '../../components/RoundedTabs';
import CareTeamMemberCard from './CareTeamMemberCard';
import { useAuthService, usePublicSettings } from '@lib/state';
import { useCareTeamList, useValueSetCareTeam } from '../../../lib/medplum-state';

export function CareTeamList({
  actionButton,
}: {
  actionButton: (value?: CareTeamMember, networkType?: NetworkType) => void;
}) {
  const { authenticatedUser } = useAuthService();
  const { careTeamMemberList } = useCareTeamList(authenticatedUser?.id);
  const { valueSetList } = useValueSetCareTeam();

  const resource: any = careTeamMemberList[0]?.participant?.[0]?.member?.resource;
  if (careTeamMemberList?.length && resource && valueSetList.citiesINValueSet) {
    const resourceAddress = resource?.address?.[0];
    if (!resourceAddress?.cityText) {
      const CITY_OPTION = parseValueSet(valueSetList.citiesINValueSet);
      // const COUNTRY_OPTION = parseValueSet(valueSetList.countriesValueSet);
      careTeamMemberList.forEach((item) => {
        const participantResource: any = item.participant?.[0]?.member?.resource;
        const address = participantResource?.address?.[0];
        if (address) {
          address.cityText = CITY_OPTION.find((e) => e.value === address.city)?.label ?? address.cityText;
          address.countryText = 'India';
        }
      });
    }
  }

  const { isPublicMode } = usePublicSettings();
  const drawer = useDisclosure();

  // Splitting results into FH/non-FH network
  // const fhNetwork = careTeamMemberList.filter(
  //   (entry: any) => entry?.managingOrganization?.[0].display === 'Fluent Health'
  // );
  // console.log(careTeamMemberList[0]);

  const nonFhNetwork = careTeamMemberList[0].participant;

  const [currentTabType, setCurrentTabType] = useState<NetworkType>(NetworkType.NonFH);
  // const [mySelectedPractitioner, setMySelectedPractitioner] = useState<number>(0);
  // const selectedPractitioner = (id: any) => {
  //   setMySelectedPractitioner(id);
  // };
  function isNotLastElement(idx: number, collection?: any) {
    // Defaults to "CareTeamMember" array if none provided
    if (!collection) collection = careTeamMemberList!; // eslint-disable-line no-param-reassign

    return idx + 1 < collection.length;
  }

  function onTabChanged(idx: number) {
    const type = [NetworkType.FH, NetworkType.NonFH][idx];
    setCurrentTabType(type);
  }
  useEffect(() => {
    document.documentElement.style.overflow = drawer.isOpen ? 'hidden' : 'unset';
  }, [drawer.isOpen]);

  return !careTeamMemberList?.length ? (
    <GenericProfileCard
      title="Keep all your medical contacts in one place"
      info="Build your care team and keep track of your healthcare professionals’ information."
      actionButtonText="to My Care Team"
      actionButton={() => actionButton()}
    />
  ) : (
    <RoundedTabs
      onChange={onTabChanged}
      pt={0}
    >
      <GenericProfileCard
        title="My care team"
        info="Build your care team and keep track of your healthcare professionals’ information."
        actionButton={() => actionButton(undefined, currentTabType)}
        customHeader={
          // <RoundedTabList
          //   p="2px"
          //   width="100%"
          //   marginRight={{ base: '0px', md: '40px' }}
          //   backgroundColor="fluentHealthSecondary.500"
          // >
          //   {/* <RoundedTab
          //     padding="8px 8px 8px 12px"
          //     fontSize="md"
          //   >
          //     Fluent Network
          //   </RoundedTab> */}
          //   {/* <RoundedTab
          //     padding="8px 8px 8px 12px"
          //     fontSize="md"
          //   >
          //     Non-Fluent Network
          //   </RoundedTab> */}
          // </RoundedTabList>
          <span
            style={{
              textTransform: 'uppercase',
              fontSize: '13px',
              fontWeight: '500',
              letterSpacing: '1.56px',
              lineHeight: '100%',
            }}
          >
            My Care Team
          </span>
        }
      >
        <RoundedTabPanels>
          {/* <RoundedTabPanel>
              <Flex
                flexWrap="wrap"
                rowGap={{ base: '24px', md: '12px' }}
              >
                {fhNetwork.length === 0 && (
                  // && !isFetching
                  <GenericProfileCard
                    title="Keep all your medical contacts in one place"
                    info="Build your care team and keep track of your healthcare professionals’ information."
                    actionButtonText="to My Care Team"
                    actionButton={() => actionButton()}
                  />
                )}
                <Drawer
                  placement="right"
                  onClose={drawer.onClose}
                  isOpen={drawer.isOpen}
                  blockScrollOnMount
                  size={{ base: 'full', md: 'sm' }}
                >
                  <DrawerOverlay />
                  <DrawerContent
                    bg="gradient.profileDrawer"
                    maxW="440px"
                  >
                    <Suspense
                      fallback={
                        <FluentHealthLoader
                          position="absolute"
                          top={0}
                          bottom={0}
                          left={0}
                          right={0}
                          my="auto"
                        />
                      }
                    >
                      <CareInfoDrawer
                        onClose={drawer.onClose}
                        id={mySelectedPractitioner}
                        Patient={fhNetwork}
                      />
                    </Suspense>
                  </DrawerContent>
                </Drawer>
                {fhNetwork.map((member: any, index: number) => (
                  <React.Fragment key={member.id}>
                    <CareTeamMemberCard
                      careTeamMember={member}
                      actionButton={actionButton}
                      // onRemoveMember={onRemoveMember}
                      isFluentNetworkMedic
                      isPublicMode={isPublicMode}
                      drawer={drawer}
                      SelectedPractitioner={selectedPractitioner}
                    />
                    {isNotLastElement(index, fhNetwork) && <Divider borderColor="fluentHealthText.500" />}
                  </React.Fragment>
                ))}
              </Flex>
            </RoundedTabPanel> */}
          <RoundedTabPanel>
            <Flex
              flexWrap="wrap"
              rowGap="5"
            >
              {!nonFhNetwork?.length && (
                // && !isFetching
                <GenericProfileCard
                  title="Keep all your medical contacts in one place"
                  info="Build your care team and keep track of your healthcare professionals’ information."
                  actionButtonText="to My Care Team"
                  actionButton={() => actionButton()}
                />
              )}
              {nonFhNetwork?.map((member: any, index: number) => (
                <React.Fragment key={member?.member?.reference}>
                  <CareTeamMemberCard
                    careTeamMember={member}
                    actionButton={actionButton}
                    isFluentNetworkMedic={false}
                    isPublicMode={isPublicMode}
                    drawer={drawer}
                  />
                  {isNotLastElement(index, nonFhNetwork) && <Divider borderColor="fluentHealthText.500" />}
                </React.Fragment>
              ))}
            </Flex>
          </RoundedTabPanel>
          {/* {hasNextPage && (
              <ProfileCardItemSkeleton
                ref={loadingElementRef}
                mt="6"
              />
            )} */}
        </RoundedTabPanels>
      </GenericProfileCard>
    </RoundedTabs>
  );
}
