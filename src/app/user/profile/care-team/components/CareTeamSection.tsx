import { Suspense, useState } from 'react';

import { CareTeamList } from './CareTeamList';
import { NetworkType } from '@lib/models/care-team-member';
import CareTeamMemberForm from './CareTeamMemberForm';
import { FluentHealthLoader } from '../../../../../components/FluentHealthLoader';
import { Modal, ModalProvider, useModal } from '../../../../../components/Modal';

export function CareTeamSection() {
  const modal = useModal();
  const [currentCareTeamMember, setCurrentCareTeamMember] = useState(null);

  // Visible section (FH / Non-FH network)
  const [visibleSection, setVisibleSection] = useState<NetworkType>(NetworkType.FH);

  const onCareTeamMemberAction = (member?: any | null, type?: NetworkType | null) => {
    setVisibleSection(type ?? NetworkType.NonFH);
    setCurrentCareTeamMember(member ?? null);
    modal.modalDisclosure.onOpen();
  };

  return (
    <>
      <CareTeamList actionButton={onCareTeamMemberAction} />
      <ModalProvider {...modal}>
        <Modal
          title="My Care Team"
          primaryButtonLabel={currentCareTeamMember ? 'Save' : 'Add'}
          showSecondaryButton={false}
          minWidth="xl" // 576px
          isCentered
          scrollY="false"
          maxH="1000px"
          {...modal.modalProps}
          {...modal.modalDisclosure}
        >
          <Suspense fallback={<FluentHealthLoader />}>
            <CareTeamMemberForm
              member={currentCareTeamMember}
              visibleSection={visibleSection}
            />
          </Suspense>
        </Modal>
      </ModalProvider>
    </>
  );
}
