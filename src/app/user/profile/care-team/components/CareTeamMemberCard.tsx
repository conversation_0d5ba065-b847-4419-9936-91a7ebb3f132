// Package modules
import { <PERSON>, <PERSON><PERSON>, Card, CardBody, Flex, <PERSON>ing, IconButton, Image, Text } from '@chakra-ui/react';
import { Edit3 as PenIcon } from 'react-feather';

import { parsePatientName } from '@lib/utils/utils';
import { usePatient } from 'src/app/user/lib/medplum-state';

export default function CareTeamMemberCard({
  careTeamMember,
  actionButton,
  // onRemoveMember,
  isFluentNetworkMedic = false,
  isPublicMode = false,
  drawer,
}: // SelectedPractitioner,
{
  careTeamMember: any;
  actionButton: (value?: any) => void;
  // onRemoveMember?: (value: any) => void;
  isFluentNetworkMedic?: boolean;
  isPublicMode?: boolean;
  drawer: any;
  // SelectedPractitioner: (id: number) => void;
}) {
  const { patient } = usePatient();
  const practitioner = careTeamMember?.member?.resource;
  const isPrimaryMedic =
    practitioner &&
    patient?.generalPractitioner?.find(
      (generalPractitioner: any) => generalPractitioner.reference === `Practitioner/${practitioner.id}`
    )?.reference;
  const email = practitioner?.telecom?.find((telecom: any) => telecom.system === 'email')?.value;
  const phone = practitioner?.telecom?.find((telecom: any) => telecom.system === 'phone')?.value;
  const otherPhone = practitioner?.telecom?.find((telecom: any) => telecom.use === 'other')?.value;
  const headerColors = isFluentNetworkMedic
    ? {
        // Fluent Network related
        heading: 'fluentHealthText.100',
        text: 'fluentHealthText.100',
        primaryText: 'fluentHealthText.100',
        primaryTextBg: 'fluentHealthComplementary.Salmon2',
        bg: 'transparent',
      }
    : {
        // Non-Fluent Network related
        heading: 'periwinkle.700',
        text: 'iris.300',
        primaryText: 'periwinkle.700',
        primaryTextBg: 'periwinkle.200',
        bg: 'transparent',
      };

  return (
    <Flex
      w="full"
      borderRadius="8px"
      padding="20px"
      position="relative"
      direction="column"
      justifyContent="space-between"
      bgColor={isFluentNetworkMedic ? 'fluentHealthComplementary.Salmon1' : 'var(--Periwinkle-50, #F8F8FF)'}
      role="group"
    >
      <Box height="30px">
        {isPrimaryMedic && (
          <Heading
            variant="primaryCareTeamMember"
            textColor={headerColors.primaryText}
            backgroundColor={headerColors.primaryTextBg}
            w="max-content"
            lineHeight="13px"
            height="18px"
          >
            Primary
          </Heading>
        )}
      </Box>
      <Flex
        w="full"
        gap="8px"
        padding="0"
        position="relative"
        direction={{ base: 'column', md: 'row' }}
        justifyContent="space-between"
      >
        <Card
          direction={{ base: 'column', md: 'row' }}
          justifyContent="space-between"
          bgColor={headerColors.bg}
          w={{ base: 'auto', md: '240px' }}
          display="flex"
          boxShadow="none"
        >
          <CardBody
            padding="0"
            pos="relative"
            display="flex"
            flexDirection="column"
          >
            <Heading
              fontSize="24px"
              lineHeight="32px"
              textColor={isFluentNetworkMedic ? 'black' : 'iris.500'}
            >
              {parsePatientName(practitioner?.name)}
              {practitioner?.identifier?.[0]?.use === 'official' &&
                `, ${practitioner.identifier?.[0]?.type?.coding?.[0]?.code}`}
            </Heading>

            <Flex
              align="center"
              mt="auto"
              display={{ base: 'none', md: 'flex' }}
            >
              {isFluentNetworkMedic && (
                <Image
                  src="/fh_logo.png"
                  width="82px"
                />
              )}
            </Flex>
          </CardBody>
        </Card>
        <Box
          flex="1"
          display="flex"
          flexDirection="column"
          gap="4px"
        >
          {practitioner?.identifier?.[0]?.use === 'official' &&
            (() => {
              const { assigner, type, value } = practitioner.identifier[0];
              const { coding } = type || {};
              const codeDisplay = coding?.[0]?.display;
              const code = coding?.[0]?.code;
              return (
                <Text color="fluentHealthText.200">
                  {assigner?.display}, {code}, {codeDisplay}, {value}
                </Text>
              );
            })()}

          {!isFluentNetworkMedic && (
            <>
              {email && <Text color="fluentHealthText.200">{email}</Text>}
              {phone && (
                <Text color="fluentHealthText.200">
                  {phone} &nbsp;&nbsp;&nbsp; {otherPhone ?? ''}
                </Text>
              )}
            </>
          )}
          {practitioner?.address?.[0] && (
            <Text color="fluentHealthText.200">
              {`${
                practitioner.address[0].cityText
                  ? `${practitioner.address[0].cityText}, `
                  : practitioner.address[0].city || ''
              }`}
              {`${practitioner.address[0].countryText || practitioner.address[0].country || ''}`}
            </Text>
          )}
          {isFluentNetworkMedic && (
            <Flex
              align="center"
              mt="auto"
              display={{ base: 'flex', md: 'none' }}
            >
              <Image
                src="/fh_logo.png"
                width="82px"
              />
            </Flex>
          )}

          {!isPublicMode && isFluentNetworkMedic && (
            <Flex
              w="full"
              mt={4}
              gap={{ base: '16px', md: '8px' }}
              direction="row"
            >
              <Button
                minHeight="32px"
                border="1px solid"
                borderColor="fluentHealthText.200"
                borderRadius="3xl"
                color="white"
                flex="1"
                onClick={() => {
                  drawer.onOpen();
                  // SelectedPractitioner(careTeamMember.id);
                }}
              >
                View profile
              </Button>
              <Button
                minHeight="32px"
                border="1px solid"
                borderRadius="3xl"
                variant="outline"
                flex="1"
              >
                Connect
              </Button>
            </Flex>
          )}
        </Box>
      </Flex>

      {!isPublicMode && (
        <IconButton
          aria-label="Edit"
          variant="ghost"
          size="sm"
          p={0}
          position="absolute"
          right="10px"
          top="10px"
          color="fluentHealthSecondary.100"
          onClick={() => actionButton({ ...careTeamMember, is_primary: !!isPrimaryMedic })}
          icon={<PenIcon size={16} />}
          opacity={{ base: 1, md: 0 }}
          _groupHover={{ opacity: 1 }}
        />
      )}
    </Flex>
  );
}
