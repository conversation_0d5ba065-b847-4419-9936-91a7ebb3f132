import dayjs from 'dayjs';
import { FormProvider, useForm } from 'react-hook-form';
import { Box, Button, Heading, Radio, Text, useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';

import { DatePickerField } from 'src/components/ui/Form';
import { TopLeftDecoration } from '../assets/objects/TopLeftDecoration';
import { BottomRightDecoration } from '../assets/objects/BottomRightDecoration';
import { BottomLeftDecoration } from '../assets/objects/BottomLeftDecoration';
import { TopRightDecoration } from '../assets/objects/TopRightDecoration';
import { QuizzArrows } from './QuizzArrows';

interface IQuizMaker {
  title: string;
  ctaButtonText?: string;
  answers?: string[];
  calendar?: boolean;
  bannerTheme: {
    bgColor: string;
  };
  goToNextStep: () => void;
  goToPrevStep: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
}
type AMRFSelectDatePayload = {
  date: string; // YYYY-MM-DD
};
export function QuizMaker({
  title,
  ctaButtonText,
  answers,
  calendar,
  bannerTheme,
  goToNextStep,
  goToPrevStep,
  canGoBack,
  canGoForward,
}: IQuizMaker) {
  const datePickerPopover = useDisclosure();

  const form = useForm<AMRFSelectDatePayload>({
    mode: 'all',
    defaultValues: {
      date: '',
    },
  });

  const dateField = form.watch('date');

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('date', '');
    }
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('date', '');
    datePickerPopover.onClose();
  };

  // Initialize the clickedAnswers state with an empty array
  const [clickedAnswers, setclickedAnswers] = useState<string[]>([]);

  const handleButtonClick = (answer: string) => {
    if (clickedAnswers.includes(answer)) {
      // If the answer is already selected, remove it from the clickedAnswers array
      setclickedAnswers((prevButtons) => prevButtons.filter((button) => button !== answer));
    } else {
      // Otherwise, add the answer to the clickedAnswers array
      setclickedAnswers((prevButtons) => [...prevButtons, answer]);
    }
  };
  const goForward = () => {
    if (!goToNextStep && !canGoForward) return;
    if (clickedAnswers.length < 1) return;
    goToNextStep();
  };
  const goBack = () => {
    if (!goToPrevStep && !canGoBack) return;
    goToPrevStep();
  };

  return (
    <Box
      display="inline-flex"
      flexDirection="column"
      alignItems="center"
      width="100%"
      height="100dvh"
      bg={bannerTheme.bgColor}
      overflow="hidden"
      padding="60px 0px 62px 0px"
      position="relative"
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        gap="36px"
      >
        <Heading
          fontSize="28px"
          lineHeight="1.21"
          letterSpacing="-0.56px"
          textAlign="center"
          maxWidth="400px"
        >
          {title}
        </Heading>
        {/* // Render the answers if available */}
        {answers && answers.length > 0 && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            gap="12px"
            width="320px"
          >
            <Text
              fontSize="16px"
              lineHeight="1.5"
              fontStyle="normal"
              fontWeight="400"
              letterSpacing="-0.32px"
            >
              You can select multiple answers
            </Text>
            {/* Render the answers */}
            {answers.map((answer) => (
              <Box
                key={answer}
                display="flex"
                background="#FFF3F0"
                border="1px solid #FFC1AD"
                padding="16px 20px"
                borderRadius="30px"
                marginBottom="8px"
                alignSelf="stretch"
                cursor="pointer"
                onClick={() => handleButtonClick(answer)}
              >
                <Radio
                  value={answer}
                  isChecked={clickedAnswers.includes(answer)}
                  marginRight="12px"
                  borderColor="#FFC1AD"
                  size="lg"
                />
                <Box>{answer}</Box>
              </Box>
            ))}
          </Box>
        )}
        {calendar && (
          <Box mt="24px">
            <FormProvider {...form}>
              <DatePickerField
                name="date"
                errorText="This field is required"
                rules={{ required: true }}
                datePickerChangeHandler={datePickerChangeHandler}
                datePickerClearHandler={datePickerClearHandler}
                datePickerPopover={datePickerPopover}
                selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
                maxDate={new Date()}
              />
            </FormProvider>
          </Box>
        )}
        {/* Button */}
        {ctaButtonText && (
          <Button
            fontSize="18px"
            lineHeight="1.44444444"
            letterSpacing="-0.36px"
            padding="12px 24px"
            fontWeight="500"
            bg={clickedAnswers.length > 0 ? '#4956E4' : '#A9AEAF'}
            borderRadius="55px"
            onClick={form.handleSubmit(goForward)}
            _hover={{ bg: clickedAnswers.length > 0 ? '#4956E4' : '#A9AEAF' }}
            _active={{ bg: clickedAnswers.length > 0 ? '#4956E4' : '#A9AEAF' }}
          >
            {ctaButtonText}
          </Button>
        )}
      </Box>
      <QuizzArrows
        goToNextStep={goForward}
        goToPrevStep={goBack}
        canGoBack={canGoBack}
        canGoForward={canGoForward}
      />
      <TopLeftDecoration />
      <TopRightDecoration />
      <BottomLeftDecoration />
      <BottomRightDecoration />
    </Box>
  );
}
