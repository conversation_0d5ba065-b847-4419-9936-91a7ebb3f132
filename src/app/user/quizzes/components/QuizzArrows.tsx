import { Box } from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';

export function QuizzArrows({
  goToNextStep,
  goToPrevStep,
  canGoBack,
  canGoForward,
}: {
  goToNextStep: () => void;
  goToPrevStep: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
}) {
  return (
    <Box
      position="absolute"
      zIndex="3000"
      right="62px"
      bottom="70px"
      overflow="hidden"
      display="inline-flex"
      justifyContent="center"
      alignItems="center"
      borderRadius="8px"
      bgColor="rgba(73,86,228)"
      cursor="pointer"
    >
      <Box
        p="8px"
        onClick={() => {
          if (canGoBack) {
            goToPrevStep();
          }
        }}
        opacity="0.3"
      >
        <ChevronLeftIcon
          height="24px"
          width="24px"
          color="white"
        />
      </Box>
      <Box
        width="1px"
        bgColor="white"
        opacity="0.33"
        alignSelf="stretch"
      />
      <Box
        p="8px"
        onClick={() => {
          if (canGoForward) {
            goToNextStep();
          }
        }}
        opacity="0.3"
      >
        <ChevronRightIcon
          height="24px"
          width="24px"
          color="white"
        />
      </Box>
    </Box>
  );
}
