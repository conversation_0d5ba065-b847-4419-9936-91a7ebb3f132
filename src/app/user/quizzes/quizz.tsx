import { Box, Fade } from '@chakra-ui/react';
import { CloseIcon } from '@chakra-ui/icons';
import { NavLink } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { QuizMaker } from './components/QuizzMaker';
import { useStepper } from 'src/app/medical-records/lib/utils';

enum QUIZZ_STEPS {
  EXERCISE_ACTIVITY_STEP = 1,
  EXERCISE_SCREENING_STEP = 2,
}

export function Quizz() {
  const stepper = useStepper(Object.keys(QUIZZ_STEPS).length, {});

  const { DOCUMENTS, PERSONAL } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  return (
    <Box>
      <NavLink
        to={`/${DOCUMENTS}/${PERSONAL}/${VIEW}`}
        style={{ position: 'absolute', top: '20px', right: '20px', zIndex: '1' }}
      >
        <CloseIcon
          width="36px"
          height="36px"
          padding="8px"
          opacity="0.5"
        />
      </NavLink>
      {stepper.currentStep === QUIZZ_STEPS.EXERCISE_ACTIVITY_STEP && (
        <Fade in>
          <QuizMaker
            title="What exercise activities do you currently take part in ?"
            ctaButtonText="Continue"
            answers={['Weight Lifting', 'Running', 'Yoga', 'Cardio', 'Stretching', 'Pilates', 'Callisthenics']}
            bannerTheme={{ bgColor: 'linear-gradient(165deg, #FFF2DF 0%, #DBDCFF 100%)' }}
            goToNextStep={stepper.goToNextStep}
            goToPrevStep={stepper.goToPrevStep}
            canGoBack={stepper.canGoToPrevStep}
            canGoForward={stepper.canGoToNextStep}
          />
        </Fade>
      )}
      {stepper.currentStep === QUIZZ_STEPS.EXERCISE_SCREENING_STEP && (
        <Fade in>
          <QuizMaker
            title="What is the date of this screening?"
            calendar
            bannerTheme={{ bgColor: 'linear-gradient(165deg, #FFF2DF 0%, #DBDCFF 100%)' }}
            goToNextStep={stepper.goToNextStep}
            goToPrevStep={stepper.goToPrevStep}
            canGoBack={stepper.canGoToPrevStep}
            canGoForward={false}
          />
        </Fade>
      )}
    </Box>
  );
}
