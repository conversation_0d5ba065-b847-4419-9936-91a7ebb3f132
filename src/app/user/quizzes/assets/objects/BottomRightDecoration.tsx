import { Box } from '@chakra-ui/react';

export function BottomRightDecoration() {
  return (
    <Box
      position="absolute"
      pointerEvents="none"
      right="0px"
      bottom="0px"
      overflow="hidden"
    >
      <svg
        width="440"
        height="354"
        viewBox="0 0 440 354"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.5">
          <circle
            cx="354"
            cy="354"
            r="235.5"
            stroke="#4956E4"
          />
          <circle
            cx="354"
            cy="354"
            r="294.5"
            stroke="#4956E4"
          />
          <circle
            cx="354"
            cy="354"
            r="353.5"
            stroke="#4956E4"
          />
          <circle
            cx="354"
            cy="354"
            r="176.5"
            stroke="#4956E4"
          />
          <circle
            cx="354"
            cy="354"
            r="117.5"
            stroke="#4956E4"
          />
          <circle
            cx="354"
            cy="354"
            r="58.5"
            stroke="#4956E4"
          />
        </g>
      </svg>
    </Box>
  );
}
