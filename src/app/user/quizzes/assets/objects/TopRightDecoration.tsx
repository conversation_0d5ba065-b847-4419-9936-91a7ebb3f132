import { Box } from '@chakra-ui/react';

export function TopRightDecoration() {
  return (
    <Box
      position="absolute"
      top="187px"
      right="0px"
      pointerEvents="none"
      overflow="hidden"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="140"
        height="185"
        viewBox="0 0 140 185"
        fill="none"
      >
        <path
          d="M31.882 83.8085C31.882 37.9213 69.4738 0.715526 115.998 0.468063L115.998 167.149C69.4737 166.902 31.882 129.696 31.882 83.8085Z"
          stroke="#495AE4"
          strokeWidth="0.933934"
        />
        <path
          d="M83.6146 184.379L152.611 184.379L152.611 65.0965L83.6146 65.0965L83.6146 184.379Z"
          fill="#FF6333"
        />
        <path
          d="M0 117.137L176 117.137L176 108.951L-7.15647e-07 108.951L0 117.137Z"
          fill="white"
        />
      </svg>
    </Box>
  );
}
