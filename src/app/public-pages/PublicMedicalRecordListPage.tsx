import { Box, Container } from '@chakra-ui/react';
import { Suspense } from 'react';

import { BigCircleDecoration, HalfCircleWaveDecoration } from '../../components/ui/Decorations';
import { MedicalRecordList, MedicalRecordListSkeleton } from '../medical-records/MedicalRecordList';

export function PublicMedicalRecordListPage() {
  return (
    <Box py="42px">
      <HalfCircleWaveDecoration />
      <BigCircleDecoration />
      <Container
        position="relative"
        zIndex={1}
        padding={{ base: '0px', md: '16px' }}
      >
        <Suspense fallback={<MedicalRecordListSkeleton />}>
          <MedicalRecordList />
        </Suspense>
      </Container>
    </Box>
  );
}
