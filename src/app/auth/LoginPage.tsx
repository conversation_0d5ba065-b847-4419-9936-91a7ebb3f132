/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useStep } from 'usehooks-ts';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import {
  Box,
  Button,
  ButtonGroup,
  Card,
  CardBody,
  Link as ChakraLink,
  Flex,
  Heading,
  Image,
  Input,
  InputGroup,
  InputLeftElement,
  PinInput,
  PinInputField,
  Text,
  useTheme,
} from '@chakra-ui/react';
import { ChevronRightIcon } from '@chakra-ui/icons';
// Local modules
import CircDecorationLayout from '@components/CircDecorationLayout';
import dayjs from 'dayjs';
import { recordLoginEvents } from '@user/lib/events-analytics-manager';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { Loader } from 'react-feather';

import FieldError from 'src/components/FieldError';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { FluentHealthLogoAnimation } from '../../components/FluentHealthLogoAnimation';
import { useIsMobile } from '../../components/ui/hooks/device.hook';
import { CircleWaveDecoration } from '../../components/ui/Decorations/CircleWaveDecoration';
import { hexOpacity } from '../../components/theme/utils';
// Assets
import { Circle } from '../medical-records/components/add-mr-flow/SuccessStatusStep';
import { pluralize } from '@lib/utils/utils';

import { ReactComponent as ThumbsUPIcon } from '@assets/icons/ThumbsUP.svg';
import QrIcon from '@assets/icons/QR.png';
import { ReactComponent as MultipleEllipse } from '@assets/icons/multiple-ellipse.svg';
// Constants
const LOGIN_STEPS = {
  PHONE_NUMBER: 1,
  PIN_VERIFICATION: 2,
  CODE_VERIFICATION: 3,
  SET_NEW_PIN: 4,
  LOGGING_IN: 5,
  PIN_SUCCESSFULLY_SETUP: 6,
  CODE_VERIFICATION_2FA: 7,
  REDIRECT: 8,
};

type FormValues = {
  phone: string;
};

function CircleDecoration() {
  return (
    <Box
      w="370px"
      h="370px"
      bg="white"
      borderRadius="50%"
      overflow="hidden"
      opacity="0.1"
    />
  );
}

export function LoginPage() {
  const { PRIVACY_POLICY, TERMS_N_CONDITIONS } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const [verificationError, setVerificationError] = useState<any>(null);
  const [phoneError, setPhoneError] = useState<any>(null);
  const [minutes, setMinutes] = useState(1);
  const [seconds, setSeconds] = useState(30);
  const [phone, setPhone] = useState('');
  const [pin, setPin] = useState('');
  const [code, setCode] = useState('');
  const [isPinValid, setIsPinValid] = useState(false);
  const [isOtpValid, setIsOtpValid] = useState(false);
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [pinError, setPinError] = useState<any>(null);
  const [isFetching, setIsFetching] = useState(false);
  const [pinErrorCount, setPinErrorCount] = useState(0);
  const hasInitialized = useRef(false);
  const { trackEventInFlow } = useAnalyticsService();
  const navigate = useNavigate();
  const {
    login,
    loginVerify,
    resendCode,
    verifyMe,
    resetPinGenerateOTP,
    resetPinOTPVerify,
    resetPinSave,
    OTP2FAVerify,
  } = useAuthService();
  // const [searchParams] = useSearchParams();
  const [currentStep, stepper] = useStep(Object.keys(LOGIN_STEPS).length);

  // const redirect = searchParams.get('redirect') || '/';
  const phoneRegExp = /^[0-9]{10}$/;

  const isMobile = useIsMobile();
  const theme = useTheme();

  const form = useForm<FormValues>({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        phone: z.string().regex(phoneRegExp, 'Invalid mobile number'),
      })
    ),
    defaultValues: {
      phone: '',
    },
  });
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, isValid },
  } = form;

  const handleEntryPin = async (value: string) => {
    setPin(value);
    // Enable the button if the PIN length is exactly 6 digits
    if (value.length === 6) {
      setIsPinValid(true);
      setPinError(null); // Clear any previous error
    } else {
      setIsPinValid(false);
      setPinError(null); // Keep error null as this is only a length check
    }
  };

  const handleNewPinChange = (value: any) => {
    setNewPin(value);
    setPinError('');
  };

  // Handle Confirm PIN Change
  const handleConfirmPinChange = (value: any) => {
    setConfirmPin(value);
  };

  const handleForgotPin = async () => {
    stepper.setStep(LOGIN_STEPS.CODE_VERIFICATION);
    recordLoginEvents(trackEventInFlow, {
      EventName: 'PINResetStarted',
    });
    await resetPinGenerateOTP(`91-${phone}`);
  };
  const getRemainingTime = (expiry_date: string): plugin.Duration => {
    const expirationDate = dayjs(expiry_date);
    const diff = expirationDate.diff(dayjs());

    return dayjs.duration(diff);
  };

  const remainingTimeToStringTime = (remainingTime: plugin.Duration): string => {
    let result = '';
    const hours = remainingTime.hours();
    const mins = remainingTime.milliseconds() ? remainingTime.minutes() + 1 : remainingTime.minutes();

    if (hours > 0) {
      result = `${hours} ${pluralize('hour', hours)}`;
    }
    if (mins > 0) {
      result += result.length ? ` ${mins} ${pluralize('minute', mins)}` : `${mins} ${pluralize('minute', mins)}`;
    }

    return result;
  };
  const hasBranchMatchId = () => {
    const { search } = window.location;
    if (/_branch_match_id[=&]/.test(search)) return true;
    const redirectParam = new URLSearchParams(search).get('redirect');
    if (redirectParam) {
      const decodedRedirect = decodeURIComponent(redirectParam);
      return /_branch_match_id[=&]/.test(decodedRedirect);
    }
    return false;
  };
  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    if (currentStep === LOGIN_STEPS.PIN_VERIFICATION) {
      try {
        // Verify code
        await loginVerify(`91-${phone}`, pin);
        stepper.setStep(LOGIN_STEPS.CODE_VERIFICATION_2FA);
        recordLoginEvents(trackEventInFlow, {
          EventName: 'LoginMPINEntered',
        });
      } catch (err: any) {
        const errorMessage = err?.response?.data?.message;
        stepper.setStep(LOGIN_STEPS.PIN_VERIFICATION);
        if (pinErrorCount === 3) {
          recordLoginEvents(trackEventInFlow, { EventName: 'LoginAccountLocked' });
        }
        if (pinErrorCount < 3) {
          recordLoginEvents(trackEventInFlow, {
            EventName: `PINError${pinErrorCount + 1}` as 'PINError1' | 'PINError2' | 'PINError3',
            login_type: 'mpin',
          });
        }
        setPinErrorCount(pinErrorCount + 1);
        setIsPinValid(false);
        setPinError(errorMessage || 'Incorrect PIN. Please try again.');
      }
    }
    if (currentStep === LOGIN_STEPS.PHONE_NUMBER) {
      // Handle phone number submission
      setPhone(data?.phone);
      try {
        await login(`91-${data?.phone}`);
        stepper.setStep(LOGIN_STEPS.PIN_VERIFICATION);
        setVerificationError(null);
        setPhoneError(null);
      } catch (err: any) {
        const remainingTime = err?.sourceError?.accountLockedUntil
          ? remainingTimeToStringTime(getRemainingTime(err.sourceError.accountLockedUntil))
          : null;
        setPhoneError(
          remainingTime
            ? `You have entered 3 incorrect PINs. Your account is locked for ${remainingTime}. Please try again later.`
            : 'No account found. To create an account, download the app using the QR code below.'
        );
        recordLoginEvents(trackEventInFlow, {
          EventName: remainingTime ? 'LoginAccountLocked' : 'LoginMobileNumberError',
        });
      }
    }
    if (currentStep === LOGIN_STEPS.CODE_VERIFICATION_2FA) {
      try {
        await OTP2FAVerify(`91-${phone}`, code, pin);
        // Get user object
        await verifyMe();
        recordLoginEvents(trackEventInFlow, {
          EventName: 'LoginCompleted',
          login_type: '2fa',
        });
        if (hasBranchMatchId()) {
          setTimeout(() => {
            window.location.reload();
            stepper.setStep(LOGIN_STEPS.LOGGING_IN);
          }, 100);
          stepper.setStep(LOGIN_STEPS.REDIRECT);
        } else {
          stepper.setStep(LOGIN_STEPS.LOGGING_IN);
        }
      } catch (error: any) {
        const errorMessage = error?.response?.data?.message;
        const errorCode = error?.response?.data?.errorCode;
        setPinError(errorMessage || 'Failed to set new PIN.');
        let login2FAOTPMessage: 'Login2FAOTPError' | 'Login2FAOTPExpired' = 'Login2FAOTPError';

        if (errorCode === 'error.otp.no_otp') login2FAOTPMessage = 'Login2FAOTPExpired';

        recordLoginEvents(trackEventInFlow, {
          EventName: login2FAOTPMessage,
        });
      }
    }
    if (currentStep === LOGIN_STEPS.CODE_VERIFICATION) {
      try {
        await resetPinOTPVerify(`91-${phone}`, code);
        stepper.setStep(LOGIN_STEPS.SET_NEW_PIN);
        recordLoginEvents(trackEventInFlow, {
          EventName: 'OTPEntered',
          login_type: 'otp',
        });
      } catch (error: any) {
        const errorMessage = error?.response?.data?.message;
        const errorCode = error?.response?.data?.errorCode;
        setPinError(errorMessage || 'Failed to set new PIN.');
        if (errorCode === 'error.otp.no_otp') {
          recordLoginEvents(trackEventInFlow, {
            EventName: 'OTPExpiredError',
            login_type: 'otp',
          });
        } else {
          recordLoginEvents(trackEventInFlow, {
            EventName: 'OTPEnteredError',
            login_type: 'otp',
          });
        }
      }
    }
    if (currentStep === LOGIN_STEPS.SET_NEW_PIN) {
      // Handle new PIN setup
      if (pinError) return; // Prevent submission if PINs don't match
      try {
        await resetPinSave(`91-${phone}`, newPin, code);
        stepper.setStep(LOGIN_STEPS.PIN_SUCCESSFULLY_SETUP);
        recordLoginEvents(trackEventInFlow, {
          EventName: 'PINResetCompleted',
        });
      } catch (error) {
        setPinError('Failed to set new PIN.');
      }
    }
  };
  // For CODE_VERIFICATION
  const verifyCode = async (value: string) => {
    setPinError(null);
    if (value.length === 6) {
      setIsOtpValid(true);
      setCode(value);
    } else {
      setIsOtpValid(false);
    }
  };
  const resendConfirmationCode = async () => {
    try {
      setIsFetching(true);
      setVerificationError(null);
      await resendCode(`91-${phone}`);
      setMinutes(1);
      setSeconds(30);
    } catch (error) {
      setVerificationError('Request was made but no response was received');
    } finally {
      setIsFetching(false);
    }
  };
  useEffect(() => {
    if (currentStep === LOGIN_STEPS.PIN_VERIFICATION) {
      recordLoginEvents(trackEventInFlow, {
        EventName: 'LoginMobileNumberEntered',
      });
    }
    return () => {};
  }, [currentStep]);

  // Validate PINs when they change
  useEffect(() => {
    if (newPin.length === 6 && confirmPin.length === 6 && newPin !== confirmPin) {
      setPinError('PINs don’t match');
    } else {
      setPinError('');
    }
  }, [newPin, confirmPin]);

  // useEffect(() => {
  //   if (currentStep === LOGIN_STEPS.PIN_SUCCESSFULLY_SETUP) {
  //     navigate(0);
  //   }
  // }, [currentStep]);

  useEffect(() => {
    let interval: number = 0;

    if (currentStep === LOGIN_STEPS.CODE_VERIFICATION) {
      interval = setInterval(() => {
        if (seconds > 0) {
          setSeconds(seconds - 1);
        }

        if (seconds === 0) {
          if (minutes === 0) {
            clearInterval(interval);
          } else {
            setSeconds(59);
            setMinutes(minutes - 1);
          }
        }
      }, 3000);
    }
    if (currentStep === LOGIN_STEPS.PIN_SUCCESSFULLY_SETUP) {
      setTimeout(() => {
        navigate(0);
      }, 3000);
    }

    return () => {
      clearInterval(interval);
    };
  }, [seconds, currentStep]);
  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      recordLoginEvents(trackEventInFlow, {
        EventName: 'LoginStarted',
        login_type: 'mpin',
      });
    }
  }, []);
  return (
    <>
      {currentStep === LOGIN_STEPS.REDIRECT && <Loader />}
      {currentStep !== LOGIN_STEPS.REDIRECT && currentStep !== LOGIN_STEPS.PIN_SUCCESSFULLY_SETUP ? (
        <CircDecorationLayout
          showLogo={currentStep !== LOGIN_STEPS.LOGGING_IN}
          showWave={!isMobile && currentStep !== LOGIN_STEPS.LOGGING_IN}
        >
          <>
            {phoneError && (
              <Box
                position="absolute"
                top={isMobile ? '28px' : '100px'}
                bg="red.500"
                marginLeft="27px"
                marginRight="27px"
                color="white"
                p={2}
                borderRadius="md"
                textAlign="center"
              >
                {phoneError}
              </Box>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              {currentStep === LOGIN_STEPS.PHONE_NUMBER && (
                <Card
                  width={{ base: '100%', md: '632px' }}
                  maxWidth="100%"
                  // width="100%"
                  boxShadow="none"
                  borderRadius="20px"
                  py={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : '36px'}
                  px={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : { base: '16px', md: '36px' }}
                  mt={['55px', '106px']}
                  bg="periwinkle.100"
                >
                  <CardBody
                    textAlign="center"
                    p="0"
                  >
                    <Flex
                      direction="column"
                      gap="8px"
                    >
                      <Box>
                        <Heading
                          size="lg"
                          m="auto"
                          fontSize={['28px', '32px']}
                          lineHeight="11"
                        >
                          Log into your Fluent <br /> account
                        </Heading>
                      </Box>
                      <Box
                        mt="16px"
                        mb="4"
                      >
                        <Text
                          lineHeight="short"
                          fontSize="lg"
                          color="gray.500"
                        >
                          Please enter your mobile number.
                        </Text>
                      </Box>
                      <Controller
                        name="phone"
                        control={control}
                        render={({ field, fieldState }) => (
                          <Flex
                            direction="column"
                            alignItems="center"
                          >
                            <Box>
                              <InputGroup
                                variant="flushed"
                                maxW={['325px', '280px']}
                                m="auto"
                              >
                                <InputLeftElement
                                  pointerEvents="none"
                                  color={isSubmitting ? 'fluentHealthText.400' : 'fluentHealthText.200'}
                                  w="72px"
                                  h="100%"
                                  ml="5"
                                  fontSize="lg"
                                >
                                  IN&nbsp;&nbsp;&nbsp;+91
                                </InputLeftElement>
                                <Input
                                  type="number"
                                  mb="1px"
                                  bg="white"
                                  borderRadius="40px"
                                  border="1.5px solid"
                                  borderColor={phoneError ? 'fluentHealthComplementary.Red' : 'fluentHealthText.500'}
                                  height="14"
                                  fontSize="lg"
                                  color="fluentHealthText.100"
                                  style={{ paddingInlineStart: '96px', paddingInlineEnd: '16px' }}
                                  _focus={{ borderColor: 'fluentHealthText.200', boxShadow: 'none' }}
                                  isInvalid={!!fieldState.error}
                                  _invalid={{ boxShadow: 'none' }}
                                  isDisabled={isSubmitting}
                                  {...field}
                                  onChange={(event) => {
                                    // Limit input to 10 digits
                                    const value = event.target.value.slice(0, 10);
                                    // Create a new event object with the limited value
                                    const syntheticEvent = {
                                      ...event,
                                      target: {
                                        ...event.target,
                                        value,
                                      },
                                    };
                                    field.onChange(syntheticEvent);
                                    if (phoneError) {
                                      setPhoneError(null);
                                    }
                                  }}
                                  onBlur={() =>
                                    recordLoginEvents(trackEventInFlow, { EventName: 'LoginMobileNumberEntered' })
                                  }
                                />
                              </InputGroup>
                              <Box
                                mt="12px"
                                mb="4"
                                maxW={['325px', '280px']}
                              >
                                <Text
                                  lineHeight="short"
                                  fontSize="md"
                                  color="fluentHealthText.300"
                                >
                                  By continuing, you consent to Fluent’s
                                  <ChakraLink
                                    as={RouterLink}
                                    to={`/${TERMS_N_CONDITIONS}/${VIEW}`}
                                    color={['#495AE4']}
                                    textDecoration="none"
                                    _hover={{ color: 'blue.700' }}
                                  >
                                    Terms and Conditions
                                  </ChakraLink>
                                  and
                                  <ChakraLink
                                    as={RouterLink}
                                    to={`/${PRIVACY_POLICY}/${VIEW}`}
                                    color={['#495AE4']}
                                    textDecoration="none"
                                    _hover={{ color: 'blue.700' }}
                                  >
                                    Privacy Policy
                                  </ChakraLink>
                                </Text>
                              </Box>
                              {/* <Box maxW="260px">
                                {!isValid && <FieldError error={fieldState.error} />}
                                {isValid && phoneError && <FieldError error={phoneError} />}
                              </Box> */}
                            </Box>
                            <Box pt={['32px', '6']}>
                              <ButtonGroup m="auto">
                                <Button
                                  type="submit"
                                  height="50px"
                                  px="24px"
                                  fontSize="lg"
                                  isLoading={isSubmitting}
                                  isDisabled={!isValid}
                                  rightIcon={
                                    <ChevronRightIcon
                                      transition="margin .4s ease"
                                      _groupHover={{ ml: '14px' }}
                                    />
                                  }
                                >
                                  Continue
                                </Button>
                              </ButtonGroup>
                            </Box>
                          </Flex>
                        )}
                      />
                    </Flex>
                  </CardBody>
                </Card>
              )}
              {currentStep === LOGIN_STEPS.PIN_VERIFICATION && (
                <Card
                  width={{ base: '100%', md: '632px' }}
                  maxWidth="100%"
                  boxShadow="none"
                  borderRadius="20px"
                  py={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : '36px'}
                  px={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : { base: '16px', md: '36px' }}
                  mt={['55px', '106px']}
                  bg="periwinkle.100"
                >
                  <CardBody
                    textAlign="center"
                    p="0"
                  >
                    <Flex
                      direction="column"
                      gap="8px"
                    >
                      <Flex
                        flexDirection="column"
                        alignItems="center"
                      >
                        <Box>
                          <Heading
                            size="lg"
                            fontSize="32px"
                            lineHeight="11"
                          >
                            Enter your Fluent PIN
                          </Heading>
                        </Box>
                        <Box
                          maxWidth={['280px', '395px']}
                          width="auto"
                        >
                          <Text
                            fontSize="lg"
                            lineHeight="short"
                            color="fluentHealthText.100"
                            paddingTop="12px"
                          >
                            Log in with your 6-digit PIN
                          </Text>
                        </Box>
                        <Box
                          m="auto"
                          mt={['32px', '40px']}
                          maxW="424px"
                        >
                          <Flex
                            gap="2"
                            justify="center"
                          >
                            <PinInput
                              mask
                              otp
                              autoFocus
                              placeholder=""
                              onChange={handleEntryPin}
                              isInvalid={!!pinError}
                              isDisabled={pinErrorCount === 4}
                            >
                              {[0, 1, 2, 3, 4, 5].map((item) => (
                                <PinInputField
                                  key={item}
                                  // Display masked value instantly
                                  type="password"
                                  width="calc(100% / 6)"
                                  bg="white"
                                  maxW={['48px', '64px']}
                                  height={['60px', '80px']}
                                  fontSize={['48px', '64px']}
                                  _invalid={{
                                    color: 'red',
                                    borderColor: 'red',
                                    boxShadow: '0 0 0 3px rgba(255, 0, 0, 0.1)',
                                  }}
                                />
                              ))}
                            </PinInput>
                          </Flex>
                        </Box>
                        <Box
                          mt="8px"
                          maxW="424px"
                        >
                          {pinError && <FieldError error={pinError} />}
                        </Box>
                        {pinErrorCount !== 4 && (
                          <Box>
                            <Button
                              size="md"
                              variant="ghost"
                              onClick={handleForgotPin}
                              isDisabled={isFetching}
                              fontWeight="semibold"
                              color="fluentHealth.500"
                            >
                              Forgot PIN ?
                            </Button>
                          </Box>
                        )}
                        <Box pt={['64px', '20']}>
                          <ButtonGroup m="auto">
                            <Button
                              type="submit"
                              height="50px"
                              px="24px"
                              fontSize="lg"
                              isLoading={isSubmitting}
                              isDisabled={!isPinValid}
                            >
                              Verify
                            </Button>
                          </ButtonGroup>
                        </Box>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              )}
              {currentStep === LOGIN_STEPS.CODE_VERIFICATION_2FA && (
                <Card
                  width={{ base: '100%', md: '632px' }}
                  maxWidth="100%"
                  boxShadow="none"
                  borderRadius="20px"
                  py={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : '36px'}
                  px={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : { base: '16px', md: '36px' }}
                  mt={['55px', '106px']}
                  bg="periwinkle.100"
                >
                  <CardBody
                    textAlign="center"
                    p="0"
                  >
                    <Flex
                      direction="column"
                      gap="8px"
                    >
                      <Flex
                        flexDirection="column"
                        alignItems="center"
                      >
                        <Box>
                          <Heading
                            size="lg"
                            fontSize="32px"
                            lineHeight="11"
                          >
                            Enter OTP
                          </Heading>
                        </Box>
                        <Box
                          maxWidth={['280px', '395px']}
                          width="auto"
                        >
                          <Text
                            fontSize="lg"
                            lineHeight="short"
                            color="charcoal.100"
                            paddingTop="12px"
                          >
                            An OTP has been sent to your mobile <br /> {`+91 ${phone}`}
                          </Text>
                        </Box>
                        <Box
                          m="auto"
                          mt={['32px', '40px']}
                          maxW="424px"
                        >
                          <Flex
                            gap="2"
                            justify="center"
                          >
                            <PinInput
                              otp
                              autoFocus
                              placeholder=""
                              onChange={(value) => verifyCode(value)}
                              onComplete={(value) => verifyCode(value)}
                              isInvalid={pinError}
                            >
                              {[0, 1, 2, 3, 4, 5].map((item) => (
                                <PinInputField
                                  key={item}
                                  width="calc(100% / 6)"
                                  bg="white"
                                  maxW={['48px', '64px']}
                                  height={['60px', '80px']}
                                  fontSize={['48px', '64px']}
                                  _invalid={{
                                    color: 'red',
                                    borderColor: 'red',
                                    boxShadow: '0 0 0 3px rgba(255, 0, 0, 0.1)',
                                  }}
                                />
                              ))}
                            </PinInput>
                          </Flex>
                        </Box>
                        <Box> {pinError && <FieldError error={pinError} />}</Box>
                        <Box mb="36px">
                          <Flex alignItems="center">
                            <Text
                              fontSize="lg"
                              lineHeight="short"
                              color="charcoal.60"
                            >
                              Didn't get it?
                            </Text>
                            <Button
                              paddingLeft="5px"
                              size="md"
                              variant="ghost"
                              onClick={() => {
                                resendConfirmationCode();
                                recordLoginEvents(trackEventInFlow, {
                                  EventName: 'Login2FAOTPResendClicked',
                                  login_type: 'otp',
                                });
                              }}
                              isDisabled={isFetching}
                              fontWeight="semibold"
                              color="fluentHealth.500"
                            >
                              Resend OTP
                            </Button>
                          </Flex>
                        </Box>
                        <Box pt={['32px', '6']}>
                          <ButtonGroup m="auto">
                            <Button
                              type="submit"
                              height="50px"
                              px="24px"
                              fontSize="lg"
                              isLoading={isSubmitting}
                              isDisabled={!isOtpValid}
                            >
                              Login
                            </Button>
                          </ButtonGroup>
                        </Box>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              )}
              {currentStep === LOGIN_STEPS.CODE_VERIFICATION && (
                <Card
                  width={{ base: '100%', md: '632px' }}
                  maxWidth="100%"
                  boxShadow="none"
                  borderRadius="20px"
                  py={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : '36px'}
                  px={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : { base: '16px', md: '36px' }}
                  mt={['55px', '106px']}
                  bg="periwinkle.100"
                >
                  <CardBody
                    textAlign="center"
                    p="0"
                  >
                    <Flex
                      direction="column"
                      gap="8px"
                    >
                      <Flex
                        flexDirection="column"
                        alignItems="center"
                      >
                        <Box>
                          <Heading
                            size="lg"
                            fontSize="32px"
                            lineHeight="11"
                          >
                            Enter OTP
                          </Heading>
                        </Box>
                        <Box
                          maxWidth={['280px', '395px']}
                          width="auto"
                        >
                          <Text
                            fontSize="lg"
                            lineHeight="short"
                            color="charcoal.100"
                            paddingTop="12px"
                          >
                            An OTP has been sent to your mobile <br />
                            {`+91 ${phone}`}
                          </Text>
                        </Box>
                        <Box
                          m="auto"
                          mt={['32px', '40px']}
                          maxW="424px"
                        >
                          <Flex
                            gap="2"
                            justify="center"
                          >
                            <PinInput
                              otp
                              autoFocus
                              placeholder=""
                              onChange={(value) => verifyCode(value)}
                              onComplete={(value) => verifyCode(value)}
                              isInvalid={pinError}
                            >
                              {[0, 1, 2, 3, 4, 5].map((item) => (
                                <PinInputField
                                  key={item}
                                  width="calc(100% / 6)"
                                  bg="white"
                                  maxW={['48px', '64px']}
                                  height={['60px', '80px']}
                                  fontSize={['48px', '64px']}
                                  _invalid={{
                                    color: 'red',
                                    borderColor: 'red',
                                    boxShadow: '0 0 0 3px rgba(255, 0, 0, 0.1)',
                                  }}
                                />
                              ))}
                            </PinInput>
                          </Flex>
                        </Box>
                        <Box> {pinError && <FieldError error={pinError} />}</Box>
                        <Box mb="36px">
                          <Flex alignItems="center">
                            <Text
                              fontSize="lg"
                              lineHeight="short"
                              color="charcoal.60"
                            >
                              Didn't get it?
                            </Text>
                            <Button
                              paddingLeft="5px"
                              size="md"
                              variant="ghost"
                              onClick={() => {
                                resendConfirmationCode();
                                recordLoginEvents(trackEventInFlow, {
                                  EventName: 'ResendOTPClicked',
                                  login_type: 'otp',
                                });
                              }}
                              isDisabled={isFetching}
                              fontWeight="semibold"
                              color="fluentHealth.500"
                            >
                              Resend OTP
                            </Button>
                          </Flex>
                        </Box>
                        <Box pt={['32px', '6']}>
                          <ButtonGroup m="auto">
                            <Button
                              type="submit"
                              height="50px"
                              px="24px"
                              fontSize="lg"
                              isLoading={isSubmitting}
                              isDisabled={!isOtpValid}
                            >
                              Verify
                            </Button>
                          </ButtonGroup>
                        </Box>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              )}
              {currentStep === LOGIN_STEPS.SET_NEW_PIN && (
                <Card
                  width={{ base: '100%', md: '632px' }}
                  maxWidth="100%"
                  boxShadow="none"
                  borderRadius="20px"
                  py={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : '36px'}
                  px={currentStep === LOGIN_STEPS.LOGGING_IN ? '0' : { base: '16px', md: '36px' }}
                  mt={['55px', '106px']}
                  bg="periwinkle.100"
                >
                  <CardBody
                    textAlign="center"
                    p="0"
                  >
                    <Flex
                      direction="column"
                      gap="8px"
                    >
                      <Flex
                        flexDirection="column"
                        alignItems="center"
                      >
                        <Box>
                          <Heading
                            size="lg"
                            fontSize="32px"
                            lineHeight="11"
                          >
                            Reset Fluent PIN
                          </Heading>
                        </Box>
                        <Flex
                          flexDirection="column"
                          alignItems="center"
                        >
                          <Box
                            maxWidth={['280px', '395px']}
                            width="auto"
                          >
                            <Text
                              fontSize="lg"
                              lineHeight="short"
                              color="fluentHealthText.100"
                              paddingTop="12px"
                            >
                              New PIN
                            </Text>
                          </Box>
                          <Box
                            m="auto"
                            mt={['12px', '16px']}
                            maxW="424px"
                          >
                            <Flex
                              gap="2"
                              justify="center"
                            >
                              <PinInput
                                mask
                                otp
                                placeholder=""
                                onChange={handleNewPinChange}
                                isInvalid={pinError}
                              >
                                {[...Array(6)].map((_, i) => (
                                  <PinInputField
                                    key={i}
                                    width="calc(100% / 6)"
                                    bg="white"
                                    maxW={['48px', '64px']}
                                    height={['60px', '80px']}
                                    fontSize={['48px', '64px']}
                                    _invalid={{
                                      color: 'red',
                                      borderColor: 'red',
                                      boxShadow: '0 0 0 3px rgba(255, 0, 0, 0.1)',
                                    }}
                                  />
                                ))}
                              </PinInput>
                            </Flex>
                          </Box>
                          <Box
                            maxWidth={['280px', '395px']}
                            width="auto"
                          >
                            <Text
                              fontSize="lg"
                              lineHeight="short"
                              color="fluentHealthText.100"
                              paddingTop="12px"
                            >
                              Confirm New PIN
                            </Text>
                          </Box>
                          <Box
                            m="auto"
                            mt={['12px', '16px']}
                            maxW="424px"
                          >
                            <Flex
                              gap="2"
                              justify="center"
                            >
                              <PinInput
                                mask
                                otp
                                placeholder=""
                                onChange={handleConfirmPinChange}
                                isInvalid={pinError}
                              >
                                {[...Array(6)].map((_, i) => (
                                  <PinInputField
                                    key={i}
                                    width="calc(100% / 6)"
                                    bg="white"
                                    maxW={['48px', '64px']}
                                    height={['60px', '80px']}
                                    fontSize={['48px', '64px']}
                                    _invalid={{
                                      color: 'red',
                                      borderColor: 'red',
                                      boxShadow: '0 0 0 3px rgba(255, 0, 0, 0.1)',
                                    }}
                                  />
                                ))}
                              </PinInput>
                            </Flex>
                          </Box>

                          <Box mb="36px">{pinError && <FieldError error={pinError} />}</Box>

                          <Box>
                            <ButtonGroup m="auto">
                              <Button
                                type="submit"
                                height="50px"
                                px="24px"
                                fontSize="lg"
                                isLoading={isSubmitting}
                                isDisabled={!(newPin.length === 6) || !(confirmPin.length === 6) || !!pinError}
                              >
                                Set New PIN
                              </Button>
                            </ButtonGroup>
                          </Box>
                        </Flex>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              )}

              {currentStep === LOGIN_STEPS.LOGGING_IN && (
                <>
                  {!isMobile && (
                    <CircleWaveDecoration
                      position="fixed"
                      top="unset"
                      bottom="-773px"
                      width="1546px"
                      left="-773px"
                      right="-773px"
                      margin="auto"
                      color={hexOpacity(theme.colors.fluentHealth[500], 0.2)}
                      strokeWidth="0.5px"
                    />
                  )}
                  <Flex
                    position="fixed"
                    inset={0}
                    direction="column"
                    align="center"
                    justify="center"
                    gap="24px"
                    mt="-100px"
                  >
                    <FluentHealthLogoAnimation />
                    <Heading
                      fontSize="2xl"
                      color="fluentHealthSecondary.100"
                    >
                      Logging you in...
                    </Heading>
                  </Flex>
                </>
              )}
              {/* <Box
                position="relative"
                display="flex"
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
                mt="20px"
                maxWidth="632px"
                bgColor="iris.500"
                borderRadius="20px"
                py="16px"
                pr={{ base: '26px', md: '16px' }}
                pl={{ base: '26px', md: '30px' }}
                overflow="hidden"
                visibility={
                  currentStep === LOGIN_STEPS.PHONE_NUMBER || currentStep === LOGIN_STEPS.PIN_VERIFICATION
                    ? 'visible'
                    : 'hidden'
                }
              >
                <Box
                  position="absolute"
                  top={['66px', '10px']}
                  right={['160px', '356px']}
                  pointerEvents="none"
                  width="352px"
                  height="347px"
                >
                  <MultipleEllipse
                    width="100%"
                    height="100%"
                    color={hexOpacity('#FFFFFF', 0.1)}
                  />
                </Box>
                <Flex
                  direction={['column', 'row']}
                  gap={['24px', '104px']}
                  justifyContent="center"
                  alignItems="center"
                >
                  <Text
                    color="white"
                    fontSize="24px"
                    fontWeight="400"
                    lineHeight="32px"
                    fontFamily="P22 Mackinac"
                    letterSpacing="-0.48px"
                  >
                    To create a Fluent account please download our mobile app.
                  </Text>
                  <Flex>
                    <Image src={QrIcon} />
                  </Flex>
                </Flex>
                <Box
                  position="absolute"
                  bottom={['160px', '32px']}
                  left={['140px', '305px']}
                  width={['294px', '370px']}
                  height={['294px', '370px']}
                  pointerEvents="none"
                >
                  <CircleDecoration />
                </Box>
              </Box> */}
            </form>
          </>
        </CircDecorationLayout>
      ) : (
        <>
          <Circle
            bottom="40%"
            left="166px"
          />
          <Circle
            top="30%"
            left="-361px"
            width="1774px"
            height="1774px"
          />
          <Flex
            position="fixed"
            inset={0}
            direction="column"
            alignItems="center"
            justify="center"
            gap="24px"
          >
            <ThumbsUPIcon />
            <Heading
              fontSize="3xl"
              color="fluentHealth.100"
              width="325px"
              fontWeight="normal"
              textAlign="center"
            >
              Your new Fluent PIN has been successfully set up!
            </Heading>
          </Flex>
        </>
      )}
    </>
  );
}
