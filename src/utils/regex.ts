export const phoneNumberRegex = /^(\d{5}\s?\d{5})$/;
export const cleanPhoneNumberRegex = /^\d{10}$/;

/**
 * Creates a regular expression for validating weight input.
 *
 * This function generates a regex pattern to validate weight inputs with optional constraints on
 * the maximum number of integer and decimal characters allowed.
 *
 * @param {number} [maxCharacters] - Optional. Maximum number of integer characters allowed.
 *                                   If not provided, any number of integer characters is allowed.
 * @param {number} [maxDecimalPoints] - Optional. Maximum number of decimal points allowed.
 *                                      If not provided, any number of decimal points is allowed.
 * @returns {RegExp} - The regular expression to validate the weight input.
 *
 * @example
 * // Creates a regex to allow up to 3 integer digits and 2 decimal places
 * const weightRegex = createWeightRegex(3, 2);
 *
 * // Example outputs:
 * // weightRegex.test('123')       // true
 * // weightRegex.test('123.45')    // true
 * // weightRegex.test('1234')      // false (exceeds 3 integer digits)
 * // weightRegex.test('123.456')   // false (exceeds 2 decimal places)
 * // weightRegex.test('12.3')      // true
 */
export const createWeightRegex = (maxCharacters?: number, maxDecimalPoints?: number): RegExp => {
  let regex = '^\\d*';
  if (maxCharacters) {
    regex = `^\\d{0,${maxCharacters}}`;
  }
  if (maxDecimalPoints !== undefined) {
    regex += `(\\.\\d{0,${maxDecimalPoints}})?$`;
  } else {
    regex += '(\\.\\d*)?$';
  }
  return new RegExp(regex);
};
