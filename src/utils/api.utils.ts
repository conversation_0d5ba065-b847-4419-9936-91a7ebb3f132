export class ApiUtils {
  public static defaultErrorMessage = 'An unknown error has occurred. Please try again later.';

  public static convertApiErrorToString(error: any) {
    if (typeof error === 'string') return error;
    if (error?.message === 'Failed to fetch') {
      return 'There was a problem with your request. Please try again later!';
    }
    if (typeof error === 'object' && error.error?.errors?.length) {
      return error.error.errors[0];
    }
    return error?.error?.message ?? error?.message ?? ApiUtils.defaultErrorMessage;
  }
}
