// This polyfill was added to support older versions of Safari - https://github.com/wojtekmaj/react-pdf/issues/1465
import 'core-js/modules/es.array.at';

// Local modules
import { Client, Exchange, Provider, fetchExchange } from 'urql';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
// Package modules
import { Suspense } from 'react';
import { ChakraProvider } from '@chakra-ui/react';

import { theme } from './components/theme/theme';
import { AppLayout, PublicAppLayout } from './components/AppLayout';
import { AuthService } from '@lib/authService';
import { ErrorBoundary } from './components/error/ErrorBoundary';
import { FluentHealthLoadingScreen } from './components/FluentHealthLoadingScreen';
import { MEDPLUM_API_URL } from '@lib/constants';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      suspense: true,
    },
  },
});

const client = new Client({
  url: `${MEDPLUM_API_URL}/fhir/R4/$graphql`,
  exchanges: [fetchExchange as Exchange],
  fetchOptions: () => ({ headers: AuthService.instance.withAuthHeader() }),
});

function Root() {
  AuthService.instance.setQueryClient(queryClient);

  return (
    <Provider value={client}>
      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <Suspense fallback={<FluentHealthLoadingScreen />}>
            <ErrorBoundary>
              <AppLayout />
            </ErrorBoundary>
          </Suspense>
        </ChakraProvider>
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-left"
        />
      </QueryClientProvider>
    </Provider>
  );
}

export function PublicRoot() {
  AuthService.instance.setQueryClient(queryClient);

  return (
    <QueryClientProvider client={queryClient}>
      <ChakraProvider theme={theme}>
        <Suspense fallback={<FluentHealthLoadingScreen />}>
          <ErrorBoundary>
            <PublicAppLayout />
          </ErrorBoundary>
        </Suspense>
      </ChakraProvider>
      <ReactQueryDevtools
        initialIsOpen={false}
        position="bottom-right"
      />
    </QueryClientProvider>
  );
}

export default Root;
