// Package modules
import axios from 'axios';

// Local modules
// eslint-disable-next-line import/no-cycle
import { AuthService } from './authService';
import { Patient } from './models/patient';
import ApiError from '../components/error/ApiError';
import {
  // API_GATEWAY_AUTH_PROXY_URL,
  AM_AUTH_TOKEN_URL,
  API_GATEWAY_URL,
  CLIENT_ID,
  GATEWAY_BASIC_AUTHORIZATION_USERNAME,
  GIO_AUTH_DOMAIN_NAME,
  LOGIN_MODES,
  MEDPLUM_API_URL,
  TOKEN_GRANT_TYPES,
} from './constants';
import { Alert, AlertPayload } from '@lib/models/alert';
import { medplumApi } from '../app/user/lib/medplum-api';
import { Bundle } from 'src/gql/graphql';

const auth = {
  async me(username: string) {
    const { data: gatewayUser, status } = await axios.post(
      `${API_GATEWAY_URL}/auth-service/v2/user/get`,
      {
        username,
        client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200 || gatewayUser.error === true) {
      throw new ApiError(gatewayUser);
    }

    const patientId = gatewayUser?.additionalInformation?.medplum_id;
    const { data: patient }: any = await medplumApi.patientInfo.getPatient(patientId);

    patient.gatewayUser = gatewayUser;
    localStorage.setItem('userId', gatewayUser.id);
    return patient as Patient;
  },
  // login Phone Number Verify
  async login(username: string) {
    localStorage.setItem('username', username);
    const { data, status } = await axios.post(`${API_GATEWAY_URL}/auth-service/v2/user/validate`, {
      username,
      client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
    });

    if (status !== 200 || !data?.userExists || data.accountLockedUntil >= Date.now()) {
      // || !data?.id
      throw new ApiError(data);
    }
    return data;
  },
  // login Pin Verify
  async loginVerify(phoneNumber: string, code: string) {
    type ResponseType = {
      access_token: string;
      refresh_token: string;
    };

    const { data, status } = await axios.post(`${API_GATEWAY_URL}/auth-service/v2/user/login`, {
      phoneNumber,
      mode: LOGIN_MODES.GENERATE,
      pin: code,
      client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
    });

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as ResponseType;
  },
  async OTP2FAVerify(phoneNumber: string, code: string, pin: string) {
    type ResponseType = {
      access_token: string;
      refresh_token: string;
    };

    const { data, status } = await axios.post(`${API_GATEWAY_URL}/auth-service/v2/user/login`, {
      phoneNumber,
      mode: LOGIN_MODES.VERIFY,
      pin,
      otp: code,
      client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
    });

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as ResponseType;
  },
  async resetPinGenerateOTP(phoneNumber: string) {
    type ResponseType = {
      access_token: string;
      refresh_token: string;
    };

    const { data, status } = await axios.post(
      `${API_GATEWAY_URL}/auth-service/v2/otp/generate`,
      {
        phoneNumber,
        mode: LOGIN_MODES.GENERATE,
        receiverType: 'sms',
        client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as ResponseType;
  },
  async resetPinOTPVerify(phoneNumber: string, code: string) {
    type ResponseType = {
      access_token: string;
      refresh_token: string;
    };

    const { data, status } = await axios.post(
      `${API_GATEWAY_URL}/auth-service/v2/otp/verify`,
      {
        phoneNumber,
        mode: LOGIN_MODES.VERIFY,
        otp: code,
        receiverType: 'sms',
        // client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as ResponseType;
  },
  async resetPinSave(phoneNumber: string, pinCode: string, otpCode: string) {
    type ResponseType = {
      access_token: string;
      refresh_token: string;
    };

    const { data, status } = await axios.post(
      `${API_GATEWAY_URL}/auth-service/v2/user/reset-pin`,
      {
        phoneNumber,
        mode: LOGIN_MODES.VERIFY,
        pin: pinCode,
        otp: otpCode,
        receiverType: 'sms',
        client_id: GATEWAY_BASIC_AUTHORIZATION_USERNAME,
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as ResponseType;
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  async resendCode() {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  async logout() {},
  async refreshTokenApi(refresh_token: string) {
    type ResponseType = {
      success: boolean;
      access_token: string;
      refresh_token: string;
    };
    const { data, status } = await axios.post(
      `${AM_AUTH_TOKEN_URL}/${GIO_AUTH_DOMAIN_NAME}/oauth/token`,
      {
        grant_type: TOKEN_GRANT_TYPES.REFRESH_TOKEN,
        refresh_token,
        client_id: CLIENT_ID,
      },
      {
        headers: AuthService.instance.withApiGatewayAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return { ...data, refresh_token: data.refresh_token } as ResponseType;
  },
};

const alerts = {
  async addAlert(payload: AlertPayload) {
    const { data, status } = await axios.post<Alert>(`${MEDPLUM_API_URL}/fhir/R4/CommunicationRequest`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 201) {
      throw new ApiError(data);
    }

    return data;
  },
  async updateAlert(alertId: Alert['id'], payload: AlertPayload) {
    const { data, status } = await axios.put<Alert>(
      `${MEDPLUM_API_URL}/fhir/R4/CommunicationRequest/${alertId}`,
      payload,
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },

  async linkCommRequestWithDocRef(commReqId: string | null | undefined, docRefId: string) {
    const payload = [
      {
        op: 'add',
        path: '/context/related/-',
        value: {
          reference: `CommunicationRequest/${commReqId}`,
        },
      },
    ];

    const { data, status } = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/DocumentReference/${docRefId}`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },

  async deleteAlert(alertId: Alert['id']) {
    const payload = {
      resourceType: 'Bundle',
      type: 'batch',
      entry: [
        {
          request: {
            method: 'DELETE',
            url: `CommunicationRequest/${alertId}`,
          },
        },
      ],
    };
    const { data, status } = await axios.post<Bundle>(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
};

export const fluentAPI = axios.create({
  baseURL: API_GATEWAY_URL,
});

fluentAPI.interceptors.request.use((config: any) => {
  return {
    ...config,
    headers: {
      ...config.headers,
      ...AuthService.instance.withAuthHeader(),
    },
  };
});

export const api = {
  auth,
  alerts,
};
