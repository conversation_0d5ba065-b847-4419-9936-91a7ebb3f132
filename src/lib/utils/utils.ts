import React, { RefObject, useEffect, useState } from 'react';
import UniversalCookie from 'universal-cookie';
import create from 'zustand';
import { useValueSetQuestionnaire } from '@user/lib/medplum-state';
import { preventativeScreeningConstants } from '@user/lib/constants';
import { FieldError, FieldErrorsImpl, Merge } from 'react-hook-form';
import dayjs from 'dayjs';

import { Patient, Questionnaire, ValueSet } from 'src/gql/graphql';
import { PatientName } from '@lib/models/patient';
import { PUBLIC_URL_PREFIX, TEMPORARY_TOKEN_QUERY_KEY } from '../constants';
import { FH_STRUCTURE_DEFINITION_CLINICALSTATUS } from 'src/constants/medplumConstants';

/**
 * Returns a function that sorts an array of objects by the provided property name
 * @param {string} prop the name of the property name to sort by
 * @param sort
 * @returns a sorted array
 */
export const sortByProp =
  (prop: string, sort: 'ASC' | 'DESC' = 'DESC') =>
  (a: any, b: any) => {
    if (sort === 'DESC') {
      return b[prop]?.toString()?.localeCompare(a[prop]?.toString(), 'en', { numeric: true });
    }
    return a[prop]?.toString()?.localeCompare(b[prop]?.toString(), 'en', { numeric: true });
  };

export const pluralize = (noun: string, count: number, suffix = 's') => `${noun}${count !== 1 ? suffix : ''}`;

/**
 * Auto-expands a textarea element to fit its content.
 * @param element
 */
export const autoExpandTextareaElement = (element: HTMLTextAreaElement): void => {
  const elementCopy = element;
  elementCopy.style.height = '0px';
  elementCopy.style.height = `${Math.min(elementCopy.scrollHeight, 300)}px`;
};

/**
 * Converts the uploaded file to base64 format.
 * @param file - The file to convert.
 * @returns A Promise that resolves with the base64 string of the file content.
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const base64String = (reader.result as string).split(',')[1];
      resolve(base64String);
    };

    reader.onerror = (error) => {
      reject(error);
    };

    reader.readAsDataURL(file);
  });
};

export const capitalizeFirstLetter = (string: any) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

export const parsePatientName = (patientName: Patient['name'] | string, failMessage = 'No name') => {
  // On shared page the Public API returns already formatted patient name
  if (typeof patientName === 'string') {
    return patientName.trim();
  }

  if (patientName?.length) {
    const nameObj = patientName[0];
    const givenNames = (nameObj?.given ?? []).join(' ').trim();
    const familyName = nameObj?.family ?? '';
    return `${givenNames} ${familyName}`.trim();
  }
  return failMessage;
};

export const parseUrlQuestionnaireList = (masterList: Questionnaire['item']) => {
  const urls = masterList
    ?.filter((masterItem: any) => masterItem?.answerValueSet)
    ?.map((masterItem: any) => {
      return masterItem?.answerValueSet;
    });
  return urls as string[];
};

export const parseValueSet = (valueSet: ValueSet[]) => {
  const parsedValueSet = valueSet?.[0]?.compose?.include
    ?.map((e) => {
      return e.concept;
    })
    ?.flat()
    ?.map((e: any) => ({ label: e.display, value: e.code }));
  return parsedValueSet || [];
};

export const getValueSetByMasterList = (masterList: Questionnaire[], url: string, custom?: any) => {
  const urls: string[] = parseUrlQuestionnaireList(masterList?.[0]?.item);
  const data = useValueSetQuestionnaire(urls);
  const key = custom || url;
  const valueSetData = data?.find((item) => Object.keys(item)[0] === key)?.[key];
  return parseValueSet(valueSetData ?? []);
};

// export const getValueSetForMedication = (searchText: string) => {
//   const data = useValueSetMedication(searchText);
//   const valueSet = data.map((e: any) => ({ label: e.display, value: e.code }));
//   return valueSet;
// };

export const parseValueSetValue = (value: any, valueSet: any, isMedicationSelect?: boolean) => {
  const label = valueSet?.find((e: any) => e.value === value)?.label;
  return isMedicationSelect ? label?.replace(` ${label.substr(label.lastIndexOf('['))}`, '') || '' : label || '';
};

/**
 * Filter relationship object to remove specific relationship types.
 * @param {any} relationship - The relationship object to filter.
 * @returns {Array<any>} The filtered relationship object.
 */
export const filterRelationship = (relationship: any) => {
  return relationship?.filter(
    (e: any) =>
      !(e.coding[0]?.code === 'C' && e.coding[0]?.system === 'http://terminology.hl7.org/CodeSystem/v2-0131') &&
      !(
        e.coding[0]?.code === 'HPOWATT' && e.coding[0]?.system === 'http://terminology.hl7.org/CodeSystem/v3-RoleCode'
      ) &&
      !(e.coding[0]?.code === 'FM' && e.coding[0]?.system === 'http://terminology.hl7.org/CodeSystem/v3-RoleCode')
  );
};

/**
 * Parse relationship object to extract relationship display.
 * @param {any} relationship - The relationship object to parse.
 * @param {string} failMessage - The message to return if parsing fails.
 * @returns {string} The parsed relationship display, or failMessage if parsing fails.
 */
export const parseRelation = (relationship: any, failMessage = '') => {
  const filteredRelationship = filterRelationship(relationship);
  if (filteredRelationship?.length) {
    return filteredRelationship[0].coding[0]?.display
      ? capitalizeFirstLetter(filteredRelationship[0].coding[0]?.display)
      : failMessage;
  }
  return failMessage;
};

/**
 * Parse relationship object to extract relationship code.
 * @param {any} relationship - The relationship object to parse.
 * @param {string} failMessage - The message to return if parsing fails.
 * @returns {string} The parsed relationship code, or failMessage if parsing fails.
 */
export const parseRelationship = (relationship: any, failMessage = '') => {
  const filteredRelationship = filterRelationship(relationship);
  if (filteredRelationship?.length) {
    return filteredRelationship[0].coding[0]?.code ? filteredRelationship[0].coding[0]?.code : failMessage;
  }
  return failMessage;
};

/**
 * Format phone number by removing non-digit characters and adding a space between the 5th and 6th digit.
 * @param {string} phoneNumber - The phone number to format.
 * @returns {string} The formatted phone number.
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  // Remove any non-digit characters
  const formattedNumber = phoneNumber.replace(/\D/g, '');
  return formattedNumber;
};

export const parsePhoneNo = (telecom: any) => {
  const phone = telecom?.find((e: any) => e.system === 'phone')?.value ?? '';
  return formatPhoneNumber(phone);
};

/**
 * Format phone number by removing non-digit characters and adding a space between the 5th and 6th digit.
 * @param {string} phoneNumber - The phone number to format.
 * @returns {string} The formatted phone number.
 */
export const cleanPhoneNo = (telecom: string): string => {
  // Removing all spaces
  const cleanedPhone = telecom.replace(/\s+/g, '');
  return cleanedPhone;
};

export const parseContactName = (contactName: PatientName | string | undefined, failMessage = 'No name') => {
  // On shared page the Public API returns already formatted patient name
  if (typeof contactName === 'string') {
    return contactName.trim();
  }

  let result = '';

  /* For full name, i.e. Bradley Justin BROWN
  if (contactName?.given && Array.isArray(contactName?.given)) {
    contactName.given.forEach((givenName) => {
      if (givenName) {
        result += ` ${givenName}`;
      }
    });
  }
  */

  if (contactName?.text) {
    result += contactName.text;
  }

  if (contactName?.family) {
    result += ` ${contactName.family}`;
  }

  return result.trim().length > 0 ? result.trim() : failMessage;
};

export const parsePatientEmail = (telecom: Patient['telecom'], failMessage = 'No email') => {
  const email = telecom?.find((t) => t.system === 'email')?.value;
  if (email && email.length > 0) {
    return email;
  }
  return failMessage;
};

/**
 * Parses patient's address and returns a formatted string with city and country info.
 * @param {Patient['address']} address - The patient's address object (city and country info).
 * @param {string} [failMessage=''] - Optional message returned if address is empty or cannot be parsed.
 * @returns {string} - Formatted string with city and country or `failMessage` if address is empty or invalid.
 */
export const parsePatientAddress = (address: Patient['address'], failMessage = '') => {
  let result = address?.[0].city ?? '';
  result += address?.[0].country ? `, ${address?.[0].country}` : '';

  return result.length === 0 ? failMessage : result;
};

/**
 * This function generates a shareable link for a specific patient, incorporating various parameters and a temporary token.
 * Example:
 *    generatePatientPublicUrl('medical-records', 'type=own');
 *    // Output: "https://example.com/public/medical-records?type=own&temporary_token=<token>"
 * @param suffixPath
 * @param queryParams
 * @param tempToken
 */
export const generatePatientPublicUrl = (
  suffixPath: string = '',
  queryParams: string = '',
  tempToken: string | null = null
) => {
  const cookies = new UniversalCookie();
  const token = tempToken || cookies.get('token') || null;
  return `${window.location.origin}${PUBLIC_URL_PREFIX}/${suffixPath}?${TEMPORARY_TOKEN_QUERY_KEY}=${token}&${queryParams}`;
};

/**
 * This function adds an article to a string, based on the first letter of the string.
 * @param text
 */
export const addArticle = (text: string) => {
  if (!text) return text;
  const vowels = ['a', 'e', 'i', 'o', 'u'];
  const firstLetter = text[0].toLowerCase();
  return vowels.includes(firstLetter) ? `an ${text}` : `a ${text}`;
};

// Returns a function, that, as long as it continues to be invoked, will not
// be triggered. The function will be called after it stops being called for
// `wait` milliseconds.
export const debounce = (func: Function, wait: number) => {
  let timeout: number | undefined;

  // This is the function that is returned and will be executed many times
  // We spread (...args) to capture any number of parameters we want to pass
  return function executedFunction(...args: any) {
    // The callback function to be executed after
    // the debounce time has elapsed
    const later = async () => {
      // null timeout to indicate the debounce ended
      timeout = undefined;

      // Execute the callback
      await func(...args);
    };
    // This will reset the waiting every function execution.
    // This is the step that prevents the function from
    // being executed because it will never reach the
    // inside of the previous setTimeout
    clearTimeout(timeout);

    // Restart the debounce waiting period.
    // setTimeout returns a truthy value (it differs in web vs Node)
    timeout = setTimeout(later, wait);
  };
};

/**
 * This hook monitors the appearance of the page element in the screen scope.
 * @param trackedNode
 * @return {{item: null, visited: boolean, isIntersection: boolean}}
 */
export const useIntersected = (trackedNode: RefObject<any>, observerOptions = {}) => {
  const [intersected, setIntersected] = useState<{
    isIntersection: boolean;
    visited: boolean;
    item: IntersectionObserverEntry | null;
  }>({
    isIntersection: false,
    visited: false,
    item: null,
  });

  const observer = new IntersectionObserver(([item]) => {
    if (item.isIntersecting && !intersected.isIntersection) {
      setIntersected((prevState) => ({ ...prevState, isIntersection: true, visited: true, item }));
    } else if (!item.isIntersecting && intersected.isIntersection) {
      setIntersected((prevState) => ({ ...prevState, isIntersection: false, item }));
    } else if (item.isIntersecting && intersected.isIntersection && !intersected.visited) {
      setIntersected((prevState) => ({ ...prevState, isIntersection: true, visited: true, item }));
    }
  }, observerOptions);

  useEffect(() => {
    const { current = null } = trackedNode;
    if (!current) return;
    observer.observe(current);

    // eslint-disable-next-line consistent-return
    return () => observer.disconnect();
  });

  return intersected;
};

/**
 * generates a random id consisting of uppercase letters and numbers
 * @returns a string
 */
export const generateRandomId = (length = 8): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i += 1) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

/**
 * Generate Unix timestamp.
 * @param {Date} time - The time to generate the Unix timestamp from. Defaults to the current time if not provided.
 * @returns {number} The Unix timestamp.
 */
export const generateUnixTimestamp = (time: Date = new Date()) => Math.floor(time.getTime() / 1000);

/**
 * Generate ISO 8601 datetime string.
 * @param {Date} time - The time to generate the ISO 8601 datetime string from. Defaults to the current time if not provided.
 * @returns {string} The ISO 8601 datetime string.
 */
export const generateISO8601DateTime = (time: Date = new Date()) => time.toISOString();

/**
 * Sorts two timestamps in descending order.
 * @param {number} timestampA - The first timestamp to compare.
 * @param {number} timestampB - The second timestamp to compare.
 * @returns {number} A negative value if 'timestampA' is greater than 'timestampB', a positive value if 'timestampB' is greater than 'timestampA', or zero if the timestamps are equal.
 */
export const sortByTimestamp = (timestampA: number, timestampB: number): number => {
  return timestampB - timestampA;
};

export const handleKeyPressNumbers = (e: React.KeyboardEvent) => {
  const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight'];
  if (!/[0-9+-]/.test(e.key) && !allowedKeys.includes(e.key)) {
    e.preventDefault();
  }
};

// This hook allows you to manage the notification popup from anywhere in the application.
export const useNotificationDisclosure = create<{
  tabIndex: number;
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  onToggle: () => void;
  setTabIndex: (tabIndex: number) => void;
}>((set) => ({
  tabIndex: 0,
  isOpen: false,
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
  onToggle: () => set((state) => ({ isOpen: !state.isOpen })),
  setTabIndex: (tabIndex: number) => set(() => ({ tabIndex })),
}));

/**
 * Evaluate a set of conditions with "AND" and "OR" logic and return a result based on those conditions.
 *
 * @param {Array} conditions - An array of conditions and logic groups.
 * @returns {*} - The result of the evaluation based on the conditions, or `undefined` if no conditions match.
 *
 * @example
 * // Example 1: Basic Usage with AND and OR Logic
 * const conditions = [
 *   true,
 *   ['OR', false, true, 'Value for OR'],
 *   ['AND', true, true, false, 'Value for AND'],
 *   true,
 *   ['OR', false, false, 'Another Value for OR'],
 * ];
 * const result = evalConditions(conditions);
 * console.log(result); // Output: "Value for AND"
 *
 * @example
 * // Example 2: Using Default Value
 * const conditions = [
 *   false,
 *   ['OR', false, false, 'Value for OR'],
 *   ['AND', false, false, 'Value for AND'],
 *   false,
 * ];
 * const result = evalConditions(conditions);
 * console.log(result); // Output: undefined
 */
export const evalConditions = (conditions: any[]) => {
  let orGroup = false; // Flag to track OR conditions
  let result;

  // eslint-disable-next-line no-restricted-syntax
  for (const condition of conditions) {
    if (Array.isArray(condition)) {
      // Get last item = return value
      const returnValue = condition[condition.length - 1];
      // Get first item = logical operator OR / AND
      const logic = condition[0];
      // Remove first and last items
      const conds = condition.slice(1, -1);

      if (logic === 'OR') {
        orGroup = true;
        // Check if any conditions in the OR group are true and return the value
        if (conds.some((cond) => cond)) {
          result = returnValue;
        }
      } else if (logic === 'AND') {
        orGroup = false;
        // Check if all conditions in the AND group are true and return the value
        if (conds.every((cond) => cond)) {
          result = returnValue;
        }
      }
    } else if (!orGroup && condition) {
      // If it's a single boolean value, and it's true (inside an AND group), return the next value in the array
      const index = conditions.indexOf(condition);
      if (index < conditions.length - 1) {
        result = conditions[index + 1];
      }
    }
  }

  // If no valid condition is found, you can return a default value or handle it as needed
  return result !== undefined ? result : undefined; // You can change this to a default value or error message if desired
};

// if name of the user or patient length is more than 20 characters. Similar to ellipsis
export const truncateName = (name: string, value: number) => {
  if (name && name.length > value) {
    return `${name.substring(0, value)} ...`;
  }
  return name;
};
export const truncateLength = (name: string, value: number) => {
  if (name && name.length > value) {
    return `${name.substring(0, value)}`;
  }
  return name;
};

// Compact arrays with null entries; delete keys from objects with null value
export const removeNulls = (obj: any) => {
  const x = obj;
  // let k: any;
  Object.keys(x).forEach((key: any) => {
    if (x[key] === null) {
      if (Array.isArray(x)) {
        x.splice(key, 1);
      } else {
        delete x[key];
      }
    } else if (typeof x[key] === 'object') {
      removeNulls(x[key]);
    }
  });
  return x;
};

// Compare 2 different strings irrespective of their case
export const equalsIgnoreCase = (str1: String, str2: String): boolean => {
  return str1.toLowerCase() === str2.toLowerCase();
};

interface NestedObject {
  [key: string]: any;
}

export const removeNullValues = (obj: NestedObject): NestedObject => {
  if (Array.isArray(obj)) {
    return obj.map(removeNullValues);
  }
  if (obj !== null && typeof obj === 'object') {
    return Object.entries(obj).reduce((acc: NestedObject, [key, value]) => {
      if (value !== null) {
        acc[key] = removeNullValues(value);
      }
      return acc;
    }, {});
  }
  return obj;
};

// check if an object is empty
export const isEmptyObject = (obj: any) => {
  // Check if obj is null or undefined
  if (obj === null || obj === undefined) {
    return true;
  }

  // Ensure obj is an object and not an array or other type
  if (typeof obj !== 'object' || Array.isArray(obj)) {
    return false;
  }

  // Check if the object has no keys or all keys have null/undefined values
  return Object.keys(obj).length === 0 || Object.values(obj).every((value) => value === null || value === undefined);
};

export function findIdentifiersByType(type: string): string[] {
  const identifiers: string[] = [];

  preventativeScreeningConstants.forEach((item) => {
    const [, value] = Object.entries(item)[0];
    if (value.type === type) {
      identifiers.push(value.identifier.value);
    }
  });

  return identifiers;
}
export function isDuplicatePresentCondition(code: string, list: any[], oldItem?: any): string | false {
  const isSameAsOld = oldItem?.code?.coding?.some((c: any) => c.code === code);
  if (isSameAsOld) return false;
  const duplicateItem = list.find(
    (item) =>
      item.code?.coding?.some((c: any) => c.code === code) && item.clinicalStatus?.coding?.[0]?.code !== 'inactive'
  );
  return duplicateItem?.id || false;
}
export function isDuplicatePresentSymptom(code: string, list: any[], oldItem?: any): string | false {
  const isActive = (item: any) => {
    const extStatus = item.extension?.find((e: any) => e.url === FH_STRUCTURE_DEFINITION_CLINICALSTATUS)
      ?.valueCodeableConcept?.coding?.[0]?.code;

    return !(extStatus === 'inactive' || item.clinicalStatus?.coding?.[0]?.code === 'inactive');
  };

  return (
    list.find(
      (item) =>
        item.code?.coding?.some((c: any) => c.code === code) &&
        isActive(item) &&
        (!oldItem?.id || item.id !== oldItem.id)
    )?.id || false
  );
}
export function isDuplicatePresentAllergies(code: string, list: any[], oldItem?: any): string | false {
  const isActiveAllergy = (item: any) => item.clinicalStatus?.coding?.[0]?.code !== 'inactive';
  return (
    list.find(
      (item) =>
        item.code?.coding?.some((c: any) => c.code === code) &&
        isActiveAllergy(item) &&
        (!oldItem?.id || item.id !== oldItem.id)
    )?.id || false
  );
}
export function isDuplicatePresentProcedures(code: string, list: any[], oldItem?: any): string | false {
  const isActiveProcedure = (item: any) => item.status !== 'completed';
  return (
    list.find(
      (item) =>
        item.code?.coding?.some((c: any) => c.code === code) &&
        isActiveProcedure(item) &&
        (!oldItem?.id || item.id !== oldItem.id)
    )?.id || false
  );
}
export function isDuplicateFamilyMemberCondition(
  newCode: string,
  familyMemberHistory: any,
  oldCondition?: any
): string | false {
  if (!newCode || familyMemberHistory?.condition?.length === 0) return false;
  if (oldCondition) {
    const oldCode = (oldCondition && oldCondition?.code?.coding?.[0]?.code) || false;
    if (oldCode && newCode === oldCode) return false;
  }
  const duplicate = familyMemberHistory?.condition?.find((condition: any) => {
    return !condition.onsetPeriod?.end && condition.code?.coding?.some((c: any) => c.code === newCode);
  });
  return duplicate || false;
}
export function isDuplicatePresentMedication(code: string, list: any[], oldItem?: any): string | false {
  const isActiveMedication = (item: any) => {
    return (
      item.extension?.find((e: any) => e.url === FH_STRUCTURE_DEFINITION_CLINICALSTATUS)?.valueCodeableConcept
        ?.coding?.[0]?.code !== 'inactive'
    );
  };
  return (
    list.find(
      (item) =>
        item.medicationCodeableConcept?.coding?.some((c: any) => c.code === code) &&
        isActiveMedication(item) &&
        (!oldItem?.id || item.id !== oldItem.id)
    )?.id || false
  );
}
export const getErrorMessage = (error: FieldError | Merge<FieldError, FieldErrorsImpl<any>> | undefined): string => {
  if (!error) return '';

  // Type guard to ensure 'type' exists
  if ('type' in error && typeof error.message === 'string') {
    if (error.type === 'invalid_type') {
      return 'This field is required';
    }
    return error.message;
  }

  return 'This field is required';
};

interface Coding {
  code?: string;
}

interface Task {
  businessStatus?: {
    coding?: Coding[];
  };
  status: string;
  focus?: {
    resource?: any;
  };
}

type FilterMode = 'exclude' | 'include';

interface FilterOptions {
  tasks?: Task[];
  codeSet?: Set<string>;
  mode?: FilterMode;
  status?: string;
  transform?: (task: Task) => any;
}

export function filterTasksByCode({ tasks = [], codeSet, mode = 'include', status, transform }: FilterOptions): any[] {
  // Early return for empty arrays to avoid unnecessary processing
  if (!tasks?.length) return [];

  // Pre-compute boolean flags for better performance
  const hasCodeSet = !!codeSet;
  const hasStatus = !!status;
  const hasTransform = !!transform;
  const isIncludeMode = mode === 'include';

  return tasks
    .filter((task) => {
      // Early validation checks - fail fast for invalid tasks
      const resource = task?.focus?.resource;
      if (!resource || resource.status === 'superseded') return false;

      // Extract and validate code
      const code = task?.businessStatus?.coding?.[0]?.code;
      if (typeof code !== 'string') return false;

      // Status check - early return if status doesn't match
      if (hasStatus && task.status !== status) return false;

      // Code matching logic - optimized with pre-computed flags
      if (!hasCodeSet) return true; // No code filtering needed

      const isMatch = codeSet.has(code);
      return isIncludeMode ? isMatch : !isMatch;
    })
    .map((task) => (hasTransform ? transform(task) : task));
}

export const extractId = (input: string) => {
  return input.includes('/') ? input.split('/')[1] : input;
};

// Utility function to format date for saving (with timezone if needed)
export const formatDateForSave = (date: any): string => {
  if (!date || !dayjs(date).isValid()) return '';
  return `${dayjs(date).format('YYYY-MM-DD')}T00:00:00.000Z`;
};

/**
 * Downloads a file from a given URL with a custom filename.
 * @param fileUrl - The URL of the file to download.
 * @param filename - The desired filename for the downloaded file.
 */
export const downloadFile = async (fileUrl: string, filename: string) => {
  const response = await fetch(fileUrl);
  const blob = await response.blob();
  const link = document.createElement('a');
  link.href = window.URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
