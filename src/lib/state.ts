// Package modules
import { useEffect, useMemo, useRef, useState } from 'react';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useDebounce } from 'usehooks-ts';
// Local modules
import { Client, useClient } from 'urql';
import { medplumApi } from '@user/lib/medplum-api';
import { useParams, useSearchParams } from 'react-router-dom';
import { Buffer } from 'buffer';
import { DOCUMENT_REF } from '@user/lib/constants';

import { api } from '@lib/api';
// import { api as userApi } from '../app/user/lib/api';
import { Patient } from '@lib/models/patient';
import { AuthService } from './authService';
import { PUBLIC_URL_PREFIX, SHARED_STATE, SHARE_PROFILE_URL_PREFIX, SHARE_RECORD_URL_PREFIX } from '@lib/constants';
import { useIntersected } from '@lib/utils/utils';
import { AnalyticsService } from '@lib/analyticsService';
import { Alert, AlertPayload } from '@lib/models/alert';
import { COMM_REMINDER_EXTENSION } from 'src/app/medical-records/lib/constants';
import { getAllAlertsQuery, getDocumentReference, getFileFromTask } from './medplum-graphql-query';

// Constants
export const STATE_TYPES = {
  ...SHARED_STATE,
  ALERT: 'alert',
  ALERT_LIST: 'alert_list',
  PUBLIC_SETTINGS: 'public_settings',
  CONSENT_MANAGER: 'consent_manager',
};

export const useAnalyticsService = <T>() => {
  const analyticsService = AnalyticsService.instance;
  const [analyticsData, setAnalyticsData] = useState<T>();

  return useMemo(
    () => ({
      analyticsService,
      trackEvent: analyticsService.trackEvent.bind(analyticsService),
      trackEventInFlow: analyticsService.trackEventInFlow.bind(analyticsService),
      analyticsData,
      setAnalyticsData,
    }),
    [analyticsService, analyticsData]
  );
};

export const base64toJSON = (data: any) => {
  const x: string = Buffer.from(data, 'base64').toString();
  const y: any = JSON.parse(x);
  return y;
};

export const usePublicSettings: any = () => {
  const isPublicMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_PROFILE_URL_PREFIX);

  const isPublicRecordMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_RECORD_URL_PREFIX);

  if (isPublicRecordMode) {
    return { isPublicMode };
  }

  return {
    isPublicMode,
  };
};

export const usePublicRecordSettings = () => {
  const isPublicMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_PROFILE_URL_PREFIX);

  const isPublicRecordMode: boolean =
    window.location.pathname.startsWith(PUBLIC_URL_PREFIX) ||
    window.location.pathname.startsWith(SHARE_RECORD_URL_PREFIX);

  if (isPublicMode) {
    return { isPublicRecordMode };
  }

  const { shareId: consentId } = useParams();
  const [searchParams] = useSearchParams();

  const getPublicRecordUrl = () => {
    return medplumApi.shareProfileRecord.getShareData(consentId, searchParams.get('access_token'), 'record');
  };

  const { data }: any = useQuery<any>([STATE_TYPES.PUBLIC_SETTINGS, consentId], getPublicRecordUrl, {
    // PublicProfileDetails
    // Retrieve data from the database only on a public page
    enabled: isPublicRecordMode,
    staleTime: 1000,
    onError: (error) => {
      throw error;
    },
  });

  const myDocumentReferenceList = data?.section?.filter((val: any) => val?.title === DOCUMENT_REF)?.[0]?.entry || [];

  const myRecordPatient = data?.section?.find((val: any) => val?.title === 'Patient')?.entry[0].resource || {};

  return {
    isPublicRecordMode,
    myDocumentReferenceList,
    myRecordPatient,
    isMultiRecord: myDocumentReferenceList.length > 1,
  };
};

export const useAuthService = () => {
  const authService = AuthService.instance;
  const authenticatedUser = authService.getAuthenticatedUser();
  const { temporaryToken } = AuthService;
  const { isPublicMode } = usePublicSettings();
  const { isPublicRecordMode } = usePublicRecordSettings();

  const { data: isLoggedIn, refetch: verifyMe } = useQuery(
    [STATE_TYPES.IS_LOGGED_IN],
    async () => {
      let isAuthenticated = false;
      if (!isPublicMode && !isPublicRecordMode) {
        isAuthenticated = await AuthService.instance.isLoggedIn();
      }
      return isAuthenticated;
    },
    {
      staleTime: 60000,
      suspense: true,
    }
  );

  const staticProps = useMemo(
    () => ({
      authService,
      login: authService.login.bind(authService),
      logout: authService.logout.bind(authService),
      loginVerify: authService.loginVerify.bind(authService),
      OTP2FAVerify: authService.OTP2FAVerify.bind(authService),
      resendCode: authService.resetPinGenerateOTP.bind(authService),
      resetPinGenerateOTP: authService.resetPinGenerateOTP.bind(authService),
      resetPinOTPVerify: authService.resetPinOTPVerify.bind(authService),
      resetPinSave: authService.resetPinSave.bind(authService),
      setAuthenticatedUser: authService.setAuthenticatedUser.bind(authService),
    }),
    [authService]
  );
  return {
    isLoggedIn,
    verifyMe,
    authenticatedUser,
    temporaryToken,
    ...staticProps,
  };
};

export async function getAlerts(client: Client, variables: any) {
  const response = await client.query(getAllAlertsQuery, variables);

  return response?.data?.CommunicationRequestList ?? [];
}

export const useAlertList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const client = useClient();

  const queryKey = [STATE_TYPES.ALERT_LIST, patientId];
  const getAll = () => {
    return getAlerts(
      client,
      { patientId, reminderUrl: COMM_REMINDER_EXTENSION }
      // {
      // ...filters,
      // page: pageParam,}}
    );
  };

  const { data, fetchNextPage, hasNextPage, isLoading, isFetching } = useInfiniteQuery(queryKey, getAll, {
    // getNextPageParam: (lastPage) =>
    //   lastPage.current_page < lastPage.last_page ? lastPage.current_page + 1 : undefined,
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    suspense: false,
  });

  const addAlert = (payload: AlertPayload) => api.alerts.addAlert(payload);
  const { mutateAsync: mutateAddAlert, isLoading: isCreatingAlert } = useMutation(addAlert, {
    onSuccess: () => {
      // Refetch loaded pages to correctly display the created reminder.
      queryClient.invalidateQueries(queryKey);
    },
  });

  type UpdateAlertPayload = {
    alertId: Alert['id'];
    payload: AlertPayload;
  };
  const updateAlert = ({ alertId, payload }: UpdateAlertPayload) => api.alerts.updateAlert(alertId, payload);

  const linkCommRequestWithDocRef = ({
    commReqId,
    docRefId,
  }: {
    commReqId: string | null | undefined;
    docRefId: string;
  }) => api.alerts.linkCommRequestWithDocRef(commReqId, docRefId);

  const { mutateAsync: mutateUpdateAlert, isLoading: isUpdatingAlert } = useMutation(updateAlert, {
    onSuccess: async () => {
      queryClient.invalidateQueries(queryKey);
    },
  });

  const deleteAlert = (alertId: Alert['id']) => api.alerts.deleteAlert(alertId);
  const { mutateAsync: mutateDeleteAlert, isLoading: isDeletingAlert } = useMutation(deleteAlert, {
    onSuccess: () => {
      queryClient.invalidateQueries(queryKey);
    },
  });

  // Concatenate all data pages into a single array
  const allAlerts = data?.pages ? data.pages.flatMap((page) => Object.values(page).flatMap((item) => item)) : [];

  const alertList = allAlerts.sort((a: any, b: any) => {
    const isAInactive = a.status === 'completed' || a.status === 'revoked';
    const isBInactive = b.status === 'completed' || b.status === 'revoked';
    if (isAInactive === isBInactive) return 0;
    return isAInactive ? 1 : -1; // move inactive to bottom
  });
  return {
    alertList,
    linkCommRequestWithDocRef,
    addAlert: mutateAddAlert,
    updateAlert: mutateUpdateAlert,
    deleteAlert: mutateDeleteAlert,
    isMutating: isCreatingAlert || isUpdatingAlert || isDeletingAlert,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
  };
};

export const useAlertListLazyPagination = (patientId: Patient['id']) => {
  const loadingElementRef = useRef(null);

  const { isIntersection: shouldLoadMore } = useIntersected(loadingElementRef);
  const debouncedShouldLoadMore = useDebounce(shouldLoadMore, 500);

  const { alertList, fetchNextPage, hasNextPage, isFetching, isLoading } = useAlertList(patientId);

  useEffect(() => {
    if (shouldLoadMore && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [shouldLoadMore, hasNextPage, isFetching]);

  return {
    alertList,
    loadingElementRef,
    shouldLoadMore: debouncedShouldLoadMore,
    hasNextPage,
    isLoading,
  };
};

export async function downloadProfile(client: Client, variables: any) {
  const response = await client.query(getFileFromTask, { id: variables });
  return response?.data?.Task ?? {};
}
export async function downloadFileQuery(client: Client, variables: string) {
  const response = await client.query(getDocumentReference, { id: variables });
  return response?.data?.DocumentReference ?? {};
}
