import { CLARITY_PROJECT_ID } from './constants';

declare global {
  interface Window {
    clarity: {
      (action: 'start', config: { projectId: string }): void;
      (action: 'identify', userId: string, sessionId?: string, pageId?: string, userHint?: string): void;
      (action: 'consent'): void;
      (action: 'upgrade', upgradeReason: string): void;
      (action: 'event', eventName: string): void;
      (action: 'set', key: string, value: string | number | boolean | string[]): void;
    };
  }
}

class ClarityService {
  private isInitialized = false;

  private projectId: string;

  constructor() {
    this.projectId = CLARITY_PROJECT_ID;
  }

  /**
   * Initialize Microsoft Clarity
   */
  init(): void {
    if (this.isInitialized || !this.projectId) {
      return;
    }

    try {
      // Load Clarity script
      this.loadClarityScript();
      this.isInitialized = true;
      console.log('Microsoft Clarity initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Microsoft Clarity:', error);
    }
  }

  /**
   * Load the Clarity tracking script
   */
  private loadClarityScript(): void {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.innerHTML = `
      (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "${this.projectId}");
    `;
    document.head.appendChild(script);
  }

  /**
   * Identify a user in Clarity
   * @param userId - Unique user identifier
   * @param sessionId - Optional session identifier
   * @param pageId - Optional page identifier
   * @param userHint - Optional user hint for better identification
   */
  identify(userId: string, sessionId?: string, pageId?: string, userHint?: string): void {
    if (!this.isInitialized || !window.clarity) {
      console.warn('Clarity not initialized');
      return;
    }

    try {
      window.clarity('identify', userId, sessionId, pageId, userHint);
    } catch (error) {
      console.error('Clarity identify error:', error);
    }
  }

  /**
   * Grant consent for Clarity tracking
   */
  consent(): void {
    if (!this.isInitialized || !window.clarity) {
      console.warn('Clarity not initialized');
      return;
    }

    try {
      window.clarity('consent');
    } catch (error) {
      console.error('Clarity consent error:', error);
    }
  }

  /**
   * Upgrade the current session
   * @param upgradeReason - Reason for upgrading the session
   */
  upgrade(upgradeReason: string): void {
    if (!this.isInitialized || !window.clarity) {
      console.warn('Clarity not initialized');
      return;
    }

    try {
      window.clarity('upgrade', upgradeReason);
    } catch (error) {
      console.error('Clarity upgrade error:', error);
    }
  }

  /**
   * Track a custom event
   * @param eventName - Name of the event to track
   */
  event(eventName: string): void {
    if (!this.isInitialized || !window.clarity) {
      console.warn('Clarity not initialized');
      return;
    }

    try {
      window.clarity('event', eventName);
    } catch (error) {
      console.error('Clarity event error:', error);
    }
  }

  /**
   * Set custom data for the session
   * @param key - Data key
   * @param value - Data value
   */
  set(key: string, value: string | number | boolean | string[]): void {
    if (!this.isInitialized || !window.clarity) {
      console.warn('Clarity not initialized');
      return;
    }

    try {
      window.clarity('set', key, value);
    } catch (error) {
      console.error('Clarity set error:', error);
    }
  }

  /**
   * Check if Clarity is initialized and available
   */
  isAvailable(): boolean {
    return this.isInitialized && !!window.clarity;
  }
}

// Create and export a singleton instance
const clarityService = new ClarityService();
export default clarityService;
