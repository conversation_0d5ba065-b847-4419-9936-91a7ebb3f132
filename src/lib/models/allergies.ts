import { AttachedMedicalRecord } from '@lib/models/medical-record';

export interface Allergy {
  id: string;
  allergy_type: { value: string; label: string } | null;
  allergy: any;
  criticality: { value: string; label: string } | null;
  diagnosis_date: string;
  intoleranceStatus: boolean;
  external_reports?: AttachedMedicalRecord[];
  resourceType: string | null;
  lastOccurrence: string;
  notes: string;
  custom_allergy: string;
}

export type AllergyPayload = Omit<Allergy, 'id' | 'external_reports'> & {
  external_reports?: Array<AttachedMedicalRecord['id']>;
};
