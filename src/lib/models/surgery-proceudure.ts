import { ProcedureType } from './misc';
import { AttachedMedicalRecord } from '@lib/models/medical-record';

export interface MasterSurgery {
  master_id: number;
  display?: string;
}

export interface Surgery {
  id: string | number;
  surgery_date: string;
  procedure_type: ProcedureType;
  surgery: {
    value: string;
    label: string;
  };
  // surgery_hospital: string;
  external_reports: AttachedMedicalRecord[];
}

export interface PastSurgery {
  resourceType: string;
  id: number | string;
  status: string;
  code: {
    text: string; // name of the surgery which is done
  };
  subject: {
    id: number; // Surgery patient Id
    reference: string; // this is the reference api
    type: string; // this is subject type
    display: string; // name of the patient
  };

  location: {
    display: string; // hospital name where the done surgery
  };
  effectiveDateTime: string; // datetime when surgery is done
  procedure_type: ProcedureType; // define procedure type
  surgery_master: {
    master_id: number;
    surgery_name: string; // Name of surgery
  };
  external_reports: AttachedMedicalRecord[];
}

export type CreateSurgeryPayload = Omit<Surgery, 'id' | 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};

export type UpdateSurgeryPayload = Omit<Surgery, 'id' | 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};
