import { SuggestionOptionProps } from '@components/ui/Form';

import { ConditionType } from '@lib/models/condition';
import { AttachedMedicalRecord } from '@lib/models/medical-record';

export interface Symptom {
  id: string;
  symptom: SuggestionOptionProps | null;
  // symptom_type: ConditionType;
  start_date: string;
  end_date: string;
  external_reports: AttachedMedicalRecord[];
}

export interface PostSurgery {
  id: string;
  symptom: {
    id: string | number;
  };
  symptom_type: ConditionType;
  diagnosis_date: string;
  notes: string;
  external_reports: AttachedMedicalRecord[];
}

export interface PartialSymptom {
  id: string;
  text: string;
}

export type CreateSymptomPayload = Omit<PostSurgery, 'id' | 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};
