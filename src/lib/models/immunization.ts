import { AttachedMedicalRecord } from './medical-record';

export enum ImmunizationFocusArea {
  GeneralHealth = 'General Health',
  SexualHealth = 'Sexual Health',
}

export interface Immunization {
  identifier: any;
  occurrenceDateTime: any;
  id: string;
  immunization: {
    id: string;
    text: string;
  };
  focus_area: ImmunizationFocusArea;
  frequency: string;
  age: string;
  diagnosis_date: string;
  fully_immunized?: boolean;
  is_pregnant?: boolean;
  pregnancy_trimester?: string;
  external_reports: AttachedMedicalRecord[];
}

export type ImmunizationPayload = Omit<Immunization, 'id' | 'immunization' | 'external_reports'> & {
  immunization: {
    id: string;
  };
  external_reports: Array<AttachedMedicalRecord['id']>;
};
