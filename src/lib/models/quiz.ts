export enum QuestionType {
  MultipleSelect = 'multiple_select',
  DatePicker = 'date_picker',
  SingleSelect = 'single_select',
  YesNo = 'yes_no',
  OpenText = 'open_text',
  Scale = 'scale',
}

export interface Question {
  title: string;
  type: QuestionType;
  subtitle?: string;
  options?: string[];
}

export interface Quiz {
  prismicId: string;
  slugs: string[];
  title: string;
  description: string;
  theme: string;
  questions: Question[];
  firstPublicationDate: Date;
  lastPublicationDate: Date;
}
