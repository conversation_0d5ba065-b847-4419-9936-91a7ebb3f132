import { AttachedMedicalRecord } from '@lib/models/medical-record';

export enum ConditionType {
  Acute = 'Acute',
  Chronic = 'Chronic',
}

export enum ConditionShareProfile {
  shareProfileFalse = 'share-profile:false',
  shareProfileTrue = 'share-profile:true',
}

export interface PartialCondition {
  condition_id: number;
  condition_name: string;
  condition_type: string; // condition speciality
}

export interface ConditionSuggestion {
  condition_id: number;
  condition_name: string;
  condition_type: string[]; // condition speciality
}

export interface Condition {
  id?: string;
  diagnosis_date?: string;
  condition_type?: {
    label: string;
    value: string;
  };
  condition?: {
    label: string;
    value: string;
  };
  extension?: any[];
  custom_condition?: string;
  clinicalStatus?: any;
  notes?: string;
  end_date?: string;
  is_shareable?: boolean;
  external_reports?: AttachedMedicalRecord[];
  resourceType?: string;
  identifier?: { system: string; value: string }[];
  code?: {
    coding: {
      system: string;
      code: string;
      display: string;
    }[];
  };
  subject?: {
    reference: string;
  };
  onsetPeriod?: {
    start?: string;
    end?: string;
  };
  note?: {
    text: string;
  }[];
  evidence?: {
    detail: {
      reference: string;
    }[];
  }[];
  meta?: {
    tag: {
      system: string;
      code: string;
      display: string;
    }[];
  };
}

export type ConditionPayload = Omit<Condition, 'id' | 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};
