import { Patient } from 'src/gql/graphql';

export enum MEDICAL_RECORD_TYPES {
  LAB_RESULTS = '102009-8',
  CONSULTATION_NOTE = 'LP72311-1',
  GENERAL = '34764-1',
  VACCINE_RECORD = '82593-5',
  PRESCRIPTIONS = '57833-6',
  INVOICE = '52075-9',
  IMAGING_RECORD = '18748-4',
}

export enum MEDICAL_RECORD_FILTERS {
  BEHOLDER = 'beholder',
  FROM_DATE = 'from_date',
  TO_DATE = 'to_date',
  ID = 'id',
  KEYWORD = 'description',
  FACILITY_TYPE = 'facility_type',
  CONSULTATION_TYPE = 'consultation_type',
  SPECIALITY_TYPES = 'speciality_types',
  SORT_BY = 'sort_by',
  CONDITIONS = 'conditions',
  TAGS = 'tags',
  REPORT_TYPE = 'report_type',
  STARRED = 'starred',
}

export enum MEDICAL_RECORD_CONSULTATION_TYPES {
  IN_PERSON = 'In-Person',
  VIRTUAL = 'Virtual',
}

export enum MEDICAL_RECORD_REFERRED_TYPES {
  YES = 'Yes',
  NO = 'No',
}
export enum MEDICAL_RECORD_BEHOLDER_TYPES {
  ALL = 'all',
  OWN = 'medical-record-record-for-myself',
  OTHERS = 'medical-record-record-for-someone-else',
}

export interface ExternalReportType {
  report_type_id: string;
  report_type: string;
  report_type_slug: string;
}

export type ExternalReportTypeList = ExternalReportType[];

export interface ExternalReportTag {
  name: string;
}

export type MedicalRecordCondition = {
  id: number;
  text: string;
};

export type MedicalRecordContent = {
  attachment: {
    creation: any;
    contentType: string;
    data: string;
    title: string;
    url: string;
  };
};

export type PUTMedicalRecordPayload = Pick<
  MedicalRecord,
  | 'title'
  | 'doctorName'
  | 'notes'
  | 'location'
  | 'typeOfVisit'
  | 'speciality'
  | 'wasReferred'
  | 'medicalRecordFor'
  | 'dateOnRecord'
  | 'conditionType'
> & {
  type: {
    id: number;
  };
  // speciality_type: {
  //   id: SpecialityType['id'];
  // };
  // condition?: {
  //   id: MedicalRecordCondition['id'];
  // } | null;
};

export type CreateMedicalRecordPayload = Pick<
  MedicalRecord,
  'title' | 'doctorName' | 'notes' | 'dateOnRecord' | 'medicalRecordFor'
> & {
  type: {
    id: number;
  };
  content: Array<{
    attachment: {
      contentType: string;
      data: string;
      title: string;
    };
  }>;
};

export type MedicalRecord = {
  date: any;
  type: any;
  id: string;
  quesRespId: string;
  notes: string;
  title: string;
  typeOfVisit: {
    system: string;
    code: string | null;
    display: string;
  };
  dateOnRecord: string;
  medicalRecord: MedicalRecordEntry[];
  location: string;
  medicalRecordType: {
    system: string;
    code: string;
    display: string;
  };
  doctorName: string;
  wasReferred: boolean;
  isBookmarked: boolean;
  conditionType: {
    system: string;
    code: string;
    display: string;
  };
  speciality: {
    system: string;
    code: string | null;
    display: string;
  };
  medicalRecordFor: string;
  recordAttachments: RecordAttachment[];
  docRefCreationDate: string;
  isLabReport: boolean;
  tags: any;
};

type MedicalRecordEntry = {
  linkId: string;
  text: string | null;
  id: string | null;
  answer: MedicalRecordAnswer[];
};

type MedicalRecordAnswer = {
  valueString: string | null;
  valueCoding: {
    system: string | null;
    code: string | null;
    display: string;
  } | null;
  valueBoolean: boolean | null;
  valueDate: string | null;
  valueAttachment: null;
};

type RecordAttachment = {
  url: string | null;
  contentType: string | null;
  size: number;
  title: string | null;
};
export interface AttachedMedicalRecord {
  id: string;
  quesRespId: string;
  title: string;
  type: string;
  date: string;
}

export interface ExtendedMedicalRecord extends MedicalRecord {
  period: number | null | undefined;
}

export type UpdateMedicalRecordAlertsPayload = {
  alerts: Array<Record<string, string>>;
};

export interface Tag {
  code: string;
  system: string;
}

export interface ValueSetUrls {
  type: string;
  url: string;
}

export interface MedicalRecordDetails {
  patientId: Patient['id'];
  status: string;
}

export interface MedicalRecordDetailsMapping {
  [key: string]: MedicalRecordDetails;
}
