import { Patient } from './patient';

export type Doctor = Pick<
  // Common types with Patient
  Patient,
  'id' | 'active' | 'name' | 'telecom' | 'address' | 'birthDate' | 'photo'
> & {
  // Specific Doctor fields
  resourceType: string;
  qualification: Array<{
    code: {
      text: string;
    };
  }>;
  languages: string[];
  about_me: string;
  experience: string;
  speciality: Array<{
    id: number;
    text: string;
  }>;
  chatbot_id: string;
  focus_area: string[];
  is_part_of_care_team_id: string[];
  education_and_credentials: {
    degree: string;
    certification: string;
    institute: string;
    certification_year: string;
  }[];
};
