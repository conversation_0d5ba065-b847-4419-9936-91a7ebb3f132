import { AttachedMedicalRecord } from '@lib/models/medical-record';

export enum PrescribedOptions {
  YES = 'Yes',
  NO = 'No',
  IDK = 'I do not know',
}
export interface Supplement {
  resourceType: string;
  id: string;
  supplement: MasterSupplement;
  supplement_brand: string;
  dosage_instruction: {
    text: string;
  };
  frequency: string;
  prescribed: PrescribedOptions;
  created_at?: string;
  external_reports: AttachedMedicalRecord[];
}

export interface MasterSupplement {
  id: string;
  class: string;
  name: string;
}

export interface PostSupplement {
  resourceType: string;
  id: string;
  supplement: {
    id: string | number;
  };
  supplement_brand: string;
  dosage_instruction: {
    text: string;
  };
  frequency: string;
  prescribed: string;
  created_at?: string;
}

export type SupplementsPayload = Omit<PostSupplement, 'id'>;
