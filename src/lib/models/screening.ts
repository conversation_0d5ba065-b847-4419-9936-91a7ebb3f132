import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { YesOrNoAnswer } from './misc';

export interface Screening {
  id: string;
  screening: {
    master_id: string;
    display: string;
  };
  focus_area: string;
  frequency: string;
  age: string;
  gender: string;
  family_history: YesOrNoAnswer | null;
  screening_date: string;
  created_at: string;
  notes: string;
  external_reports: AttachedMedicalRecord[];
}

export type ScreeningPayload = {
  screening_date: string;
  screening: {
    master_id: string;
  };
  focus_area: string;
  family_history: YesOrNoAnswer | null;
  notes: string | null;
  external_reports: Array<AttachedMedicalRecord['id']>;
};

export type MasterScreening = {
  key: string;
  code: string;
  display: string;
  route: string;
  name?: string;
  value?: string;
};
