import { HealthcareProxyRelationType } from './misc';
import { PatientName } from '@lib/models/patient';

export interface HealthcareProxy {
  id: number;
  name: PatientName;
  relationship: HealthcareProxyRelationType;
  telecom: any;
  file: HealthcareProxyFile[];
  active: boolean;
}

export interface HealthcareProxyName {
  text: string;
  family: string;
}

export interface HealthcareProxyFile {
  contentType: string;
  data: string;
  url: string;
}
export interface HealthcareProxyFilePayload {
  file_type: string;
  file: HealthcareProxyFile;
}

export type HealthcareProxyPayload = Omit<HealthcareProxy, 'id' | 'file'>;
