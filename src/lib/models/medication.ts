import { AttachedMedicalRecord } from './medical-record';

export enum MedicationType {
  ALLOPATHIC = 'Allopathic',
  HOMEOPATHIC = 'Hemeopathic',
  AYURVEDIC = 'Ayurvedic',
  OTHER = 'Other',
}
export interface Medication {
  resourceType: string;
  id: string;
  drug_id: string;
  drug_name: string;
  generic_name: string;
  formulation: string;
  strength: string;
  start_date: string;
  end_date: string;
  days: number;
  authoredOn: string;
  dosageInstruction: {
    text: string;
  };
  medication_date: string;
  medication_end: string;
  medication_status: string;
  frequency: string;
  type_of_medication: string;
  external_reports: AttachedMedicalRecord[];
}

export type MedicationPayload = Omit<Medication, 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};

export interface MedicationSuggestion {
  id: string;
  drug_name: string;
  generic_name: string;
}
