import { RecordedByType } from '@lib/models/misc';

export enum VITAL_SOURCE_IDS {
  ONE = 1,
  TWO = 2,
  THREE = 3,
}

export enum VITAL_SOURCE_NAMES {
  EHR = 'EHR',
  DIRECT = 'Direct',
  PHR = 'PHR',
}

export interface PatientVitals {
  resourceType: 'Observation' | string;
  id: number;
  code: {
    text: string;
  };
  vital_master_id: number;
  subject: {
    id: number;
    display: string;
  };
  encounter: {
    id: number;
    display: string;
    reference: string;
    type: string;
  };
  effectiveDateTime: string;
  performer: {
    id: number;
    type: string;
    display: string;
    reference: string;
  }[];
  fields: {
    field_id: number;
    field_value: string;
    field_unit: string;
  }[];
  source: {
    id: VITAL_SOURCE_IDS;
    text: VITAL_SOURCE_NAMES;
  };
  recorded_by: RecordedByType;
}

export type MasterVital = {
  name: string;
  value: string;
};

export type MasterVitalField = {
  field_id: string;
  field_type: string;
  unit: string;
  separator: string;
};

export type AddPatientVitalsPayload = Pick<PatientVitals, 'effectiveDateTime' | 'vital_master_id' | 'source'> & {
  recorded_by?: RecordedByType;
  fields: Array<{
    field_id: number;
    field_value: string;
  }>;
};

export type UpdatePatientVitalsPayload = Pick<
  PatientVitals,
  'effectiveDateTime' | 'resourceType' | 'vital_master_id'
> & {
  recorded_by?: RecordedByType;
  fields: Array<{
    field_id: number;
    field_value: string;
  }>;
};

export type MasterImmunization = {
  name: string;
  value: string;
};
