import { PatientName } from '@lib/models/patient';
import { PersonLivingStatus, ProcedureType, RelationType } from './misc';
import { AttachedMedicalRecord } from '@lib/models/medical-record';

export interface RelatedPerson {
  id: string;
  resourceType: string;
  relationship: RelationType;
  name: Patient<PERSON><PERSON>;
  age: number | string;
  date_of_birth: string;
  blood_type: string;
  status: PersonLivingStatus;
  conditions: RelatedPersonCondition[];
  procedures: RelatedPersonProcedure[];
}

export type RelatedPersonPayload = Omit<RelatedPerson, 'id' | 'conditions' | 'procedures'> & {
  conditions: RelatedPersonConditionPayload[];
  procedures: RelatedPersonProcedurePayload[];
};

export type RelatedPersonSearchParams = {
  keyword?: string;
  page?: number;
  limit?: number;
};

export interface RelatedPersonCondition {
  id: string;
  notes?: string;
  diagnosis_date: string;
  condition?: {
    label: string;
    value: string;
  };
  condition_type?: {
    label: string;
    value: string;
  };
  is_shareable?: boolean;
  external_reports?: AttachedMedicalRecord[];
  code?: any;
  extension?: any;
  onsetPeriod: {
    start: string;
    end: string;
  };
}

export type RelatedPersonConditionPayload = Omit<RelatedPersonCondition, 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};

export interface RelatedPersonProcedure {
  id: number;
  name: string;
  notes: string;
  diagnosis_date: string;
  procedure_type: ProcedureType;
  created_at: string;
  external_reports: AttachedMedicalRecord[];
}

export type RelatedPersonProcedurePayload = Omit<RelatedPersonProcedure, 'external_reports'> & {
  external_reports: Array<AttachedMedicalRecord['id']>;
};
