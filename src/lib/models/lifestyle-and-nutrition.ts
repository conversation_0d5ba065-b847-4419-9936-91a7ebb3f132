export type MasterLifestyleCategory = {
  name: string;
  label: string;
};

export type MasterLifestyleQuestion = {
  linkId: any;
  id: string;
  type: string;
  text: string;
  answerValueSet: string;
  answerOption: string[] | null;
};

export type PatientLifestyleAnswer = {
  id: string;
  answer: string;
  question: MasterLifestyleQuestion;
};

export type CreatePatientLifestyleAnswer = {
  lifestyle_questions: Array<{
    question_id: string;
    answer: string;
  }>;
};

export type UpdatePatientLifestyleAnswer = {
  answer: string;
};
