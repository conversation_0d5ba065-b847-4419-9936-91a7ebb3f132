export type MasterReproductiveHealthQuestion = {
  id: string;
  linkId: string;
  answerOption: any;
  text: string;
  type: string;
  answerValueSet: string;
};

export type PatientReproductiveHealthAnswer = {
  id: string;
  answer: PatientReproductiveHealthSelectAnswer | string;
  question: MasterReproductiveHealthQuestion;
};

export type PatientReproductiveHealthSelectAnswer = {
  code: string;
  display: string;
  system: string;
};

export type CreatePatientReproductiveHealthAnswer = {
  reproductive_health_questions: Array<{
    question_id: string;
    answer: string;
  }>;
};

export type UpdatePatientReproductiveHealthAnswer = {
  answer: string;
};
