// Misc/generic/auxiliary/shared stuff that don't have a better file to be moved to (yet)

export enum EmergencyContactType {
  EmergencyContact = 'emergency-contact',
  AdvancedMedicalDirective = 'advanced-medical-directive',
}

export enum BloodType {
  A_POSITIVE = 'A+',
  A_NEGATIVE = 'A-',
  AB_POSITIVE = 'AB+',
  AB_NEGATIVE = 'AB-',
  B_POSITIVE = 'B+',
  B_NEGATIVE = 'B-',
  O_POSITIVE = 'O+',
  O_NEGATIVE = 'O-',
}

export enum RelationType {
  Mother = 'Mother',
  Father = 'Father',
  Sister = 'Sister',
  Brother = 'Brother',
  Child = 'Child',
  Spouse = 'Spouse',
  MaternalAunt = 'Maternal Aunt',
  MaternalUncle = 'Maternal Uncle',
  PaternalAunt = 'Paternal Aunt',
  PaternalUncle = 'Paternal Uncle',
  MaternalGrandfather = 'Maternal Grandfather',
  MaternalGrandmother = 'Maternal Grandmother',
  PaternalGrandfather = 'Paternal Grandfather',
  PaternalGrandmother = 'Paternal Grandmother',
  Other = 'Other',
}

export enum EmergencyContactRelationType {
  Parent = 'Parent',
  Spouse = 'Spouse',
  Sibling = 'Sibling',
  Child = 'Child',
  AuntUncle = 'Aunt/Uncle',
  Friend = 'Friend',
  Neighbour = 'Neighbour',
  Other = 'Other',
}
export enum HealthcareProxyRelationType {
  Parent = 'Parent',
  Spouse = 'Spouse',
  Sibling = 'Sibling',
  Child = 'Child',
  AuntUncle = 'Aunt/Uncle',
  Friend = 'Friend',
  Neighbour = 'Neighbour',
  Other = 'Other',
}
export enum PersonLivingStatus {
  Alive = 'Alive',
  Deceased = 'Deceased',
}

export enum ProcedureType {
  Surgical = 'Surgical',
  NonSurgical = 'Non Surgical',
}

export enum RecordedByType {
  Self = 'Self',
  Professional = 'Doctor',
}

export enum PositionofRecording {
  Standing = 'Standing',
  Sitting = 'Sitting',
  LyingDown = 'Lying down',
}

export type BaseSearchParams = {
  keyword?: string;
  page?: number;
  limit?: number;
  sort_order?: string;
  sort_by?: string;
};

export enum BASE_FILTERS {
  FROM_DATE = 'from_date',
  TO_DATE = 'to_date',
}

export interface ISidebarProps {
  onClose: () => void;
  isOpen?: boolean;
  name?: string;
  active?: string;
  subActive?: string;
  action?: string;
  clickOutside?: boolean;
}

export enum YesOrNoAnswer {
  Yes = 'Yes',
  No = 'No',
  DontKnow = 'I don’t know',
}

export type TimeOfRecordingOption = {
  label: string;
  value: string;
  hideIcon?: boolean;
};
