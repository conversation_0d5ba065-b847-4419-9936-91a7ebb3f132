export enum NetworkType {
  FH = 'In',
  NonFH = 'Out',
}
export enum FluentNetwork {
  FH = 'Fluent Health',
  NonFH = 'Non Fluent Health',
}
export interface CareTeamMember {
  id: number;
  doctor_id?: number | string;
  name: string;
  speciality: {
    id: number;
    text: string;
  };
  phone_number: string;
  alternate_phone_number: string;
  email: string;
  city: {
    city_id: number;
    city_name: string;
  };
  country: {
    country_id: number;
    country_name: string;
  };
  is_primary: boolean;
  type_of_network: NetworkType | string | null;
}

type SpecialitySubDocument = { speciality: { id: string | number } };
type CitySubDocument = { city: { city_id: string | number } };
type CountrySubDocument = { country: { country_id: string | number } };
export type CareTeamMemberPayload = Omit<CareTeamMember, 'id' | 'speciality' | 'city' | 'country'> &
  SpecialitySubDocument &
  CitySubDocument &
  CountrySubDocument;

export type FHCareTeamMemberPayload = Pick<CareTeamMember, 'is_primary' | 'type_of_network'>;
