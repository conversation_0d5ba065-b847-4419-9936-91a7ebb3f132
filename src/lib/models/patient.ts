import { PatientCommunication } from 'src/gql/graphql';

export enum AddressUse {
  HOME = 'home',
  WORK = 'work',
  TEMP = 'temp',
  OLD = 'old',
  BILLING = 'billing',
}

export enum AddressType {
  POSTAL = 'postal',
  PHYSICAL = 'physical',
  BOTH = 'both',
}

export enum PatientGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  UNKNOWN = 'unknown',
  NONE = '',
}

export enum PatientNameUse {
  USUAL = 'usual',
}

export enum PatientNamePrefix {
  MR = 'Mr',
  MRS = 'Mrs',
  MS = 'Ms',
  DR = 'Dr',
  PROF = 'Prof',
  NONE = '',
}

// Telecommunications form for contact point
// - what communications system is required to make use of the contact
export enum PatientTelecomSystem {
  PHONE = 'phone',
  EMAIL = 'email',
  MOBILE_OTP_TOKEN = 'mobile_otp_token',
}

// Identifies the purpose for the contact point
export enum PatientTelecomUse {
  HOME = 'home',
  MOBILE = 'mobile',
}

export enum PatientEthnicity {
  SOUTH_ASIAN = 'South Asian (Indo/Pak)',
  OTHER_ASIAN = 'Other Asian',
  AFRICAN = 'African',
  AFRICAN_AMERICAN = 'African American',
  SOUTH_AMERICAN = 'South American',
  CAUCASIAN = 'Caucasian',
  LATINO_HISPANIC = 'Latino/Hispanic',
  PACIFIC_ISLANDER = 'Pacific Islander',
  NATIVE_AMERICAN = 'Native American',
  AUSTRALIAN_ABORIGINAL = 'Australian Aboriginal',
  MIDDLE_EASTERNER = 'Middle Easterner',
  ASHKENAZI_JEWISH = 'Ashkenazi Jewish',
  MIXED = 'Mixed',
  OTHER = 'Other',
  UNKNOWN = 'Unknown',
}

export enum PatientPreferredLanguage {
  ENGLISH = 'English',
  HINDI = 'Hindi',
  BENGALI = 'Bengali',
  MARATHI = 'Marathi',
  TELUGU = 'Telugu',
  TAMIL = 'Tamil',
  GUJARATI = 'Gujarati',
  URDU = 'Urdu',
  KANNADA = 'Kannada',
  OTHER = 'Other',
}

// TODO: Marked all/many fields as optional for now TBD which ones are required

export interface PatientAddress {
  use?: AddressUse;
  type?: AddressType;
  text?: string; // Complete patient address. which may contain country, city , state etc.
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  email?: string;
  phone_no?: string;
}

export interface PatientName {
  use?: PatientNameUse; // Identifies the purpose for this name.
  text?: string; // Full name of e.g. patient, practitioner etc
  family?: string; // surname or the family name
  given?: string[]; // A sequence of Unicode characters
  prefix?: PatientNamePrefix[];
}

//
// Details for all kinds of technology mediated contact points for a person or organization,
// including telephone, email, etc
export interface PatientTelecom {
  system?: PatientTelecomSystem;
  use?: PatientTelecomUse;
  value?: string; // A sequence of Unicode characters
}

export interface PatientNumericalUnit {
  value?: string;
  unit?: string; // todo: enum ?
}

export interface PatientManagingOrganization {
  id?: string | number; // Clinic ID
  display?: string; // Clinic name
  reference?: string; // Internal API reference
  type?: string; // Type. e.g - clinic, care provider
}

export interface PatientPhoto {
  contentType?: string;
  data?: string;
  url?: string;
}
export enum PATIENT_IMAGE_UPLOAD_TYPE {
  COVER_IMAGE = 'cover_image',
  PROFILE_IMAGE = 'profile_image',
}

export interface Patient {
  link: any;
  extension: any;
  id: string;
  active: boolean;
  languageValueSet?: any;
  meta: any;
  name: PatientName[]; // A name associated with the individual.
  address?: PatientAddress[];
  birthDate: string; // A date or partial date. See Patient schema
  gender: PatientGender;
  gender_at_birth: PatientGender;
  telecom?: PatientTelecom[];
  patient_no?: string; // Unique medixce patient no
  is_vip?: boolean; // true if patient is VIP false otherwise
  blood_group?: string;
  digital_health_id?: string;
  communication?: PatientCommunication[];
  ethnicity?: any;
  age?: number;
  weight?: PatientNumericalUnit;
  corporate_name?: string;
  plan_name?: string;
  plan_activation_date?: string;
  plan_expiry_date?: string;
  membership_type_id?: string;
  membership_type?: string; // todo: enum?
  annual_charges?: string; // Annual Charges for plan
  creation_date?: string;
  managingOrganization: PatientManagingOrganization;
  deceasedBoolean?: boolean;
  deceasedDateTime?: string;
  photo?: PatientPhoto[];
  height?: PatientNumericalUnit; // This is found under vitals in the MediXcel API
  sex?: string; // (bio: Sex assigned at birth) This is missing from MediXcel
  gatewayUser?: any;
  preferred_language?: string;
}

export type PatientPayload = {
  resourceType?: string;
  id?: string | number;
  name?: PatientName[]; // A name associated with the individual.\
  telecom?: PatientTelecom[];
  gender?: PatientGender;
  birthDate?: string; // A date or partial date. See Patient schema
  address?: PatientAddress[];
  preffered_language?: string;
  ethnicity?: string;
  photo?: ProfilePhotoType[] | null;
};

type ProfilePhotoType = {
  url: string;
};
export type PatientPhotoPayload = {
  upload_type: string;
  photo: PatientPhoto;
};
