interface Extension {
  url: string;
  valueCode: string;
}

interface Relationship {
  label?: string;
  value?: string;
  coding?: {
    system?: string;
    code?: string;
    display?: string;
  }[];
}
export interface FamilyMemberHistory {
  first_name?: string;
  last_name?: string;
  name?: string;
  observations?: any;
  patient?: any;
  id?: string;
  relationship?: Relationship;
  date_of_birth?: string;
  blood_type?: {
    label: string;
    value: string;
  };
  status?: boolean | string;
  ethnicity?: {
    label: string;
    value: string;
  };
  FamilyMemberHistoryList?: any[];
  resourceType?: string;
  extension?: Extension[];
  deceasedBoolean?: boolean | string;
  bornDate?: string;
  condition?: any;
}

export type FamilyMemberHistoryPayload = FamilyMemberHistory;
