import { AttachedMedicalRecord } from '@lib/models/medical-record';

export enum ALERT_TYPES {
  SMS = 'sms',
  EMAIL = 'email',
  DISPLAY = 'display',
}

export enum ALERT_STATUSES {
  PENDING = 'Pending',
  SKIPPED = 'Skipped',
  SENT = 'Sent',
}

// Todo valueSet need to come via api
export enum ALERT_FREQUENCY {
  ONCE = 'rf-donotrepeat',
  DAILY = 'rf-everyday',
  WEEKLY = 'rf-everyweek',
  MONTHLY = 'rf-everymonth',
  YEARLY = 'rf-everyyear',
  CUSTOM = 'rf-custom',
}

export enum CUSTOM_ALERT_FREQUENCY_UNITS {
  HOURS = 'rfb-hours',
  DAYS = 'rfb-days',
  WEEKS = 'rfb-weeks',
  MONTHS = 'rfb-months',
  YEARS = 'rfb-years',
}

export interface AlertSchedule {
  alert_date: string;
  alert_time: string;
  status: ALERT_STATUSES;
}

export interface Alert {
  id: string | undefined | null;
  type: ALERT_TYPES;
  title: string;
  frequency: ALERT_FREQUENCY;
  alert_subject: string;
  alert_message: string;
  alert_start_date: string;
  alert_end_date: string;
  alert_time: string;
  all_day: boolean;
  schedules: AlertSchedule[];
  external_reports: AttachedMedicalRecord[];
}

export type AlertPayload = {
  id?: string;
  external_reports?: Array<AttachedMedicalRecord['id']>;
  resourceType: string;
  subject: Subject;
  payload: Payload[];
  occurrenceDateTime: string | null;
  note: Note[] | null;
  status: string;
  extension: any;
  category: any;
};

// Replace 'CommunicationRequestList' with the correct property name from CommReqQuery, for example 'communicationRequests'
export type Reminder = NonNullable<any>;
export interface Note {
  text: string;
}

export interface Subject {
  reference: string;
  display: any;
}

export interface Payload {
  contentAttachment?: ContentAttachment;
  contentString?: string;
}
export interface ContentAttachment {
  title: string;
}

export interface Extension {
  url: string;
  extension: ExtensionExtension[];
}

export interface ExtensionExtension {
  url: string;
  valueBoolean?: boolean;
  valueCode?: string;
}

export interface Bundle {
  resourceType: string;
  type: string;
  entry: Entry[];
}

export interface Entry {
  response: Response;
}

export interface Response {
  outcome: Outcome;
  status: string;
}

export interface Outcome {
  resourceType: string;
  id: string;
  issue: Issue[];
}

export interface Issue {
  severity: string;
  code: string;
  details: Details;
}

export interface Details {
  text: string;
}
