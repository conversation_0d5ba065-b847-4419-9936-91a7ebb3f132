export interface HealthInsurance {
  id: number;
  file: HealthInsuranceFile[];
  policy_number: string;
  contact_number: string;
  insurance_company_id: string;
  insurance_company_name: string;
}

export interface HealthInsuranceCompany {
  company_id: string;
  company_name: string;
}

export interface HealthInsuranceFile {
  contentType: string;
  data: string;
  url: string;
}

export interface HealthInsuranceFilePayload {
  file_type: string;
  file: HealthInsuranceFile;
}

export type HealthInsurancePayload = Pick<HealthInsurance, 'policy_number' | 'contact_number' | 'insurance_company_id'>;
