import { EmergencyContactRelationType, EmergencyContactType } from './misc';
import { PatientName } from '@lib/models/patient';

export interface EmergencyContact {
  id: number;
  name: PatientName;
  relation: EmergencyContactRelationType;
  contact_no: string;
  type: EmergencyContactType;
  file: EmergencyContactFile[];
}

export interface EmergencyContactName {
  text: string;
  family: string;
}

export interface EmergencyContactFile {
  contentType: string;
  data: string;
  url: string;
}
export interface EmergencyContactFilePayload {
  file_type: string;
  file: EmergencyContactFile;
}

export type EmergencyContactPayload = Omit<EmergencyContact, 'id' | 'file'>;
