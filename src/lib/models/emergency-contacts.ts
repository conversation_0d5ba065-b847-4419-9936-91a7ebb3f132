export enum RelationType {
  Mother = 'Mother',
  Father = 'Father',
  Sister = 'Sister',
  Brother = 'Brother',
  Spouse = 'Spouse',
  Son = 'Son',
  Daughter = 'Daughter',
  Other = 'Other',
}

export enum EmergencyContactType {
  EmergencyContact = 'emergency-contact',
  AdvancedMedicalDirective = 'advanced-medical-directive',
}

export interface PatientEmergencyContact {
  name: string;
  relation: RelationType;
  contact_no: string;
  type: EmergencyContactType;
}
