export enum RequestConsentStatusType {
  Living = 'Living',
  Deceased = 'Deceased',
}

type ConsentTask = {
  id: string;
  provision?: {
    type: string;
  };
  status?: string;
  sourceReference?: {};
};

type Consent = {
  map(arg0: (consent: any) => JSX.Element): import('react').ReactNode;
  item: any;
  consentTask: ConsentTask | undefined;
  id: string;
  name: string;
  alert?: string;
};

export interface ConsentManager {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  consentStatus: {
    label: string;
    value: string;
  };
  emailAddress: string;
  mobileNumber?: string;
}

export type ConsentManagerPayload = Omit<ConsentManager, 'id'> & {};

export interface ConsentRequestedProps {
  id?: string;
  consents: Consent[];
  consentTask?: ConsentTask[];
  handleConsentClick: (consent: Consent, isTurning18Soon?: any) => void; // Define the expected function type
}

export interface ConsentItemProps {
  id?: string;
  consent: Consent;
  consentTask?: ConsentTask;
  handleConsentClick: (consent: Consent, isTurning18Soon?: any) => void;
}
