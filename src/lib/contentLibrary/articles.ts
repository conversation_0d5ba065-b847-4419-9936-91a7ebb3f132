import ApiError from '@components/error/ApiError';

import { cmsAPI, contentLibraryAPI } from '.';
import { articleDetailsQuery } from './cms-article-graphql-queries';
import { ArticleDetail, ArticleInfo } from './types';
import { fluentAPI } from '@lib/api';

const contentArticles = {
  async fetchRecommendedArticles(): Promise<ArticleInfo[]> {
    try {
      return await contentLibraryAPI
        .get('/recommendation/auto', {
          method: 'GET',
        })
        .then((response) => {
          return response.data;
        });
    } catch (error) {
      throw new ApiError(error);
    }
  },
  async bookMarkArticle(articleId: string) {
    try {
      return await contentLibraryAPI
        .post('/interaction', {
          referenceType: 'ARTICLE',
          referenceId: articleId,
          type: 'ARTICLE_BOOKMARK',
        })
        .then((response) => {
          return response.data;
        });
    } catch (error) {
      throw new ApiError(error);
    }
  },
  async unBookMarkArticle(interactionId: string) {
    try {
      return await contentLibraryAPI.delete(`/interaction/${interactionId}`).then((response) => {
        return response.data;
      });
    } catch (error) {
      throw new ApiError(error);
    }
  },
  async fetchArticlesDetails(ids: string[]): Promise<{ data: { articles: ArticleDetail[] } }> {
    try {
      // console.log(ids);
      return await cmsAPI
        .post('', {
          query: articleDetailsQuery(),
          variables: {
            IDs: ids.sort(() => 0.5 - Math.random()).slice(0, 6),
          },
        })
        .then((response) => {
          return response.data;
        });
    } catch (error) {
      throw new ApiError(error);
    }
  },
  async fetchAsset(id: string): Promise<string> {
    try {
      const response = await fluentAPI.get(`/cms/assets/${id}`, {
        responseType: 'arraybuffer',
      });
      const blob = new Blob([response.data], { type: response.headers['content-type'] });
      const url = URL.createObjectURL(blob);
      return url;
    } catch (error) {
      throw new ApiError(error);
    }
  },
};

export default contentArticles;
