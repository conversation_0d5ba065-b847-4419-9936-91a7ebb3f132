import axios from 'axios';

import { CMS_GRAPHQL_API_URL, CONTENT_LIBRARY_API_URL } from '@lib/constants';
import { AuthService } from '@lib/authService';
import { enumContentType } from 'src/app/medical-records/lib/state';

const contentLibraryAPI = axios.create({
  baseURL: CONTENT_LIBRARY_API_URL,
});

contentLibraryAPI.interceptors.request.use((config: any) => {
  return {
    ...config,
    headers: {
      ...config.headers,
      ...AuthService.instance.withAuthHeader(),
      'Content-Type': enumContentType.JSON,
    },
  };
});

contentLibraryAPI.defaults.withCredentials = true;

const cmsAPI = axios.create({
  baseURL: CMS_GRAPHQL_API_URL,
});

cmsAPI.interceptors.request.use((config: any) => {
  const headers = { ...config.headers };

  // Check if the `skipAuth` flag is set and remove it from headers
  const { skipAuth, ...remainingHeaders } = headers;

  // Return the updated config with appropriate headers
  return {
    ...config,
    headers: {
      ...remainingHeaders,
      ...(skipAuth ? {} : AuthService.instance.withAuthHeader()),
      'Content-Type': enumContentType.JSON,
    },
  };
});

cmsAPI.defaults.withCredentials = true;

export { contentLibraryAPI, cmsAPI };
