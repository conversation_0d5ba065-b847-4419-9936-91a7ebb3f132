import { useLocalStorage } from 'usehooks-ts';
import { useQuery } from '@tanstack/react-query';

import { LOCAL_STORAGE_KEYS } from '@lib/constants';
import appSettingsAPI from '../appSettings/appSettings';
import { AppStaticSettings, FAQItem } from '../appSettings/types';
import { useAuthService } from '@lib/state';

export const useFetchAppSettingData = (): {
  data: AppStaticSettings;
  faqs: FAQItem[];
  error: any;
  isLoading: boolean;
  isFetching: boolean;
} => {
  const [localData, setLocalData] = useLocalStorage<any>(LOCAL_STORAGE_KEYS.APP_SETTINGS, null);
  const [localFaqs, setLocalFaqs] = useLocalStorage<any>(LOCAL_STORAGE_KEYS.FAQS, null);
  const { isLoggedIn = false } = useAuthService();
  const { data, error, isLoading, isFetching } = useQuery(
    ['app_static_settings', isLoggedIn],
    () => appSettingsAPI.fetchAppSettings(isLoggedIn),
    {
      enabled: !localData,
      onSuccess: (fetchedData) => {
        if (fetchedData) {
          setTimeout(() => setLocalData(fetchedData), 0);
        }
      },
      staleTime: 5000 * 60 * 60,
    }
  );

  const { data: faqsData } = useQuery(['faqs', isLoggedIn], () => appSettingsAPI.fetchFaqs(isLoggedIn), {
    enabled: !localFaqs,
    onSuccess: (fetchedFaqs) => {
      setLocalFaqs(fetchedFaqs);
    },
    staleTime: 5000 * 60 * 60,
  });

  return {
    data: localData || data,
    faqs: localFaqs || faqsData,
    error,
    isLoading: !localData && isLoading,
    isFetching,
  };
};
