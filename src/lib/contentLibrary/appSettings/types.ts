export type ContentBlock = {
  id: string;
  title: string;
  content: string;
};
export type FAQItem = {
  faq_group: string;
  faq_type: string;
  answer: string;
  question: string;
};

export type FAQContent = {
  faqs_id: FAQItem;
};

type SocialMediaLink = {
  id: string;
  title: string;
  link: string;
  application: any;
  icon_patapp: {
    storage: string;
    filename_disk: string;
    filename_download: string;
    id: string;
  };
};

export type AppStaticSettings = {
  id: string;
  account_settings_title: string;
  account_settings_browser_notification_title: string;
  section_measurement_preferences_title: string;
  section_communication_preferences_title: string;
  data_and_privacy_title: string;
  section_consent_manager_title: string;
  section_personal_data_usage_title: string;

  section_personal_data_usage_content: ContentBlock;

  section_third_party_consent_title: string;
  section_third_party_consent_content: ContentBlock;

  section_legal_title: string;
  section_legal_terms_title: string;
  section_legal_terms_content: any;
  section_legal_privacy_title: string;
  section_legal_privacy_content: any;
  section_legal_cookie_title: string;
  section_legal_cookie_content: any;
  section_legal_consent_title: string;
  section_legal_consent_content: any;
  section_legal_medadvice_title: string;
  section_legal_medadvice_content: any;

  support_title: string;
  section_fluent_support_title: string;
  section_faqs_title: string;
  section_emailus_title: string;
  section_faq_content: FAQContent[];

  section_fluent_support_content: ContentBlock;

  sharing_title: string;
  section_socialmedia_title: string;
  section_socialmedia_links: {
    id: string;
    socialmedia_links_id: SocialMediaLink;
  }[];
};
