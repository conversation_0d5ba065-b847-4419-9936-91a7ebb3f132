import ApiError from '@components/error/ApiError';

import { cmsAPI } from '..';
import { appStaticSettingsQuery, faqsQuery } from './cms-settings-graphql-queries';
import { AppStaticSettings, FAQItem } from './types';

const appSettingsAPI = {
  async fetchAppSettings(isLoggedIn: boolean): Promise<AppStaticSettings> {
    try {
      return await cmsAPI
        .post(
          '',
          {
            query: appStaticSettingsQuery(isLoggedIn),
          },
          {
            headers: {
              skipAuth: !isLoggedIn,
            },
          }
        )
        .then((response) => {
          return response.data?.data?.app_static_settings;
        });
    } catch (error) {
      throw new ApiError(error);
    }
  },
  async fetchFaqs(isLoggedIn: boolean): Promise<FAQItem[]> {
    try {
      return await cmsAPI
        .post(
          '',
          {
            query: faqsQuery(),
          },
          {
            headers: {
              skipAuth: !isLoggedIn,
            },
          }
        )
        .then((response) => {
          return response.data?.data?.faqs;
        });
    } catch (error) {
      throw new ApiError(error);
    }
  },
};

export default appSettingsAPI;
