export const appStaticSettingsQuery = (isLoggedIn: boolean) => {
  if (!isLoggedIn) {
    return `query app_static_settings {
      app_static_settings {
        section_legal_terms_title
        section_legal_terms_content
        section_legal_privacy_title
        section_legal_privacy_content
      }
    }
    `;
  }

  return `query app_static_settings {
            app_static_settings {
              id
              account_settings_title
              section_measurement_preferences_title
              account_settings_browser_notification_title
              section_communication_preferences_title
              data_and_privacy_title
              section_consent_manager_title
              section_personal_data_usage_title
              section_personal_data_usage_content {
                title
                content
              }
              section_third_party_consent_title
              section_third_party_consent_content {
                title
                content
              }
              section_legal_title
              section_legal_terms_title
              section_legal_terms_content
              section_legal_privacy_title
              section_legal_privacy_content
              section_legal_cookie_title
              section_legal_cookie_content
              section_legal_consent_title
              section_legal_consent_content
              section_legal_medadvice_title
              section_legal_medadvice_content
              support_title
              section_fluent_support_title
              section_faqs_title
              section_fluent_support_title
              section_fluent_support_content {
                title
                content
              }
              sharing_title
              section_socialmedia_title
              section_socialmedia_links {
                id
                socialmedia_links_id {
                  id
                  title
                  link
                  application
                  icon_patapp {
                    storage
                    filename_disk
                    filename_download
                    id
                  }
                }
              }
            }
}
`;
};
export const faqsQuery = () => {
  return `query Faqs {
    faqs(
      filter: { faq_group: { faq_group_type: { _contains: "app" } }, status: { _eq: "published" } }
      sort: ["sort", "faq_group.faq_group_title", "question"]
    ) {
      id
      faq_group {
        faq_group_title
        faq_group_type
      }
      question
      answer
      status
      image {
        id
        filename_disk
      }
    }
  }`;
};
