export const articleDetailsQuery = () => {
  // TODO: Recommendation teaser card for launch as it's not in scope. Need to change the below query as it not correct.
  return `query articles($IDs: [String]) {
    articles(filter: { article_type: { _eq: "app" }, id: { _in: $IDs } }) {
      id
      article_title
      article_teaser
      article_type
      article_hero_image {
        id
        title
        filename_download
      }
      primary_topic {
        fact_code {
          fact_code
          fact_display
        }
      }
      article_reviewer {
        reviewer_name
        reviewer_qualification
      }
    }
}
`;
};
