export type INTERACTION_TYPES =
  | 'ARTICLE_READ'
  | 'ARTICLE_LIKE'
  | 'ARTICLE_DISLIKE'
  | 'ARTICLE_SHARE'
  | 'ARTICLE_BOOKMARK';

export type ArticleDetail = {
  id: string;
  article_title: string;
  article_teaser?: string | null;
  article_hero_image: {
    id: string;
    title: string;
    filename_download: string;
  };
  primary_topic?: {
    fact_code: {
      fact_code: string;
      fact_display: string;
    } | null;
  } | null;
  article_reviewer: {
    reviewer_name: string;
    reviewer_qualification?: string | null;
  };
};

type Interaction = {
  id: string;
  referenceId: string;
  referenceType: string;
  type: INTERACTION_TYPES;
  createdAt: string;
};

type Article = {
  id: string;
  title: string;
  active: boolean;
  interactions: Interaction[];
  isLiked: boolean;
  isDisliked: boolean;
  isRead: boolean;
  isShared: boolean;
  isBookmarked: boolean;
  tags: string[];
  globalReadCount: number;
};

export type ArticleInfo = {
  articleId: string;
  score: number;
  article: Article;
};

export type FAQ = {
  id: string;
  question: string;
  answer: string;
  faq_type: string;
  faq_group: string;
};
