import { graphql } from 'src/gql';

export const getAllAlertsQuery = `
  query CommReqList($patientId: String!) {
    CommunicationRequestList(patient: $patientId,  category: "reminder", _count: 1000, _filter: "_tag ne delete") {
      __typename  
      id
      resourceType
      

      payload {
        contentString
       
      }
      occurrenceDateTime
      subject {
        reference
}
      note {
        text
      }
      category {
        coding {
          system
          code
          display
        }
      }
      extension {
        url
        valueTiming {
          repeat {
            frequency
            period
            periodUnit
          }
        }
        valueBoolean
         valueCodeableConcept{
            coding{
                system
                code
                display
            },
            text
        }
      }
      status # status of communication
     
    }
  }
`;

export const getAlertQuery = graphql(`
  query CommReq($alertId: ID!) {
    CommunicationRequest(id: $alertId) {
      __typename
      id
      resourceType

      payload {
        contentString
      }
      occurrenceDateTime
      subject {
        reference
      }
      note {
        text
      }
      category {
        coding {
          system
          code
          display
        }
      }
      extension {
        url
        valueTiming {
          repeat {
            frequency
            period
            periodUnit
          }
        }
        valueBoolean
        valueCodeableConcept {
          coding {
            system
            code
            display
          }
          text
        }
      }
      status # status of communication
    }
  }
`);

// export const getAllAlertsQuery = graphql(/* GraphQL */ `
//   query allFilmsWithVariablesQuery($patientId: String!, $reminderUrl: String) {
//     allFilms(first: $patientId) {
//       edges {
//         node {
//           ...FilmItem
//         }
//       }
//     }
//   }
// `);
// const alerts = {
//   getAllAlerts() {
//     const query = graphql(`
//       query CommReqList($patientId: String!, $reminderUrl: String) {
//         CommunicationRequestList(patient: $patientId) {
//           __typename
//           id

//           payload {
//             contentAttachment {
//               title # for remind me to
//             }
//           }

//           occurrenceDateTime

//           note {
//             text
//           }

//           status # status of communication
//           frequency: extension(url: $reminderUrl) {
//             extension {
//               valueCode
//               valueBoolean
//               url
//             }
//           }
//         }
//       }
//     `);
//     return query;
//   },
// };

// export const medplumGraphQlQuery = {
//   alerts,
// };

export const getFileFromTask = `
  query getTask($id: ID!) {
      Task(id: $id){
        id
        status
        output {
          type {
            coding {
              code
              display
            }
          }
          valueReference {
            reference
          }
        }
      }
    }`;

export const getDocumentReference = `
  query getDocumentReference($id: ID!) {
    DocumentReference(id: $id) {
      id
      status
      type {
        coding {
          code
          display
        }
      }
      content {
        attachment {
          contentType
          url
          title
        }
      }
      subject {
        reference
        display
      }
      author {
        reference
        display
      }
      date
      description
    }
  }
`;
