import { FH_CODE_SYSTEM_FACT, LOINC_URL, UNITS_MEASURE_URL } from 'src/constants/medplumConstants';

/**
 * Enum for FHIR Observation Status.
 */
export enum ObservationStatus {
  /**
   * The existence of the observation is registered, but there is no result yet available.
   */
  Registered = 'registered',

  /**
   * This is an initial or interim observation: data may be incomplete or unverified.
   */
  Preliminary = 'preliminary',

  /**
   * The observation is complete, and there are no further actions needed.
   */
  Final = 'final',

  /**
   * Subsequent to being Final, the observation has been modified. This includes updates, new information, and corrections.
   */
  Amended = 'amended',

  /**
   * Subsequent to being Final, the observation has been modified to correct an error in the test result.
   */
  Corrected = 'corrected',

  /**
   * The observation is unavailable because the measurement was not started or not completed (also sometimes called "aborted").
   */
  Cancelled = 'cancelled',

  /**
   * The observation has been withdrawn following previous final release. This electronic record should never have existed.
   */
  EnteredInError = 'entered-in-error',

  /**
   * The authoring/source system does not know which of the status values currently applies for this observation.
   */
  Unknown = 'unknown',
}

export interface ObservationType {
  name: string;
  code: string;
  codeDisplay: string;
  system: string;
  categoryCode: string;
  categoryDisplay: string;
  unit?: string;
  unitSystem?: string;
  valueCodeUrl?: string;
}

export enum ObservationNames {
  Height = 'Height',
  Weight = 'Weight',
  BloodType = 'Blood Type',
}

export const observationTypes: ObservationType[] = [
  {
    name: ObservationNames.Height,
    code: '8302-2',
    codeDisplay: 'Body Height',
    system: LOINC_URL,
    categoryCode: 'vital-signs',
    categoryDisplay: 'vital-signs',
    unit: 'cm',
    unitSystem: UNITS_MEASURE_URL,
  },
  {
    name: ObservationNames.Weight,
    code: '29463-7',
    codeDisplay: 'Body Weight',
    system: LOINC_URL,
    categoryCode: 'vital-signs',
    categoryDisplay: 'vital-signs',
    unit: 'kg',
    unitSystem: UNITS_MEASURE_URL,
  },
  {
    name: ObservationNames.BloodType,
    code: '883-9',
    codeDisplay: 'Blood Group',
    system: LOINC_URL,
    categoryCode: 'laboratory',
    categoryDisplay: 'laboratory',
    valueCodeUrl: FH_CODE_SYSTEM_FACT,
  },
];
