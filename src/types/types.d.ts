/**
 * Props for the AddDropdown component.
 */
export interface AddDropdownProps {
  options: string[] | undefined;
  defaultValue: string | undefined;
  onSelect?: (value: string) => void;
  onClear?: (value: string) => void;
  isAdding: boolean;
  setIsAdding: React.Dispatch<React.SetStateAction<boolean>> | undefined;
  showClearButton?: boolean;
}

/**
 * Props for the AddInputField component.
 */

interface AddInputFieldProps {
  defaultValue: string;
  onSelect?: (value: string) => void;
  onClear?: (value: string) => void;
  isAdding: boolean;
  setIsAdding: React.Dispatch<React.SetStateAction<boolean>> | undefined;
  maxCharacters?: number;
  maxDecimalPoints?: number;
  onInputChange?: (value: string) => void;
}
