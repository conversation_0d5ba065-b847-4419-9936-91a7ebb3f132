// Package modules
import { ChakraProps, Flex, Heading } from '@chakra-ui/react';

// Local modules
import { FluentHealthLogoAnimation } from './FluentHealthLogoAnimation';

export function FluentHealthLoader({ showHeading = true, ...props }: ChakraProps & { showHeading?: boolean }) {
  return (
    <Flex
      direction="column"
      align="center"
      justify="center"
      gap="24px"
      {...props}
    >
      <FluentHealthLogoAnimation />
      {showHeading && (
        <Heading
          fontSize="2xl"
          color="fluentHealthSecondary.100"
        >
          Processing...
        </Heading>
      )}
    </Flex>
  );
}
