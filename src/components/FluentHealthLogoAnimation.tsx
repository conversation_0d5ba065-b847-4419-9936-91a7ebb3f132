// Package modules
import { isValidMotionProp, motion } from 'framer-motion';
import { Box, Flex, chakra, shouldForwardProp, useTheme } from '@chakra-ui/react';

// Local modules
import { hexOpacity } from './theme/utils';

// eslint-disable-next-line @typescript-eslint/naming-convention
const AnimatedBox = chakra(motion.div, {
  /**
   * Allow motion props and non-Chakra props to be forwarded.
   */
  shouldForwardProp: (prop) => isValidMotionProp(prop) || shouldForwardProp(prop),
});

export function FluentHealthLogoAnimation() {
  const theme = useTheme();

  return (
    <Flex
      position="relative"
      justify="center"
      height="101px"
    >
      <Flex
        position="absolute"
        top="0"
        left="0"
        right="0"
        mx="auto"
        direction="column"
        align="center"
        justify="center"
      >
        <AnimatedBox
          width="115px"
          height="45px"
          borderTopRadius="full"
          bgColor={hexOpacity(theme.colors.fluentHealthSecondary[150], 0.3)}
          animate={{
            width: [115, 60, 115],
            height: [45, 23, 45],
            backgroundColor: [
              hexOpacity(theme.colors.fluentHealthSecondary[150], 0.3),
              hexOpacity(theme.colors.fluentHealthSecondary[150], 1),
              hexOpacity(theme.colors.fluentHealthSecondary[150], 0.3),
            ],
          }}
          // @ts-ignore no problem in operation, although type error appears.
          transition={{
            duration: 2,
            ease: 'easeInOut',
            repeat: Infinity,
            repeatType: 'loop',
          }}
        />
        <Box
          width="87px"
          height="33px"
          borderTopRadius="9999px"
          bgColor={hexOpacity(theme.colors.fluentHealthSecondary[150], 0.5)}
        />
        <AnimatedBox
          width="60px"
          height="23px"
          borderTopRadius="9999px"
          bgColor={hexOpacity(theme.colors.fluentHealthSecondary[150], 1)}
          animate={{
            width: [60, 115, 60],
            height: [23, 45, 23],
            backgroundColor: [
              hexOpacity(theme.colors.fluentHealthSecondary[150], 1),
              hexOpacity(theme.colors.fluentHealthSecondary[150], 0.3),
              hexOpacity(theme.colors.fluentHealthSecondary[150], 1),
            ],
          }}
          // @ts-ignore no problem in operation, although type error appears.
          transition={{
            duration: 2,
            ease: 'easeInOut',
            repeat: Infinity,
            repeatType: 'loop',
          }}
        />
      </Flex>
    </Flex>
  );
}
