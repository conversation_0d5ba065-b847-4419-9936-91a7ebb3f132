// Package modules
import { extendTheme } from '@chakra-ui/react';

// Component style overrides
import { buttonStyles } from './components/button';
import { checkboxStyles } from './components/checkbox';
import { headingStyles } from './components/heading';
import { inputTheme } from './components/input';
import { linkStyles } from './components/link';
import { modalStyles } from './components/modal';
import { pinInputStyles } from './components/pininput';
import { formStyles } from './components/form';
import { containerStyles } from './components/container';
import { switchStyles } from './components/switch';
import { radioStyles } from './components/radio';
import { alertStyles } from './components/alert';
import { textareaTheme } from './components/textarea';
import { tooltipTheme } from './components/tooltip';
import { colors } from './colors';

export const styles = {
  global: {
    body: {
      bg: 'gradient.page',
      color: colors.fluentHealthText['100'],
      minHeight: '100vh',
      fontWeight: '400',
      letterSpacing: '-0.02em',
    },
    '.hide-scrollbar::-webkit-scrollbar': {
      display: 'none',
      msOverflowStyle: 'none',
      scrollbarWidth: 'none',
    },
  },
};

export const typography = {
  fonts: {
    heading: `'P22Mackinac', sans-serif`,
    body: `'Apercu', sans-serif`,
  },
  lineHeights: {
    '10': '0.875rem',
    '11': '2.75rem',
  },
};

export const radius = {
  radii: {
    '4xl': '1.75rem',
  },
};

export const theme = extendTheme({
  styles,
  colors,
  ...typography,
  ...radius,
  components: {
    Button: buttonStyles,
    Heading: headingStyles,
    Link: linkStyles,
    Form: formStyles,
    Input: inputTheme,
    Textarea: textareaTheme,
    PinInput: pinInputStyles,
    Container: containerStyles,
    Switch: switchStyles,
    Radio: radioStyles,
    Checkbox: checkboxStyles,
    Alert: alertStyles,
    Modal: modalStyles,
    Tooltip: tooltipTheme,
  },
});
