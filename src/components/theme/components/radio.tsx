export const radioStyles = {
  baseStyle: {
    control: {
      borderWidth: 1,
      borderColor: 'gray.200',

      '&[data-checked]': {
        borderColor: 'royalBlue.300',
        bg: 'transparent',
        color: 'royalBlue.500',
        borderWidth: '1.5px',
      },
      '&[data-checked]::before': {
        width: '10px',
        height: '10px',
      },
      '&[data-checked]:hover': {
        bg: 'transparent',
      },
      '&:focus-visible': {
        boxShadow: 'none',
      },
      '&[data-disabled], &[data-disabled][data-checked]': {
        color: 'fluentHealthText.400',
      },
      '&[data-disabled]:not([data-checked])': {
        bg: 'fluentHealthText.500',
      },
    },
  },
  variants: {
    checkbox: {
      control: {
        '&[data-checked]::before': {
          width: '16px',
          height: '16px',
          flexShrink: 0,
          backgroundImage: 'url("/check-icon.svg")',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        },
      },
    },
  },
};
