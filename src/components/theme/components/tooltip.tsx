import { defineStyle, defineStyleConfig } from '@chakra-ui/react';

// define the base component styles
const baseStyle = {
  bgColor: 'gray.500',
  borderRadius: '16px',
  marginTop: '5px',
  padding: '16px',
  fontSize: 'sm',
  placement: 'bottom',
  minW: '400px',
  '& .chakra-tooltip__arrow': {
    background: 'var(--chakra-colors-gray-500) !important',
  },
};

// define custom sizes
const sizes = {
  sm: defineStyle({
    fontSize: 'sm',
    py: '2',
    px: '2',
    maxW: '200px',
  }),
  md: defineStyle({
    fontSize: 'md',
    py: '2',
    px: '2',
    maxW: '300px',
  }),
  lg: defineStyle({
    fontSize: 'lg',
    py: '2',
    px: '2',
    maxW: '400px',
  }),
};

// define styles for custom variant
const defaultVariant = defineStyle(() => {
  return {};
});

// define custom variants
const variants = {
  default: defaultVariant,
};

// define which sizes, variants, and color schemes are applied by default
const defaultProps = {
  size: 'lg',
  variant: 'default',
  colorScheme: 'brand',
};

// export the component theme
export const tooltipTheme = defineStyleConfig({
  baseStyle,
  sizes,
  variants,
  // @ts-ignore
  defaultProps,
});
