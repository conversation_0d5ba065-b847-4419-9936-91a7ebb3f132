export const pinInputStyles = {
  baseStyle: {
    color: '#41399D',
  },
  sizes: {
    fh: {
      w: '64px',
      h: '80px',
      fontSize: '6xl',
      fontWeight: '700',
      lineHeight: '4.5rem',
    },
  },
  variants: {
    outline: {
      borderRadius: '12px',
      border: '1px solid',
      borderColor: ' #DADCFF',
      _focus: {
        boxShadow: '0px 0px 0px 3px #DADCFF',
        border: '1px solid #41399D',
      },
      _invalid: {
        color: '#EA4747',
      },
    },
  },
  defaultProps: {
    size: 'fh',
  },
};

// TODO: Check defineStyle
