import { inputAnatomy } from '@chakra-ui/anatomy';
import { createMultiStyleConfigHelpers, defineStyle } from '@chakra-ui/react';

import { colors } from '../colors';
import { hexOpacity } from '../utils';

const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(inputAnatomy.keys);

const xl = defineStyle({
  h: '14',
  borderRadius: 'full',
});

const sizes = {
  xl: definePartsStyle({ field: xl, addon: xl, element: xl }),
};

const rounded = definePartsStyle({
  field: {
    px: '16px',
    background: 'white',
    // Pseudo
    _placeholder: {
      color: 'gray.400',
    },
  },
  addon: {},
  element: {
    borderRadius: 'full',
  },
});

const roundedTransparent = definePartsStyle({
  field: {
    px: '16px',
    background: 'transparent',
    border: '1px solid',
    borderColor: 'iris.500',
    _placeholder: {
      color: hexOpacity(colors.periwinkle[700], 0.6),
    },
    _focus: {
      background: hexOpacity('#ffffff', 0.2),
    },
  },
  addon: {},
  element: {
    borderRadius: 'full',
  },
});

export const inputTheme = defineMultiStyleConfig({
  sizes,
  variants: { rounded, roundedTransparent },
});
