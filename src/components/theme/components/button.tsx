export const buttonStyles = {
  baseStyle: {
    height: 'auto',
    fontWeight: '500',
    lineHeight: '1',
    borderRadius: '55px',
    letterSpacing: '-0.02em',
  },
  sizes: {
    xl: {
      fontSize: 'lg',
      lineHeight: '26px',
      padding: '12px 24px',
    },
  },
  variants: {
    outline: {
      fontSize: 'md',
      height: 'auto',
    },
    ghost: {
      fontSize: 'md',
      height: 'auto',
    },
    quiet: {
      px: 0,
      py: 0,
    },
    quietDanger: {
      px: 0,
      py: 0,
      color: 'fluentHealthComplementary.Red',
    },
    quietPrimary: {
      px: 0,
      py: 0,
      color: 'fluentHealth.500',
    },
    solid: {
      _disabled: {
        backgroundColor: 'gray.200',
        borderColor: 'gray.200',
        opacity: 1,
      },
      _hover: {
        _disabled: {
          backgroundColor: 'gray.200',
          borderColor: 'gray.200',
          pointerEvents: 'none',
        },
      },
    },
  },
  defaultProps: {
    colorScheme: 'iris',
  },
};
