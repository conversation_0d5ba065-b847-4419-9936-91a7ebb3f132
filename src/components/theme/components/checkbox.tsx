export const checkboxStyles = {
  baseStyle: {},
  variants: {
    toggle: {
      control: {
        position: 'relative',
        width: '28px',
        height: '16px',
        borderRadius: '30px',
        border: 'none',
        bgColor: 'gray.200',
        '&[data-checked], &[data-checked]:hover': {
          bgColor: 'iris.500',
        },
        '&[data-focus-visible]': {
          boxShadow: 'none',
        },
        _focusWithin: {
          boxShadow: 'none',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: '2px',
          left: '2px',
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          filter: 'drop-shadow(0px 10px 28px rgba(7, 16, 84, 0.14)) drop-shadow(0px 1px 4px rgba(7, 16, 84, 0.10))',
          bgColor: 'white',
          transition: 'left 0.1s ease-in-out',
        },
        '&[data-checked]::before': {
          left: '14px',
        },
      },
      icon: {
        display: 'none',
      },
    },
  },
};
