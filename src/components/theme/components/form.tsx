const FLOATING_INPUT_STYLES = {
  color: 'iris.500',
  paddingLeft: '0',
  borderRadius: '0',
  borderColor: 'iris.500',
  borderTop: '0',
  borderLeft: '0',
  borderRight: '0',
  fontSize: 'lg',
  letterSpacing: '-0.02em',

  _focusWithin: {
    borderBottomColor: 'iris.500',
  },
  _focusVisible: {
    borderTop: '0',
    borderLeft: '0',
    borderRight: '0',
    boxShadow: 'unset',
    borderBottomColor: 'iris.500',
  },
  _invalid: {
    borderBottomColor: 'papaya.600',
    boxShadow: 'none',
  },
  _disabled: {
    color: 'gray.300',
    opacity: 1,
    '& + label': {
      color: 'gray.300',
    },
  },
  // On hover - if not focused then change color
  '&:not(:focus-within):placeholder-shown:hover + label': {
    color: 'periwinkle.700',
  },
  // On hover - if valid then change color and border color
  '&:not(input[aria-invalid]):hover, &:not(input[aria-invalid=true]):hover': {
    borderBottomColor: 'periwinkle.700',
    color: 'periwinkle.700',
  },
  // There is a value - move the label up.
  '&:not(:placeholder-shown) + label': {
    transform: 'translateY(-115%)',
    color: 'gray.300',
    fontSize: 'sm',
  },
};

const FLOATING_INPUT_LABEL_STYLES = {
  color: 'iris.500',
  fontSize: 'lg',
  fontWeight: 400,
  top: 0,
  left: 0,
  zIndex: 2,
  position: 'absolute',
  backgroundColor: 'transparent',
  pointerEvents: 'none',
  px: 0,
  mr: 3,
  my: 2,
  transformOrigin: 'left top',
  transition: 'font-size 0.2s ease, transform 0.2s ease',
};

export const formStyles = {
  variants: {
    floatingTel: {
      container: {
        input: {
          ...FLOATING_INPUT_STYLES,
          paddingLeft: '3.2rem',
        },
        label: FLOATING_INPUT_LABEL_STYLES,

        '.chakra-input__left-element': {
          color: 'iris.500',
          fontSize: 'lg',
          transition: 'opacity 0.2s ease',
          opacity: 0,
          userSelect: 'none',
        },

        '.has__value .chakra-input__left-element': {
          opacity: 1,
        },

        _focusWithin: {
          label: {
            transform: 'translateY(-115%)',
            color: 'gray.300',
            fontSize: 'sm',
          },
          '.chakra-input__left-element': {
            opacity: 1,
          },
        },
      },
    },
    floating: {
      container: {
        input: FLOATING_INPUT_STYLES,

        textarea: {
          marginTop: '8px',
          paddingTop: '0',
          paddingBottom: '6px',
          ...FLOATING_INPUT_STYLES,
        },

        label: FLOATING_INPUT_LABEL_STYLES,

        _focusWithin: {
          label: {
            transform: 'translateY(-115%)',
            color: 'gray.300',
            fontSize: 'sm',
          },
        },
      },
    },
  },
};
