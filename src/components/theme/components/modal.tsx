export const modalStyles = {
  baseStyle: {
    dialog: {
      position: 'absolute',
      bottom: { base: '0', md: 'unset' },
      bg: 'gradient.gradient2',
      px: { base: '16px', md: '24px' },
      py: { base: '22px', md: '20px' },
      borderRadius: { base: '20px 20px 0 0', md: '20px' },
      my: 0,
      maxH: '90vh',
    },
    body: {
      p: '0 4px',
    },
    header: {
      p: 0,
      color: 'fluentHealthText.100',
      lineHeight: '2',
      mb: '24px',
    },
    closeButton: {
      top: '18px',
      right: '18px',
      bgColor: 'iris.500',
      color: 'white',
      size: '12px',
      p: '8px',
      borderRadius: '50%',
      sx: {
        '& > svg': {
          width: '12px',
          height: '12px',
        },
      },
    },
  },
  variants: {
    default: {},
    sharing: {
      dialog: {
        bg: 'fluentHealthComplementary.Salmon1',
      },
    },
    periwinkle: {
      dialog: {
        bg: 'gradient.periwinkle',
      },
    },
  },
};
