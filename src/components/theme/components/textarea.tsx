import { defineStyle, defineStyleConfig } from '@chakra-ui/react';

import { colors } from '../colors';
import { hexOpacity } from '../utils';

const roundedTransparent = defineStyle({
  p: '20px',
  background: 'transparent',
  border: '1px solid',
  borderColor: 'iris.500',
  borderRadius: '28px',
  _placeholder: {
    color: hexOpacity(colors.periwinkle[700], 0.6),
  },
  _focus: {
    background: hexOpacity('#ffffff', 0.2),
  },
});

export const textareaTheme = defineStyleConfig({
  variants: { roundedTransparent },
});
