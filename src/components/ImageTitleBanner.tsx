import { But<PERSON>, Container, Flex, Heading, Image, Link, Text } from '@chakra-ui/react';
import { ReactNode } from 'react';

interface CTA {
  text: string;
  onClick?: () => void;
  href?: string;
  isExternal?: boolean;
}
interface ImageTitleBannerProps {
  title: string;
  description: ReactNode;
  image: string;
  cta?: CTA;
  reverse?: boolean;
  imageStyles?: Record<string, any>;
  containerStyles?: Record<string, any>;
  mainContainerStyles?: Record<string, any>;
}
export function ImageTitleBanner({
  title,
  description,
  image,
  cta,
  reverse,
  imageStyles = {},
  containerStyles = {},
  mainContainerStyles = {},
}: ImageTitleBannerProps) {
  return (
    <Flex
      position="relative"
      width="100%"
      direction={{ base: 'column', md: reverse ? 'row-reverse' : 'row' }}
      gap="32px"
      alignItems="center"
      {...mainContainerStyles}
    >
      <Image
        src={image}
        height={{ base: 'auto', md: '550px' }}
        zIndex="1"
        right="0"
        top="200px"
        transform={{ md: 'none' }}
        {...imageStyles}
      />
      <Container
        maxWidth="1240px"
        padding={{ base: '0 32px', md: '0' }}
        {...containerStyles}
      >
        <Flex
          alignItems="center"
          gap="100px"
          position="relative"
        >
          <Flex
            direction="column"
            gap="32px"
            maxWidth="475px"
            zIndex="1"
          >
            <Heading fontSize={{ base: '28px', md: '36px' }}>{title}</Heading>
            <Flex
              direction="column"
              gap="24px"
              alignItems="start"
            >
              <Text fontSize="lg">{description}</Text>

              {cta ? (
                <Link
                  href={cta.href || ''}
                  isExternal={cta.isExternal}
                >
                  <Button
                    zIndex="2"
                    height="50px"
                    onClick={() => cta?.onClick?.()}
                  >
                    {cta.text}
                  </Button>
                </Link>
              ) : null}
            </Flex>
          </Flex>
        </Flex>
      </Container>
    </Flex>
  );
}
