import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Flex, Menu, MenuButton, MenuItem, MenuList, useDisclosure } from '@chakra-ui/react';
import { AddDropdownProps } from '@src/types/types';

/**
 * AddDropdown component for selecting options from a dropdown menu.
 * @param {AddDropdownProps} props - The component props.
 * @returns {JSX.Element} The AddDropdown component.
 */
function AddDropdown({
  options,
  defaultValue,
  onSelect,
  onClear,
  setIsAdding,
  isAdding,
  showClearButton,
}: AddDropdownProps) {
  const [selectedValue, setSelectedValue] = useState(defaultValue);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    if (isAdding) onOpen();
  }, [isAdding, onOpen]);

  const handleSelect = (value: string) => {
    setSelectedValue(value);
    if (onSelect) onSelect(value);
    onClose();
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      if (setIsAdding) setIsAdding(false);
    }
  };

  const handleClear = (value: string) => {
    setSelectedValue(value);
    if (onClear) onClear(value);
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <Flex
      direction="column"
      alignItems="center"
      ref={menuRef}
    >
      <Box
        position="relative"
        width="100%"
      >
        <Menu
          isOpen={isOpen}
          onClose={onClose}
        >
          <MenuButton
            as={Button}
            borderBottom="1px solid #FFF"
            width="100%"
            textAlign="left"
            borderRadius="0"
            onClick={onOpen}
            color="white"
            fontSize={{ base: 'md', md: 'xl' }}
            lineHeight="1"
            height="27.5px"
            paddingRight="4"
            paddingLeft="0"
            fontWeight="400"
            fontFamily="var(--chakra-fonts-heading)"
          >
            {selectedValue}
          </MenuButton>
          <MenuList
            maxHeight="200px"
            overflowY="auto"
            w="22rem"
          >
            {options?.map((option) => (
              <MenuItem
                key={option}
                onClick={() => handleSelect(option)}
              >
                {option}
              </MenuItem>
            ))}
          </MenuList>
        </Menu>
        {selectedValue && showClearButton && (
          <Button
            size="sm"
            position="absolute"
            right="0.5rem"
            top="50%"
            transform="translateY(-50%)"
            onClick={() => handleClear(selectedValue)}
            backgroundColor="transparent"
            _hover={{ backgroundColor: 'transparent' }}
            padding="0"
            fontFamily="Apercu"
            lineHeight="18px"
            fontWeight="400"
          >
            Clear
          </Button>
        )}
      </Box>
    </Flex>
  );
}

export default AddDropdown;
