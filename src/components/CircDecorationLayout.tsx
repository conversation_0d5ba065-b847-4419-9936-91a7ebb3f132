import { Box, Flex, useTheme } from '@chakra-ui/react';

import { hexOpacity } from './theme/utils';
import { CircleWaveDecoration } from './ui/Decorations/CircleWaveDecoration';
import { BigCircleDecoration } from './ui/Decorations';

import { ReactComponent as FluentHealthLogo } from '@assets/icons/fh-logo.svg';

function CircDecorationLayout({
  showWave = true,
  showLogo = true,
  children,
}: {
  showWave?: boolean;
  showLogo?: boolean;
  children: React.ReactElement;
}) {
  const theme = useTheme();

  return (
    <Flex
      background="linear-gradient(180.96deg, #FFEFD8 -0.34%, #CED1FF 99.68%)"
      minH="100vh"
      direction="column"
      alignItems="center"
      px="2"
    >
      {showWave && (
        <>
          <CircleWaveDecoration
            top="-450px"
            left="-450px"
            width="966px"
            color={hexOpacity(theme.colors.fluentHealth[500], 0.2)}
          />
          <BigCircleDecoration
            top="450px"
            zIndex={0}
          />
        </>
      )}

      <Box mt={['32px', '30px']}>
        {showLogo && (
          <FluentHealthLogo
            width="112px"
            height="34px"
          />
        )}
      </Box>
      {children}
    </Flex>
  );
}

export default CircDecorationLayout;
