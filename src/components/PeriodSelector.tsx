// Package modules
import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  ChakraProps,
  Flex,
  IconButton,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Skeleton,
  Text,
  useDisclosure,
  useOutsideClick,
} from '@chakra-ui/react';
import { X as CloseIcon } from 'react-feather';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// Local modules
import { DatePicker, DatePickerContainer, DateRange } from './ui/DatePicker';
import { BREAKPOINTS } from '@lib/constants';

// Assets
import { ReactComponent as CalendarIcon } from '@assets/icons/calendar.svg';

dayjs.extend(customParseFormat);

// Constants
const INPUT_DATE_FORMAT = 'DD-MM-YYYY';

enum DATE_PICKER_INPUT_TYPES {
  DAY = 'day',
  MONTH = 'month',
  YEAR = 'year',
}

export function PeriodSelectorSkeleton() {
  return (
    <Skeleton
      width="128px"
      height="36px"
      borderRadius="full"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.500"
    />
  );
}

interface IDatePickerInput extends ChakraProps {
  value: string | null | undefined;
  placeholder?: string | null;
  type: DATE_PICKER_INPUT_TYPES;
  onChange?: (value: string | null) => any;
}
function DatePickerInput({ value, placeholder, type, onChange, ...props }: IDatePickerInput) {
  const inputRef = useRef<HTMLInputElement | null>(null);

  const isDayOrMonth = type === DATE_PICKER_INPUT_TYPES.DAY || type === DATE_PICKER_INPUT_TYPES.MONTH;

  const handleInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;

    const numberValidation = /^\d+$/;

    if (!numberValidation.test(inputValue) && inputValue.length > 0) {
      return;
    }

    if (isDayOrMonth && inputValue.length > 2) {
      return;
    }

    if (type === DATE_PICKER_INPUT_TYPES.YEAR && inputValue.length > 4) {
      return;
    }

    if (type === DATE_PICKER_INPUT_TYPES.MONTH && Number(inputValue) > 12) {
      return;
    }

    if (onChange) {
      onChange(inputValue);
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    if (isDayOrMonth && event.target.value === '0' && inputRef.current) {
      inputRef.current.value = '01';
      handleInput(event);
      return;
    }

    if (
      !event.target.value.startsWith('0') &&
      event.target.value.length > 0 &&
      event.target.value.length < 2 &&
      inputRef.current
    ) {
      inputRef.current.value = event.target.value.padStart(2, '0');
      handleInput(event);
    }
  };

  const showPlaceholder = (!value || value?.length === 0) && placeholder;

  return (
    <Input
      value={value || ''}
      placeholder={placeholder || ''}
      ref={inputRef}
      py="3px"
      px="2px"
      textAlign="center"
      width="30px"
      height="30px"
      border="none"
      borderRadius="6px"
      color={showPlaceholder ? 'fluentHealthText.400' : 'fluentHealthText.100'}
      onInput={handleInput}
      onBlur={handleBlur}
      _hover={{
        bgColor: 'fluentHealthSecondary.500',
      }}
      _focus={{
        bgColor: 'fluentHealthSecondary.500',
      }}
      _focusWithin={{
        boxShadow: 'none',
      }}
      {...props}
    />
  );
}

export function PeriodInputs({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
}: {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange?: (date: Date) => any;
  onEndDateChange?: (date: Date) => any;
}) {
  const startDay = startDate && dayjs(startDate).format('DD');
  const startMonth = startDate && dayjs(startDate).format('MM');
  const startYear = startDate && dayjs(startDate).format('YYYY');
  const endDay = endDate && dayjs(endDate).format('DD');
  const endMonth = endDate && dayjs(endDate).format('MM');
  const endYear = endDate && dayjs(endDate).format('YYYY');

  const [inputStartDay, setInputStartDay] = useState<string | null>(startDay);
  const [inputStartMonth, setInputStartMonth] = useState<string | null>(startMonth);
  const [inputStartYear, setInputStartYear] = useState<string | null>(startYear);
  const [inputEndDay, setInputEndDay] = useState<string | null>(endDay);
  const [inputEndMonth, setInputEndMonth] = useState<string | null>(endMonth);
  const [inputEndYear, setInputEndYear] = useState<string | null>(endYear);

  useEffect(() => {
    const startInputDate = dayjs(`${inputStartDay}-${inputStartMonth}-${inputStartYear}`, INPUT_DATE_FORMAT);
    if (startInputDate.isValid() && onStartDateChange) {
      onStartDateChange(startInputDate.toDate());
    }
  }, [inputStartDay, inputStartMonth, inputStartYear]);

  useEffect(() => {
    const endInputDate = dayjs(`${inputEndDay}-${inputEndMonth}-${inputEndYear}`, INPUT_DATE_FORMAT);
    if (endInputDate.isValid() && onEndDateChange) {
      onEndDateChange(endInputDate.toDate());
    }
  }, [inputEndDay, inputEndMonth, inputEndYear]);

  useEffect(() => {
    setInputStartDay(startDay);
    setInputStartMonth(startMonth);
    setInputStartYear(startYear);
  }, [startDay, startMonth, startYear]);

  useEffect(() => {
    setInputEndDay(endDay);
    setInputEndMonth(endMonth);
    setInputEndYear(endYear);
  }, [endDay, endMonth, endYear]);

  return (
    <Flex
      align="center"
      gap="2px"
      mb="16px"
      sx={{ [`@media ${BREAKPOINTS.TABLET}`]: { gap: '4px' } }}
    >
      <Flex
        py="0.5"
        width="full"
        border="1px solid"
        borderColor="fluentHealthSecondary.300"
        borderRadius="6px"
        justify="center"
        align="center"
        gap="2px"
        cursor="pointer"
        sx={{
          [`@media ${BREAKPOINTS.TABLET}`]: {
            gap: '8px',
            py: '0.25rem',
            borderRadius: 0,
          },
        }}
      >
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.DAY}
          placeholder="DD"
          value={inputStartDay}
          onChange={setInputStartDay}
        />
        <Text as="span">/</Text>
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.MONTH}
          placeholder="MM"
          value={inputStartMonth}
          onChange={setInputStartMonth}
        />
        <Text as="span">/</Text>
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.YEAR}
          width="48px"
          placeholder="YYYY"
          value={inputStartYear}
          onChange={setInputStartYear}
        />
      </Flex>
      <Text
        as="span"
        lineHeight="0"
      >
        -
      </Text>
      <Flex
        py="0.5"
        width="full"
        border="1px solid"
        borderColor="fluentHealthSecondary.300"
        borderRadius="6px"
        justify="center"
        align="center"
        gap="2px"
        cursor="pointer"
        sx={{
          [`@media ${BREAKPOINTS.TABLET}`]: {
            gap: '8px',
            py: '0.25rem',
            borderRadius: 0,
          },
        }}
      >
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.DAY}
          placeholder="DD"
          value={inputEndDay}
          onChange={setInputEndDay}
        />
        <Text as="span">/</Text>
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.MONTH}
          placeholder="MM"
          value={inputEndMonth}
          onChange={setInputEndMonth}
        />
        <Text as="span">/</Text>
        <DatePickerInput
          type={DATE_PICKER_INPUT_TYPES.YEAR}
          width="48px"
          placeholder="YYYY"
          value={inputEndYear}
          onChange={setInputEndYear}
        />
      </Flex>
    </Flex>
  );
}

export function PeriodButtons({ onChange }: { onChange: (range: DateRange) => any }) {
  const selectLastMonth = () => {
    const currentMonthNumber = dayjs().get('month');
    const lastMonth = dayjs()
      .month(currentMonthNumber - 1)
      .toDate();
    const startDate = dayjs(lastMonth).startOf('month').toDate();
    const endDate = dayjs(lastMonth).endOf('month').toDate();
    onChange([startDate, endDate]);
  };

  const selectLast6Months = () => {
    const currentMonthNumber = dayjs().get('month');
    const lastMonth = dayjs()
      .month(currentMonthNumber - 1)
      .toDate();
    const last6Months = dayjs()
      .month(currentMonthNumber - 7)
      .toDate();
    const startDate = dayjs(last6Months).startOf('month').toDate();
    const endDate = dayjs(lastMonth).endOf('month').toDate();
    onChange([startDate, endDate]);
  };

  return (
    <Flex
      gap="8px"
      mb="8px"
      sx={{
        [`@media ${BREAKPOINTS.TABLET}`]: {
          gap: '4px',
          mb: '16px',
        },
      }}
    >
      <Button
        bgColor="fluentHealthSecondary.500"
        color="fluentHealthSecondary.100"
        py="10px"
        fontSize="14px"
        borderRadius="8px"
        width="full"
        _hover={{
          bgColor: 'fluentHealthSecondary.300',
        }}
        onClick={selectLastMonth}
        sx={{ [`@media ${BREAKPOINTS.TABLET}`]: { py: '6px' } }}
      >
        Last month
      </Button>
      <Button
        bgColor="fluentHealthSecondary.500"
        color="fluentHealthSecondary.100"
        py="10px"
        fontSize="14px"
        borderRadius="8px"
        width="full"
        _hover={{
          bgColor: 'fluentHealthSecondary.300',
        }}
        onClick={selectLast6Months}
        sx={{ [`@media ${BREAKPOINTS.TABLET}`]: { py: '6px' } }}
      >
        Last 6 months
      </Button>
    </Flex>
  );
}

interface IPeriodSelector {
  initialDate: DateRange;
  onChange?: (fromDate: Date | null, toDate: Date | null) => void;
  strategy?: 'absolute' | 'fixed';
  fullWidth?: boolean;
  clearDateRange?: Function;
}
export function PeriodSelector({
  onChange,
  initialDate,
  strategy = 'absolute',
  fullWidth,
  clearDateRange,
}: IPeriodSelector) {
  const [dateRange, setRangeDate] = useState<DateRange>(initialDate);

  const [startDate, endDate] = dateRange;

  const datePickerPopover = useDisclosure();

  const periodFilterValueText = dateRange
    .map((date) => {
      if (dayjs(date).isValid()) {
        return dayjs(date).format('D MMM, YYYY');
      }
      return null;
    })
    .filter((date) => date !== null)
    .join(' - ');

  const showPeriodFilterValueText = !!periodFilterValueText;

  const datePickerContainerRef = useRef<HTMLDivElement | null>(null);
  useOutsideClick({
    ref: datePickerContainerRef,
    handler: () => {
      datePickerPopover.onClose();
    },
  });

  const updateSearchParams = async (fromDate: Date | null = null, toDate: Date | null = null) => {
    if (onChange) {
      onChange(fromDate, toDate);
    }
  };

  const onDateChange = async (date: DateRange | Date | null) => {
    let resultDate: DateRange | Date | null = null;

    if (date instanceof Date) {
      resultDate = [date, date];
    } else {
      resultDate = date === null ? [null, null] : date;
    }
    setRangeDate(resultDate);
    await updateSearchParams(resultDate[0], resultDate[1]);
  };

  const onStartDateInputChange = async (date: Date) => {
    setRangeDate([date, dateRange[1]]);
    await updateSearchParams(date, dateRange[1]);
  };

  const onEndDateInputChange = async (date: Date) => {
    let resultDate: [Date | null, Date | null] = [dateRange[0], date];

    // If the end date is less than the start date, then reset the start date.
    if (startDate && startDate.valueOf() > date.valueOf()) {
      resultDate = [null, date];
    }

    setRangeDate(resultDate);
    await updateSearchParams(resultDate[0], resultDate[1]);
  };

  const onPeriodButtonChange = async (dates: DateRange) => {
    setRangeDate(dates);
    await updateSearchParams(dates[0], dates[1]);
    datePickerPopover.onClose();
  };

  const clearDateHandler = async (event: React.MouseEvent<SVGElement | HTMLButtonElement>) => {
    event.preventDefault();
    setRangeDate([null, null]);
    await updateSearchParams(null, null);
    datePickerPopover.onClose();
    if (clearDateRange) {
      clearDateRange();
    }
  };

  return (
    <Box ref={datePickerContainerRef}>
      <Popover
        closeOnBlur={false}
        isOpen={datePickerPopover.isOpen}
        onOpen={datePickerPopover.onOpen}
        onClose={datePickerPopover.onClose}
        lazyBehavior="unmount"
        strategy={strategy}
        isLazy
      >
        <PopoverTrigger>
          <Button
            width={fullWidth ? 'full' : 'auto'}
            py="2px"
            px="4"
            pr={showPeriodFilterValueText ? '0.5' : '1.5'}
            minH="36px"
            variant="outline"
            color={showPeriodFilterValueText ? 'royalBlue.500' : 'charcoal.100'}
            borderColor={showPeriodFilterValueText ? 'fluentHealth.500' : 'iris.100'}
            boxShadow={showPeriodFilterValueText ? '0 0 0 2px #DADCFF' : 'none'}
            iconSpacing="1"
            rightIcon={
              <IconButton
                as="span"
                aria-label={showPeriodFilterValueText ? 'Clear filtering by date' : 'Filter by date'}
                size="18px"
                bgColor="transparent"
                color={showPeriodFilterValueText ? 'fluentHealthText.250' : 'charcoal.60'}
                p="1.5"
                borderRadius="50%"
                flexShrink="0"
                _hover={{
                  bgColor: showPeriodFilterValueText ? 'fluentHealthSecondary.300' : 'transparent',
                }}
                icon={
                  showPeriodFilterValueText ? (
                    <CloseIcon
                      width="18px"
                      height="18px"
                      onClick={clearDateHandler}
                    />
                  ) : (
                    <CalendarIcon
                      width="18px"
                      height="18px"
                    />
                  )
                }
              />
            }
            _hover={{ borderColor: 'fluentHealth.500' }}
          >
            {showPeriodFilterValueText ? periodFilterValueText : 'Date Range'}
          </Button>
        </PopoverTrigger>
        <DatePickerContainer as={PopoverContent}>
          <PeriodButtons onChange={onPeriodButtonChange} />
          <PeriodInputs
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={onStartDateInputChange}
            onEndDateChange={onEndDateInputChange}
          />
          <DatePicker
            monthsShown={2}
            selected={startDate}
            startDate={startDate}
            endDate={endDate}
            onChange={onDateChange}
            // @ts-ignore
            selectsRange
            inline
            isClearable
          />
          <Button
            variant="ghost"
            alignSelf="self-start"
            px="0"
            py="0"
            mt="32px"
            mb="8px"
            color="iris.500"
            isDisabled={!startDate && !endDate}
            onClick={clearDateHandler}
            _disabled={{
              color: 'fluentHealthText.400',
            }}
          >
            Clear selection
          </Button>
        </DatePickerContainer>
      </Popover>
    </Box>
  );
}
