import React, { ComponentType, Suspense } from 'react';
import { Box, Flex } from '@chakra-ui/react';

import { FluentHealthLogoAnimation } from './FluentHealthLogoAnimation';

interface SuspenseLoaderProps {
  component: () => Promise<{ default: ComponentType<any> }>;
  fallbackProps?: {
    minHeight?: string | number;
    width?: string;
    bgColor?: string;
    borderRadius?: string;
    boxShadow?: string;
    p?: string | number;
    showTabsSkeleton?: boolean;
    tabCount?: number;
  };
  componentProps?: any;
}

export function SuspenseLoader({ component, fallbackProps = {}, componentProps = {} }: SuspenseLoaderProps) {
  const LazyComponent = React.lazy(component);

  const {
    minHeight = '200px',
    width = '100%',
    bgColor,
    borderRadius,
    boxShadow,
    p,
    showTabsSkeleton = false,
    tabCount = 2,
  } = fallbackProps;

  const fallbackContent = (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight={minHeight}
      width={width}
      bgColor={bgColor}
      borderRadius={borderRadius}
      boxShadow={boxShadow}
      p={p}
    >
      {showTabsSkeleton ? (
        <Flex
          direction="column"
          w="100%"
        >
          <Flex
            px="10px"
            gap="8px"
            mb="15px"
          >
            {Array.from({ length: tabCount }, (_, i) => (
              <Box
                key={i}
                h="40px"
                w="120px"
                bg="gray.100"
                borderRadius="md"
              />
            ))}
          </Flex>
          <Flex
            flex="1"
            justifyContent="center"
            alignItems="center"
          >
            <FluentHealthLogoAnimation />
          </Flex>
        </Flex>
      ) : (
        <FluentHealthLogoAnimation />
      )}
    </Box>
  );

  return (
    <Suspense fallback={fallbackContent}>
      <LazyComponent {...componentProps} />
    </Suspense>
  );
}
