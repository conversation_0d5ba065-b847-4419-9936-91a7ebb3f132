import { Flex, Text } from '@chakra-ui/react';

import { ApiUtils } from '../../utils/api.utils';

export default function AppError({ error }: { error: any }) {
  if (!error) return null;

  return (
    <Flex
      borderRadius="md"
      bgColor="fluentHealthComplementary.Red"
      p="2"
    >
      <Text
        fontSize="md"
        color="white"
      >
        {ApiUtils.convertApiErrorToString(error)}
      </Text>
    </Flex>
  );
}
