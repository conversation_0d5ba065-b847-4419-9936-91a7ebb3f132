import { createSearchParams, useNavigate, useSearchParams } from 'react-router-dom';

export const useNavigateSearch = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  return ({
    path = '',
    params = {},
    paramsHandling = 'replace',
  }: {
    path?: string;
    params?: Record<string, string | null>;
    paramsHandling?: 'merge' | 'preserve' | 'replace';
  }) => {
    const sp: Record<string, string> = {};

    if (paramsHandling === 'merge') {
      searchParams.forEach((value, key) => (sp[key] = value));
    }

    Object.entries(params).forEach(([key, value]) => {
      if (value == null) {
        delete sp[key];
      } else {
        sp[key] = value;
      }
    });

    if (paramsHandling === 'preserve') {
      searchParams.forEach((value, key) => (sp[key] = value));
    }

    return navigate({
      pathname: path,
      search: params ? `?${createSearchParams(sp)}` : undefined,
    });
  };
};
