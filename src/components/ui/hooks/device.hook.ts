import { useMediaQuery, useWindowSize } from 'usehooks-ts';

export const useIsMobile = () => {
  const matches = useMediaQuery('(max-width: 767px)');
  return matches;
};

export const useIsTablet = () => {
  const matches = useMediaQuery('(max-width: 62em)');
  return matches;
};

export const useIsTabletReactive = () => {
  const { width } = useWindowSize()
  return width < 992
};

export const useIsDesktop = () => {
  const matches = useMediaQuery('(min-width: 1024px)');
  return matches;
};

export const useDevice = () => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();

  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  return 'desktop';
};
