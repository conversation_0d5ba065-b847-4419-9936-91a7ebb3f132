import { Table } from '@tanstack/react-table';
import { cva, VariantProps } from 'class-variance-authority';
import clsx from 'clsx';

import { useNavigateSearch } from '../hooks/navigate-search.hook';
import { usePaginationButtons } from './pagination-buttons.hook';
import { DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE } from '../Table/api-table.hook';
import { Select } from '../Select';

const styles = cva(
  'flex justify-between gap-3 md:gap-2 py-6 px-4 border-t border-t-gray-200 flex-col-reverse md:flex-row',
  {
    variants: {},
    defaultVariants: {},
  }
);

export type TappPaginatorProps = { table: Table<any>; total?: number } & VariantProps<typeof styles> &
  JSX.IntrinsicElements['div'];

const pageSizeOptions = [
  { label: '10', value: 10 },
  { label: '20', value: 20 },
  { label: '50', value: 50 },
  { label: '100', value: 100 },
];

export const TappPaginator: React.FC<TappPaginatorProps> = ({ table, total = 0, className, ...props }) => {
  const navigate = useNavigateSearch();
  const { pageSize, pageIndex } = table.getState().pagination;
  const pageCount = table.getPageCount();

  const paginationButtons = usePaginationButtons({ pageIndex, pageCount });

  const setPageIndex = (index: number) => {
    navigate({
      params: {
        pageIndex: index === DEFAULT_PAGE_INDEX ? null : index.toString(),
      },
      paramsHandling: 'merge',
    });
  };

  const setPageSize = (size: number) => {
    navigate({
      params: {
        pageIndex: null,
        pageSize: size === DEFAULT_PAGE_SIZE ? null : size.toString(),
      },
      paramsHandling: 'merge',
    });
  };

  const onPreviousPage = () => {
    setPageIndex(pageIndex - 1);
  };

  const onNextPage = () => {
    setPageIndex(pageIndex + 1);
  };

  return (
    <div className={clsx(styles({}), className)}>
      <div className="flex items-center justify-center gap-3 md:justify-start md:gap-4">
        <span className="text-xs font-medium text-gray-400">
          Showing {Math.min(pageSize, total)} of {total} items
        </span>
      </div>

      <div className="flex flex-col-reverse items-center justify-center gap-3 md:flex-row md:gap-6">
        <div className="flex items-center">
          <span className="mr-3 text-xs font-medium text-gray-400">Items per page: </span>
          <Select
            isClearable={false}
            menuPlacement="auto"
            options={pageSizeOptions}
            value={pageSizeOptions.find((c) => c.value === pageSize)}
            onChange={(e: any) => e && setPageSize(e.value)}
          />
        </div>

        {pageSize < total && (
          <div className="flex h-10 items-center justify-center space-x-[-1px] md:justify-end">
            <ActionButton
              className="rounded-tl rounded-bl md:pl-2 md:pr-3"
              onClick={() => onPreviousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              chevron_left
              <span className="hidden md:inline">Previous</span>
            </ActionButton>

            {paginationButtons.map((page, i) =>
              page === null ? (
                <PageButton
                  key={i}
                  disabled
                >
                  ...
                </PageButton>
              ) : (
                <PageButton
                  key={i}
                  onClick={() => setPageIndex(page - 1)}
                  active={pageIndex === page - 1}
                >
                  {page}
                </PageButton>
              )
            )}

            <ActionButton
              className="rounded-tr rounded-br md:pl-3 md:pr-2"
              onClick={() => onNextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="hidden md:inline">Next</span>
              chevron_right
            </ActionButton>
          </div>
        )}
      </div>
    </div>
  );
};

function PageButton({ active = false, className, ...props }: { active?: boolean } & JSX.IntrinsicElements['button']) {
  return (
    <button
      className={clsx(
        'flex h-full min-w-[40px] items-center justify-center border border-gray-200 text-xs font-medium hover:bg-gray-50 hover:transition disabled:pointer-events-none disabled:text-gray-300',
        { 'z-10 border-gray-800': active },
        className
      )}
      {...props}
    />
  );
}

function ActionButton({ className, ...props }: JSX.IntrinsicElements['button']) {
  return (
    <button
      className={clsx(
        'flex h-full w-10 items-center justify-center gap-1 border border-gray-200 text-xs font-medium transition hover:bg-gray-50 disabled:pointer-events-none disabled:text-gray-300 md:w-auto',
        className
      )}
      {...props}
    />
  );
}
