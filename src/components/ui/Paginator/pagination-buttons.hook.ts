import { useMemo } from 'react';

export const getPaginationButtons = ({ pageIndex, pageCount }: { pageIndex: number; pageCount: number }) => {
  const current = pageIndex + 1;

  if (pageCount <= 5) {
    return new Array(pageCount).fill(0).map((_, i) => i + 1);
  }

  if (current === 1 || current === 2) {
    return [1, 2, 3, null, pageCount];
  }

  if (current === pageCount || current === pageCount - 1) {
    return [1, null, pageCount - 2, pageCount - 1, pageCount];
  }

  const items = new Set<number>();
  items.add(1);
  items.add(current - 1);
  items.add(current);
  items.add(current + 1);
  items.add(pageCount);

  const arr = Array.from<number | null>(items);
  const firstElem = arr.at(0)!;
  const secondElem = arr.at(1)!;
  const lastElem = arr.at(-1)!;
  const secondToLastElem = arr.at(-2)!;

  if (secondElem === firstElem + 2) {
    arr.splice(1, 0, firstElem + 1);
  } else if (secondElem > firstElem + 2) {
    arr.splice(1, 0, null);
  }

  if (secondToLastElem === lastElem - 2) {
    arr.splice(-1, 0, lastElem - 1);
  } else if (secondToLastElem < lastElem - 2) {
    arr.splice(-1, 0, null);
  }

  return arr;
};

export const usePaginationButtons = ({ pageIndex, pageCount }: { pageIndex: number; pageCount: number }) => {
  const range = useMemo(() => {
    return getPaginationButtons({ pageIndex, pageCount });
  }, [pageIndex, pageCount]);

  return range;
};
