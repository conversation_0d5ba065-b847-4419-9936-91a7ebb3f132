// Package modules
import React, { MouseEvent, PropsWithChildren, ReactNode, memo, useCallback, useMemo, useRef } from 'react';
import BaseDatePicker, {
  CalendarContainerProps,
  ReactDatePickerCustomHeaderProps,
  ReactDatePickerProps,
} from 'react-datepicker';
import {
  Box,
  Button,
  ChakraProps,
  Flex,
  IconButton,
  NumberDecrementStepper,
  NumberIncrementStepper,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  Popover,
  PopoverContent,
  PopoverProps,
  PopoverTrigger,
  Text,
  UseDisclosureReturn,
  chakra,
  useDisclosure,
  useOutsideClick,
  useTheme,
} from '@chakra-ui/react';
import {
  ChevronDown as ArrowDown,
  ChevronLeft as ArrowLeft,
  ChevronRight as ArrowRight,
  ChevronUp as ArrowUp,
  ArrowUpLeft as ArrowUpLeftIcon,
} from 'react-feather';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';

// Local modules
import { generateArrayOfYears } from '../../../app/medical-records/lib/utils';

// Assets
import 'react-datepicker/dist/react-datepicker.css';

dayjs.extend(localeData);

// Styled components
// eslint-disable-next-line @typescript-eslint/naming-convention
const StyledCalendarContainer = chakra(Flex, {
  shouldForwardProp: (prop) => !['showPopperArrow', 'arrowProps'].includes(prop),
});

// Types
export type DateRange = [Date | null, Date | null];

// Helper functions
const getContainerStyles = (theme: any) => ({
  '& .react-datepicker__month-container': {
    width: '100%',
  },
  '& .react-datepicker__month': {
    margin: '0',
  },
  '& .react-datepicker__day-name': {
    color: theme.colors.fluentHealthText[400],
    fontSize: '14px',
    margin: '4.5px',
  },
  '& .react-datepicker__day': {
    position: 'relative',
    display: 'inline-flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '0',
    fontSize: '16px',
    // width: 'max-content',
    width: '36px',
    height: 'max-content',
    margin: '4px 0',
    '&:hover': {
      transition: 'background-color 0.2s ease-in',
      '& > div:before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        borderRadius: '50%',
      },
    },
    '&:not(.react-datepicker__day--disabled)': {
      color: theme.colors.fluentHealthText[100],
      '&:hover': {
        backgroundColor: theme.colors.fluentHealthSecondary[500],
      },
    },
    [[
      '&.react-datepicker__day--range-start',
      '&.react-datepicker__day--in-range',
      '&.react-datepicker__day--in-selecting-range',
      '&.react-datepicker__day--selected',
      // '&.react-datepicker__day--keyboard-selected',
    ].join(', ')]: {
      backgroundColor: theme.colors.fluentHealthSecondary[500],
    },
    '&.react-datepicker__day:not(.react-datepicker__day--in-selecting-range):not(.react-datepicker__day--disabled):not(.react-datepicker__day--selected)':
      {
        '&:hover': {
          backgroundColor: theme.colors.fluentHealthSecondary[500],
          color: theme.colors.fluentHealthText[100],

          '& > div:before': {
            content: '""',
            border: `2px solid ${theme.colors.fluentHealthSecondary[400]}`,
          },
        },
      },
    '&.react-datepicker__day--keyboard-selected:not(.react-datepicker__day--in-range)': {
      backgroundColor: 'transparent',
    },
    '&.react-datepicker__day--range-start': {
      borderTopLeftRadius: '50%',
      borderBottomLeftRadius: '50%',
    },
    '&.react-datepicker__day--range-end': {
      borderTopRightRadius: '50%',
      borderBottomRightRadius: '50%',
    },
    '&.react-datepicker__day--selecting-range-start': {
      borderTopLeftRadius: '50%',
      borderBottomLeftRadius: '50%',
    },
    '&.react-datepicker__day--selecting-range-end': {
      borderTopRightRadius: '50%',
      borderBottomRightRadius: '50%',
    },
    '&.react-datepicker__day:not(.react-datepicker__day--in-selecting-range):not(.react-datepicker__day--in-range)': {
      borderRadius: '50%',
    },
    '&.react-datepicker__day--selected:not(.react-datepicker__day--in-selecting-range):not(.react-datepicker__day--in-range)':
      {
        borderRadius: '50%',
      },
    [[
      '&.react-datepicker__day--range-start',
      '&.react-datepicker__day--selected',
      '&.react-datepicker__day--range-end',
    ].join(', ')]: {
      color: theme.colors.fluentHealthText[250],
      '& > div:before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        border: `2px solid ${theme.colors.fluentHealthText[250]}`,
        borderRadius: '50%',
      },
    },
    '&.react-datepicker__day--outside-month': {
      color: theme.colors.fluentHealthText[400],
      // '&:before': {
      //   content: 'none'
      // }
    },
    '&.react-datepicker__day--outside-month:not(.react-datepicker__day--in-range)': {
      '& > div:before': {
        content: 'none',
      },
    },
  },
  '& .react-datepicker__header': {
    backgroundColor: 'white',
    padding: '0',
    borderBottom: 'none',
    marginBottom: '8px',
  },
  '& .chakra-text': {
    color: theme.colors.fluentHealthText[100],
  },
});

// Components
export function DatePickerContainer({ ...props }) {
  return (
    <Box
      width="auto"
      px="16px"
      py="16px"
      borderRadius="8px"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(7, 16, 84, 0.1), 0px 10px 28px -2px rgba(7, 16, 84, 0.14)"
      {...props}
    />
  );
}

function DatePickerYearInput({ ...props }) {
  return (
    <NumberInput
      _hover={{
        '& > input': {
          bgColor: 'fluentHealthSecondary.500',
        },
        '& > div': {
          visibility: 'visible',
        },
      }}
      sx={{
        '& > div': {
          visibility: 'hidden',
        },
      }}
      {...props}
    >
      <NumberInputField
        pt="2px"
        px="4px"
        pl="6px"
        width="62px"
        height="auto"
        border="none"
        fontWeight="500"
        _focus={{
          borderBottomLeftRadius: '0',
          borderBottomRightRadius: '0',
          borderBottom: '1px solid',
          borderColor: 'fluentHealthText.100',
          outline: 'none',
          bgColor: 'fluentHealthSecondary.500',
        }}
        _focusWithin={{
          boxShadow: 'none',
          '& + div': {
            visibility: 'visible',
          },
        }}
        _invalid={{
          boxShadow: 'none',
        }}
      />
      <NumberInputStepper width="20px">
        <NumberIncrementStepper border="none">
          <ArrowUp
            size={10}
            strokeWidth="4px"
          />
        </NumberIncrementStepper>
        <NumberDecrementStepper border="none">
          <ArrowDown
            size={10}
            strokeWidth="4px"
          />
        </NumberDecrementStepper>
      </NumberInputStepper>
    </NumberInput>
  );
}

interface IDatePickerHeader extends ReactDatePickerCustomHeaderProps {
  monthsShown?: Pick<ReactDatePickerProps, 'monthsShown'>;
}
function DatePickerHeader(props: IDatePickerHeader) {
  const monthsPopover = useDisclosure();

  const monthsPopoverContainerRef = useRef<HTMLDivElement | null>(null);
  useOutsideClick({
    ref: monthsPopoverContainerRef,
    handler: () => {
      monthsPopover.onClose();
    },
  });

  const {
    date,
    monthsShown,
    monthDate,
    customHeaderCount,
    decreaseMonth,
    increaseMonth,
    changeYear,
    changeMonth,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
  } = props;

  const [currentMonth]: string[] = monthDate
    .toLocaleString('en-US', {
      month: 'long',
      year: 'numeric',
    })
    .split(' ');

  const monthList: string[] = useMemo(dayjs.months, []);
  const yearList: string[] = useMemo(generateArrayOfYears, []);

  const showLeftArrow = customHeaderCount === 0;
  const showRightArrow = customHeaderCount === monthsShown;

  const handleMonthChange = (month: string) => {
    // If there is more than 1 column, then after changing the month, we need to update the corresponding column.
    changeMonth(monthList.indexOf(month) - customHeaderCount);
    monthsPopover.onToggle();
  };

  const handleYearChange = (value: string) => {
    if (value.length > 4) {
      return;
    }
    changeYear(Number(value));
  };

  return (
    <Flex>
      <Flex
        align="center"
        justify="space-between"
        width="full"
      >
        <IconButton
          aria-label="Previous Month"
          width="auto"
          minW="unset"
          top="unset"
          left="unset"
          px="4px"
          py="4px"
          bgColor="transparent"
          color="fluentHealthText.300"
          textIndent="0"
          icon={<ArrowLeft size={18} />}
          className="react-datepicker__navigation react-datepicker__navigation--previous"
          disabled={prevMonthButtonDisabled}
          visibility={showLeftArrow ? 'visible' : 'hidden'}
          onClick={decreaseMonth}
          _hover={{
            color: 'fluentHealthText.100',
            bgColor: 'fluentHealthSecondary.300',
          }}
        />
        <Flex
          ref={monthsPopoverContainerRef}
          align="center"
          pl="12px"
        >
          <Popover
            autoFocus={false}
            isOpen={monthsPopover.isOpen}
            onClose={monthsPopover.onClose}
            onOpen={monthsPopover.onOpen}
            placement="bottom-start"
          >
            <PopoverTrigger>
              <Button
                py="5px"
                px="5px"
                fontSize="16px"
                color="fluentHealthText.100"
                bgColor="transparent"
                borderRadius="6px"
                onClick={monthsPopover.onOpen}
                _hover={{
                  bgColor: 'fluentHealthSecondary.500',
                }}
              >
                {currentMonth}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              width="300px"
              maxHeight="260px"
              overflowY="auto"
              borderRadius="12px"
              borderColor="fluentHealthSecondary.300"
              p="4px"
              boxShadow="0px 1px 4px rgba(7, 16, 84, 0.1), 0px 10px 28px -2px rgba(7, 16, 84, 0.14)"
            >
              {monthList.map((month) => (
                <Flex
                  key={month}
                  justify="space-between"
                  align="center"
                  borderRadius="8px"
                  py="6px"
                  px="8px"
                  cursor="pointer"
                  bgColor={month === currentMonth ? 'fluentHealthSecondary.500' : 'transparent'}
                  _hover={{ bg: 'fluentHealthSecondary.500' }}
                  _active={{ bg: 'fluentHealthSecondary.500' }}
                  _focus={{ bg: 'fluentHealthSecondary.500' }}
                  onClick={() => handleMonthChange(month)}
                >
                  <Text
                    fontSize="16px"
                    maxW="200px"
                  >
                    {month}
                  </Text>
                  <IconButton
                    icon={<ArrowUpLeftIcon size={18} />}
                    aria-label="Icon Button"
                    bgColor="transparent"
                    color="fluentHealthText.300"
                    size="18px"
                    p="6px"
                    borderRadius="50%"
                    flexShrink="0"
                    _hover={{
                      color: 'fluentHealthText.100',
                      bgColor: 'fluentHealthSecondary.300',
                    }}
                  />
                </Flex>
              ))}
            </PopoverContent>
          </Popover>
          <DatePickerYearInput
            min={yearList[yearList.length - 1]}
            max={yearList[0]}
            value={dayjs(date).get('year')}
            onChange={handleYearChange}
          />
        </Flex>
        <IconButton
          width="auto"
          minW="unset"
          top="unset"
          right="unset"
          px="4px"
          py="4px"
          bgColor="transparent"
          color="fluentHealthText.300"
          textIndent="0"
          icon={<ArrowRight size={18} />}
          aria-label="Next Month"
          className="react-datepicker__navigation react-datepicker__navigation--next"
          disabled={nextMonthButtonDisabled}
          visibility={showRightArrow ? 'visible' : 'hidden'}
          onClick={increaseMonth}
          _hover={{
            color: 'fluentHealthText.100',
            bgColor: 'fluentHealthSecondary.300',
          }}
        />
      </Flex>
    </Flex>
  );
}

function DatePickerDay(day: number) {
  return (
    <Flex
      justify="center"
      align="center"
      width="36px"
      height="36px"
    >
      {day}
    </Flex>
  );
}

export function DatePickerPopover({
  popoverProps,
  datePickerPopover,
  popoverTriggerElement,
  onClickOutside,
  isDisabled = false,
  children,
}: PropsWithChildren<{
  popoverProps?: PopoverProps;
  datePickerPopover: UseDisclosureReturn;
  popoverTriggerElement: ReactNode;
  onClickOutside?: Function;
  isDisabled: boolean;
}>) {
  const datePickerContainerRef = useRef<HTMLDivElement | null>(null);
  useOutsideClick({
    ref: datePickerContainerRef,
    handler: () => {
      datePickerPopover.onClose();
      onClickOutside?.();
    },
  });

  return (
    <Box
      ref={datePickerContainerRef}
      width="full"
    >
      <Popover
        closeOnBlur={false}
        isOpen={datePickerPopover.isOpen}
        {...(!isDisabled ? { onOpen: datePickerPopover.onOpen } : {})}
        onClose={datePickerPopover.onClose}
        lazyBehavior="unmount"
        isLazy
        {...popoverProps}
      >
        <PopoverTrigger>{popoverTriggerElement}</PopoverTrigger>
        <DatePickerContainer as={PopoverContent}>{children}</DatePickerContainer>
      </Popover>
    </Box>
  );
}

export function DatePickerClearSelectionButton({
  isDisabled,
  onClick,
  ...props
}: PropsWithChildren<
  {
    onClick: (event: MouseEvent<HTMLButtonElement>) => void;
    isDisabled: boolean;
  } & ChakraProps
>) {
  return (
    <Button
      variant="ghost"
      alignSelf="self-start"
      px="0"
      py="0"
      color="iris.500"
      _disabled={{
        color: 'fluentHealthText.400',
      }}
      onClick={onClick}
      {...props}
    >
      Clear selection
    </Button>
  );
}

interface IDatePicker extends ReactDatePickerProps {
  CustomHeaderRenderer?: (props: any) => JSX.Element;
}
function DatePickerComponent({
  customInput = null,
  renderDayContents = DatePickerDay,
  CustomHeaderRenderer = DatePickerHeader,
  ...props
}: IDatePicker) {
  const theme = useTheme();

  const customHeaderRenderer = (headerProps: ReactDatePickerCustomHeaderProps) => {
    return (
      <CustomHeaderRenderer
        monthsShown={props.monthsShown ? props.monthsShown - 1 : props.monthsShown}
        {...headerProps}
      />
    );
  };

  const memoizedCalendarContainer = useCallback(
    (containerProps: CalendarContainerProps) => (
      <StyledCalendarContainer
        gap="24px"
        fontFamily="Apercu"
        border="none"
        borderRadius="0"
        sx={getContainerStyles(theme)}
        {...containerProps}
      />
    ),
    [theme]
  );

  return (
    <BaseDatePicker
      calendarContainer={memoizedCalendarContainer}
      customInput={customInput}
      renderDayContents={renderDayContents}
      renderCustomHeader={customHeaderRenderer}
      {...props}
    />
  );
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export const DatePicker = memo(DatePickerComponent);
