import clsx from 'clsx';
import React from 'react';

import { InferBaseHtmlType } from '../_helpers';

type BaseType = JSX.IntrinsicElements['div'];
export type TappInputErrorProps = BaseType;

const TappInputError = React.forwardRef<InferBaseHtmlType<BaseType>, TappInputErrorProps>(
  ({ style, className, ...props }, forwardedRef) => {
    return (
      <div
        ref={forwardedRef}
        className={clsx('text-xxs text-error first-letter:uppercase', className)}
        {...props}
      />
    );
  }
);

TappInputError.displayName = 'TappInputError';
export { TappInputError };
