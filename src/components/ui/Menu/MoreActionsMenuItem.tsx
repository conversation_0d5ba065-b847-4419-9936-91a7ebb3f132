import { PropsWithChildren } from 'react';
import { ChakraProps, MenuItem } from '@chakra-ui/react';

export function MoreActionsMenuItem({
  icon,
  children,
  ...props
}: ChakraProps &
  PropsWithChildren<{
    icon: React.ReactElement;
    onClick?: () => void;
  }>) {
  return (
    <MenuItem
      icon={icon}
      fontSize="14px"
      fontWeight="500"
      color="fluentHealthSecondary.100"
      borderRadius="8px"
      py="10px"
      px="8px"
      _hover={{ bg: 'fluentHealthSecondary.500' }}
      _active={{ bg: 'fluentHealthSecondary.500' }}
      _focus={{ bg: 'fluentHealthSecondary.500' }}
      {...props}
    >
      {children}
    </MenuItem>
  );
}
