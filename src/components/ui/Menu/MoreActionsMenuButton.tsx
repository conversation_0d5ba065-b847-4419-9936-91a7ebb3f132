import { ChakraProps, IconButton, MenuButton } from '@chakra-ui/react';
import { MoreHorizontal as MoreHorizontalIcon } from 'react-feather';

export function MoreActionsMenuButton({ iconSize = 18, ...props }: ChakraProps & { iconSize?: number }) {
  return (
    <MenuButton
      as={IconButton}
      size="24px"
      height="fit-content"
      p="6px"
      icon={<MoreHorizontalIcon size={iconSize} />}
      aria-label="More actions"
      bgColor="transparent"
      color="fluentHealthText.300"
      borderRadius="50%"
      _hover={{
        color: 'fluentHealthText.100',
        bgColor: 'fluentHealthSecondary.300',
      }}
      {...props}
    />
  );
}
