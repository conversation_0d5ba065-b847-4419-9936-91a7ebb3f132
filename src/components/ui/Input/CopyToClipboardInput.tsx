import { Link2 as LinkIcon } from 'react-feather';
import { Box, Button, ChakraProps, Flex, Text, useTheme } from '@chakra-ui/react';

export function CopyToClipboardInput({
  value,
  onCopy,
  isLoading = false,
  isDisabled = false,
  ...props
}: ChakraProps & {
  value?: string;
  onCopy?: () => void;
  isLoading?: boolean;
  isDisabled?: boolean;
}) {
  const theme = useTheme();

  return (
    <Flex
      border="1px solid"
      borderColor="fluentHealthText.500"
      borderRadius="lg"
      paddingLeft="16px"
      paddingRight="16px"
      align="center"
      justify="space-between"
      gap="20px"
      w="auto"
      h="40px"
      transition="opacity 0.1s ease-in-out"
      {...(isLoading || isDisabled
        ? {
            userSelect: 'none',
            pointerEvents: 'none',
            opacity: 0.5,
          }
        : {})}
      {...props}
    >
      <Text
        fontFamily="Apercu"
        fontWeight="normal"
        fontSize="md"
        lineHeight="1"
        color="fluentHealthText.100"
        overflow="hidden"
        textOverflow="ellipsis"
        whiteSpace="nowrap"
      >
        {value}
      </Text>
      <Button
        fontSize="md"
        color="fluentHealth.500"
        flexShrink={0}
        padding={0}
        backgroundColor="transparent"
        onClick={() => onCopy?.()}
        isLoading={isLoading}
        isDisabled={isDisabled}
        rightIcon={
          <Box transform="rotate(-45deg)">
            <LinkIcon
              size={16}
              color={theme.colors.fluentHealth[500]}
            />
          </Box>
        }
        _hover={{
          backgroundColor: 'transparent',
        }}
        _disabled={{
          backgroundColor: 'transparent',
        }}
      >
        Copy link
      </Button>
    </Flex>
  );
}
