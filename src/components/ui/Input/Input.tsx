import { useAutoAnimate } from '@formkit/auto-animate/react';
import clsx from 'clsx';
import React from 'react';
import { ControllerFieldState } from 'react-hook-form';
import useResizeObserver from 'use-resize-observer';

import { InferBaseHtmlType } from '../_helpers';
import { TappInputError } from '../InputError';
import { TappLabel } from '../Label/Label';

type BaseType = Omit<JSX.IntrinsicElements['input'], 'prefix' | 'ref'>;
export type TappInputProps = {
  name?: string;
  label?: string;
  border?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
} & BaseType &
  Partial<ControllerFieldState>;

const TappInput = React.forwardRef<InferBaseHtmlType<BaseType>, TappInputProps>(
  ({ label, prefix, suffix, className, border, style, error, invalid, isDirty, isTouched, ...props }, forwardedRef) => {
    const [errorRef] = useAutoAnimate();
    const hasErrorVisible = isTouched && invalid && error;

    const { ref: prefixRef, width: prefixWidth = 0 } = useResizeObserver<HTMLSpanElement>({ box: 'border-box' });
    const { ref: suffixRef, width: suffixWidth = 0 } = useResizeObserver<HTMLSpanElement>({ box: 'border-box' });

    return (
      <div className={clsx('group flex flex-shrink flex-col', className)}>
        {label && (
          <TappLabel
            htmlFor={props.id || props.name}
            className="mb-1"
          >
            {label}
          </TappLabel>
        )}

        <div className="relative w-full">
          {!!prefix && (
            <span
              className={clsx('absolute left-0 top-0 bottom-0 flex h-full items-center justify-center pl-4 pr-2', {
                'text-error': hasErrorVisible,
                'text-gray-400 group-focus-within:text-primary': !hasErrorVisible,
              })}
              ref={prefixRef}
            >
              {prefix}
            </span>
          )}
          <input
            className={clsx(
              'h-10 w-full rounded border bg-white px-4 text-sm font-medium transition placeholder:text-gray-400 hover:bg-gray-50 group-focus-within:bg-white',
              {
                'border-gray-200': border && !hasErrorVisible,
                'border-transparent': !border && !hasErrorVisible,
                'border-error': hasErrorVisible,
                'group-focus-within:border-primary': !hasErrorVisible,
              }
            )}
            style={{
              ...(prefixWidth > 0 && { paddingLeft: `${prefixWidth}px` }),
              ...(suffixWidth > 0 && { paddingRight: `${suffixWidth}px` }),
              ...(style ?? {}),
            }}
            ref={forwardedRef}
            {...props}
            // {...field}
            id={props.id || props.name}
          />

          {!!suffix && (
            <span
              className={clsx(
                'absolute right-0 top-0 bottom-0 flex h-full items-center justify-center pl-2 pr-4 text-gray-400'
              )}
              ref={suffixRef}
            >
              {suffix}
            </span>
          )}
        </div>

        <div ref={errorRef}>{hasErrorVisible && <TappInputError className="mt-1">{error.message}</TappInputError>}</div>
      </div>
    );
  }
);

TappInput.displayName = 'TappInput';
export { TappInput };
