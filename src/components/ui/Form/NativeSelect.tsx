import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { PropsWithChildren } from 'react';
import { RegisterOptions, useFormContext } from 'react-hook-form';

export function NativeSelect({
  labelText,
  errorText,
  name,
  rules,
  children,
  isInvalid,
  isDisabled,
}: PropsWithChildren<{
  name: string;
  labelText?: string;
  errorText?: string;
  rules?: RegisterOptions;
  isInvalid?: boolean;
  isDisabled?: boolean;
}>) {
  const { register, watch } = useFormContext();
  const field = watch(name);
  const hasSelectedOption = field.length > 0;

  return (
    <FormControl
      variant="floating"
      isInvalid={isInvalid}
      isDisabled={isDisabled}
    >
      <Select
        color={field.length > 0 ? 'gray.500' : 'gray.300'}
        variant="flushed"
        focusBorderColor="gray.500"
        borderBottom="1px solid"
        borderBottomColor="gray.100"
        fontSize="lg"
        letterSpacing="-0.02em"
        {...register(name, rules)}
        _placeholder={{ color: 'gray.300' }}
        _focusWithin={{
          boxShadow: 'none',
        }}
      >
        <option label=" " />
        {children}
      </Select>
      {labelText && <FormLabel {...(hasSelectedOption ? {} : { transform: 'none !important' })}>{labelText}</FormLabel>}
      {errorText && <FormErrorMessage>{errorText}</FormErrorMessage>}
    </FormControl>
  );
}
