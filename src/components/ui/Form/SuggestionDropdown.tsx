import { ChangeEvent, useCallback, useEffect, useRef, useState } from 'react';
import { ArrowUpLeft as ArrowUpLeftIcon } from 'react-feather';
import {
  Box,
  Flex,
  IconButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spinner,
  Text,
  useDisclosure,
  useOutsideClick,
} from '@chakra-ui/react';

import { debounce } from '@lib/utils/utils';

export interface SuggestionOptionProps {
  value: string | number;
  label: string;
  payload?: any;
  isSelected?: boolean;
  hideIcon?: boolean;
}

function SuggestionOption({ option, onSelect }: { option: SuggestionOptionProps; onSelect: Function }) {
  return (
    <Flex
      key={option.value}
      justify="space-between"
      align="center"
      borderRadius="8px"
      py="10px"
      px="8px"
      cursor="pointer"
      bg={option.isSelected ? 'fluentHealthSecondary.500' : 'transparent'}
      _hover={{ bg: 'fluentHealthSecondary.500' }}
      _active={{ bg: 'fluentHealthSecondary.500' }}
      _focus={{ bg: 'fluentHealthSecondary.500' }}
      onClick={() => onSelect(option)}
    >
      <Text maxW="300px">{option.label}</Text>
      {!option.hideIcon && (
        <IconButton
          icon={<ArrowUpLeftIcon size={18} />}
          aria-label="Icon Button"
          bgColor="transparent"
          color="fluentHealthText.300"
          size="18px"
          p="6px"
          borderRadius="50%"
          flexShrink="0"
          _hover={{
            color: 'fluentHealthText.100',
            bgColor: 'fluentHealthSecondary.300',
          }}
        />
      )}
    </Flex>
  );
}

export function SuggestionDropdown({
  textValue = '',
  debounceDelay = 300,
  options,
  optionRenderer = SuggestionOption,
  children,
  onChange,
  onClear,
  onSelect,
  keepOpenAfterBlur = true,
  resetTextValueAfterBlur = true,
  isFreeInput = false,
  visibleScrollbar = false,
}: {
  textValue?: string;
  debounceDelay?: number;
  options: SuggestionOptionProps[];
  optionRenderer?: (props: any) => React.ReactNode;
  children: (props: any) => React.ReactNode;
  onChange: Function;
  onClear: Function;
  onSelect: Function;
  keepOpenAfterBlur?: boolean;
  resetTextValueAfterBlur?: boolean;
  isFreeInput?: boolean;
  visibleScrollbar?: boolean;
}) {
  const [searchText, setSearchText] = useState<string>(textValue);
  const [isFetchingSuggestions, setIsFetchingSuggestions] = useState(false);

  const resultsPopover = useDisclosure();

  const searchInputContainerRef = useRef<HTMLDivElement | null>(null);
  useOutsideClick({
    ref: searchInputContainerRef,
    handler: () => {
      if (keepOpenAfterBlur && searchText.length > 0) {
        return false;
      }

      resultsPopover.onClose();
      return true;
    },
  });

  const clearState = () => {
    onClear();
    setIsFetchingSuggestions(false);
    resultsPopover.onClose();
  };

  const debounsedUpdateSuggestions = debounce(async (searchQuery: string) => {
    if (searchQuery === '') {
      clearState();
      return false;
    }

    try {
      const filteredResults = await onChange(searchQuery);

      if (filteredResults.length === 0) {
        clearState();
        return false;
      }

      setIsFetchingSuggestions(false);
    } catch (_) {
      clearState();
    }

    return true;
  }, debounceDelay);

  const searchTextChangeHandler = (value: string) => {
    setSearchText(value);
    onClear();
    setIsFetchingSuggestions(true);
    resultsPopover.onOpen();
    debounsedUpdateSuggestions(value);

    // Update parent on text change if isFreeInput property is true
    if (isFreeInput) {
      onSelect({ value });
    }
    return true;
  };

  const searchInputChangeHandler = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    searchTextChangeHandler(value);
  }, []);

  const searchInputBlueHandler = useCallback(() => {
    if (resetTextValueAfterBlur) {
      setSearchText(textValue);
    }
  }, [textValue, resetTextValueAfterBlur]);

  const searchPrefillHandler = (item: SuggestionOptionProps) => {
    onSelect(item);
    resultsPopover.onClose();
  };

  useEffect(() => {
    setSearchText(textValue);
  }, [textValue]);

  return (
    <Box
      ref={searchInputContainerRef}
      position="relative"
    >
      <Popover
        autoFocus={false}
        returnFocusOnClose={false}
        isOpen={resultsPopover.isOpen}
        onClose={resultsPopover.onClose}
        placement="bottom-start"
        closeOnBlur={false}
        matchWidth
      >
        <PopoverTrigger>
          {children({
            searchText,
            searchInputChangeHandler,
            searchInputBlueHandler,
            suggestionDropdownPopover: resultsPopover,
          })}
        </PopoverTrigger>
        <PopoverContent
          className="hide-scrollbar"
          width="100%"
          borderRadius="12px"
          borderColor="fluentHealthSecondary.300"
          p="4px"
          boxShadow="0px 1px 4px rgba(7, 16, 84, 0.1), 0px 10px 28px -2px rgba(7, 16, 84, 0.14)"
          maxH="200px"
          overflowY="auto"
        >
          {isFetchingSuggestions && (
            <Flex
              justify="center"
              align="center"
              py="4"
              width="full"
            >
              <Spinner
                width="18px"
                height="18px"
              />
            </Flex>
          )}
          <Flex
            className={visibleScrollbar ? '' : 'hide-scrollbar'}
            direction="column"
            overflowY="auto"
            sx={{
              '&::-webkit-scrollbar': {
                width: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'iris.300',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent',
              },
            }}
          >
            {options.map((option) =>
              optionRenderer({
                option,
                onSelect: searchPrefillHandler,
              })
            )}
          </Flex>
        </PopoverContent>
      </Popover>
    </Box>
  );
}
