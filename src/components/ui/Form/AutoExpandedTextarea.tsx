import { ChakraProps, Textarea, TextareaProps } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { FieldPath, FieldValues, UseControllerProps, useController } from 'react-hook-form';

import { autoExpandTextareaElement } from '@lib/utils/utils';

// https://github.com/react-hook-form/react-hook-form/issues/8618#issuecomment-1180236280
type InputProps<TFieldValues extends FieldValues, TName extends FieldPath<TFieldValues>> = UseControllerProps<
  TFieldValues,
  TName
>;

export function AutoExpandedTextarea<TFieldValues extends FieldValues, TName extends FieldPath<TFieldValues>>({
  controllerProps,
  ...props
}: ChakraProps &
  TextareaProps & {
    controllerProps: InputProps<TFieldValues, TName>;
  }) {
  const [element, setElement] = useState<HTMLTextAreaElement | null>(null);
  const { field } = useController(controllerProps);

  const onChangeHandler = (event: any) => {
    autoExpandTextareaElement(event.target);
    field.onChange(event);
  };

  // Update height on first render
  useEffect(() => {
    if (element) {
      autoExpandTextareaElement(element);
    }
  }, [element]);

  return (
    <Textarea
      borderColor="fluentHealthText.100"
      minH="32px"
      maxH="120px"
      onChange={onChangeHandler}
      onBlur={field.onBlur}
      value={field.value}
      name={field.name}
      ref={(el) => {
        field.ref(el);
        setElement(el);
      }}
      {...props}
    />
  );
}
