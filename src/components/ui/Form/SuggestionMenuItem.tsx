import { Box, Flex, HStack, Text, useTheme } from '@chakra-ui/react';
import { PlusCircle as AddIcon } from 'react-feather';

import { MedicalRecord } from '@lib/models/medical-record';
import { MEDICAL_RECORD_ICON_MAP } from 'src/app/medical-records/lib/constants';

export function SuggestionMenuItem({ record }: { record: MedicalRecord }) {
  const theme = useTheme();
  return (
    <Flex
      justifyContent="space-between"
      alignItems="center"
      flex={1}
      pl="2"
      pr="4"
      py="2"
    >
      <HStack>
        <Box
          ml="1"
          mb="1"
          maxW="32px"
          height="40px"
        >
          {MEDICAL_RECORD_ICON_MAP[record.type?.slug]}
        </Box>
        <Flex flexDirection="column">
          <Text fontSize="sm">{record.title}</Text>
          <Text
            fontSize="xs"
            color="fluentHealthText.400"
          >
            Consultation from {record.date}
          </Text>
        </Flex>
      </HStack>
      <AddIcon
        size="20px"
        color={theme.colors.fluentHealthText['400']}
      />
    </Flex>
  );
}
