import React, { PropsWithChildren } from 'react';
import { Box, ChakraProps, RadioGroup, RadioGroupProps } from '@chakra-ui/react';

export function GroupedRadioButtons({ children, ...props }: PropsWithChildren<ChakraProps & RadioGroupProps>) {
  return (
    <RadioGroup
      py="2"
      width="max-content"
      display="flex"
      alignItems="center"
      gap="4px"
      border="1px solid"
      borderColor="iris.500"
      borderRadius="8px"
      sx={{
        '& > .chakra-radio': {
          px: '2',
        },
      }}
      {...props}
    >
      {children}
    </RadioGroup>
  );
}

export function GroupedRadioButtonsDivider(props: ChakraProps) {
  return (
    <Box
      width="1px"
      height="24px"
      bgColor="iris.500"
      {...props}
    />
  );
}
