import React, { PropsWithChildren, forwardRef, useEffect } from 'react';
import { RegisterOptions, useFormContext } from 'react-hook-form';
import dayjs from 'dayjs';
import {
  Box,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  PopoverProps,
  UseDisclosureReturn,
  useTheme,
} from '@chakra-ui/react';

import { hexOpacity } from '../../theme/utils';
import { DatePicker, DatePickerClearSelectionButton, DatePickerPopover } from '../DatePicker';

// Assets
import { ReactComponent as CalendarIcon } from '@assets/icons/calendar.svg';

// Utility function to extract display value from saved date (removes timezone part)
const getDisplayValue = (value: string): string => {
  if (!value || value.trim() === '') return '';

  // Extract YYYY-MM-DD part from formats like "2023-09-05T00:00:00.000Z" or "2023-09-05"
  const dateOnly = value.split('T')[0];

  // Validate that the extracted date is a valid YYYY-MM-DD format
  // Also ensure it's not just an empty string or invalid date
  if (dateOnly && dayjs(dateOnly, 'YYYY-MM-DD', true).isValid()) {
    return dateOnly;
  }

  return '';
};

export enum DATEPICKER_VARIANTS {
  FLOATING = 'floating',
  ROUNDED_TRANSPARENT = 'roundedTransparent',
}

type FieldProps = {
  variant?: DATEPICKER_VARIANTS;
  name: string;
  labelText?: string;
  errorText?: string;
  placeholderText?: string;
  rules?: RegisterOptions;
  isInvalid?: boolean;
  isDisabled?: boolean;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const DatePickerInput = forwardRef(
  (
    {
      variant = DATEPICKER_VARIANTS.FLOATING,
      name,
      labelText = undefined,
      errorText = undefined,
      placeholderText = ' ',
      isInvalid = false,
      isDisabled = false,
    }: PropsWithChildren<FieldProps>,
    ref: React.ForwardedRef<HTMLDivElement>
  ) => {
    const theme = useTheme();

    const { watch, trigger, setValue } = useFormContext();
    const field = watch(name);
    const displayValue = getDisplayValue(field);

    // Since the date picker and the date input are not linked, we need to manually validate if the input is valied.
    useEffect(() => {
      trigger(name);
    }, [field]);

    if (variant === DATEPICKER_VARIANTS.ROUNDED_TRANSPARENT) {
      return (
        <FormControl isInvalid={isInvalid}>
          <InputGroup size="xl">
            <Input
              type="text"
              variant={variant}
              color={displayValue.length > 0 ? 'gray.500' : hexOpacity(theme.colors.periwinkle[700], 0.6)}
              size="xl"
              value={displayValue}
              onChange={(e) => {
                // Update the form with the display value, but the actual save format will be handled by the DatePickerField
                setValue(name, e.target.value);
              }}
              sx={{
                '&::-webkit-inner-spin-button, &::-webkit-calendar-picker-indicator': {
                  display: 'none',
                  WebkitAppearance: 'none',
                },
                '&::-webkit-date-and-time-value': { textAlign: 'left' },
              }}
              isDisabled={isDisabled}
              {...(placeholderText ? { placeholder: placeholderText } : {})}
            />
            <InputRightElement>
              <CalendarIcon color={theme.colors.papaya[600]} />
            </InputRightElement>
          </InputGroup>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
      );
    }

    return (
      <FormControl
        ref={ref}
        variant={variant}
        isInvalid={isInvalid}
      >
        <InputGroup display="block">
          <Input
            type="text"
            color={displayValue?.length > 0 ? 'gray.500' : 'gray.300'}
            variant="flushed"
            value={displayValue}
            onChange={(e) => {
              // Update the form with the display value, but the actual save format will be handled by the DatePickerField
              setValue(name, e.target.value);
            }}
            isDisabled={isDisabled}
            sx={{
              '&::-webkit-inner-spin-button, &::-webkit-calendar-picker-indicator': {
                display: 'none',
                WebkitAppearance: 'none',
              },
              '&::-webkit-date-and-time-value': { textAlign: 'left' },
            }}
            {...(placeholderText ? { placeholder: placeholderText } : {})}
          />
          {labelText && <FormLabel>{labelText}</FormLabel>}
          {errorText && <FormErrorMessage>{errorText}</FormErrorMessage>}
          <InputRightElement>
            <CalendarIcon color={theme.colors.papaya[600]} />
          </InputRightElement>
        </InputGroup>
      </FormControl>
    );
  }
);

export function DatePickerField({
  // Field props
  variant,
  name,
  labelText = undefined,
  errorText = undefined,
  placeholderText = undefined,
  rules = undefined,
  isInvalid = false,
  isDisabled = false,
  popoverProps,
  // Datepicker props
  datePickerPopover,
  datePickerChangeHandler,
  selected,
  maxDate,
  minDate,
  excludeDates,
  datePickerClearHandler,
  onClickOutside,
  showClearDateButton = true,
  isClearDateButtonDisabled = false,
}: {
  datePickerPopover: UseDisclosureReturn;
  datePickerChangeHandler: (date: Date | null) => void;
  selected: Date | null;
  maxDate?: Date | null;
  minDate?: Date | null;
  excludeDates?: Date[];
  datePickerClearHandler?: Function;
  showClearDateButton?: boolean;
  popoverProps?: PopoverProps;
  onClickOutside?: () => void;
  isClearDateButtonDisabled?: boolean;
  isInvalid?: boolean;
} & FieldProps) {
  return (
    <DatePickerPopover
      popoverProps={popoverProps}
      datePickerPopover={datePickerPopover}
      isDisabled={isDisabled}
      onClickOutside={onClickOutside}
      popoverTriggerElement={
        <Box>
          <DatePickerInput
            variant={variant}
            name={name}
            labelText={labelText}
            errorText={errorText}
            placeholderText={placeholderText}
            rules={rules}
            isInvalid={isInvalid}
            isDisabled={isDisabled}
          />
        </Box>
      }
    >
      <DatePicker
        monthsShown={1}
        onChange={datePickerChangeHandler}
        selected={selected}
        maxDate={maxDate}
        minDate={minDate}
        excludeDates={excludeDates}
        inline
        isClearable
      />
      {showClearDateButton && (
        <DatePickerClearSelectionButton
          mt="12px"
          isDisabled={isClearDateButtonDisabled}
          onClick={() => datePickerClearHandler?.()}
        >
          Clear selection
        </DatePickerClearSelectionButton>
      )}
    </DatePickerPopover>
  );
}
