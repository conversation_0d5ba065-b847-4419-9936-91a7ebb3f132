import { cva, VariantProps } from 'class-variance-authority';
import clsx from 'clsx';

const styles = cva('py-1 px-3 rounded-full border text-sm', {
  variants: {
    color: {
      primary: 'bg-primary-50 text-primary-600 border-primary-600',
      accent: 'bg-accent-50 text-accent-600 border-blue-600',
      success: 'bg-success-50 text-success-600 border-success-600',
      error: 'bg-error-50 text-error-600 border-error-600',
      disabled: 'bg-gray-100 text-gray-500 border-gray-500',
      amber: 'bg-amber-50 text-amber-600 border-amber-600',
    },
  },
  defaultVariants: {
    color: 'primary',
  },
});

export type TappPillProps = {} & VariantProps<typeof styles> & JSX.IntrinsicElements['span'];

export const TappPill: React.FC<TappPillProps> = ({ color, className, ...props }) => {
  return (
    <span
      className={clsx(styles({ color }), className)}
      {...props}
    />
  );
};
