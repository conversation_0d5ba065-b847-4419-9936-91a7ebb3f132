import { useQuery } from '@tanstack/react-query';
import { ColumnDef, getCoreRowModel, getSortedRowModel, TableOptions, useReactTable } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { PaginatedResponse } from '@lib/models/paginated-response';

export type FilterParams = Record<string, string>;

export const DEFAULT_PAGE_INDEX = 0;
export const DEFAULT_PAGE_SIZE = 20;

export type ApiTableParams<T, V> = {
  queryKey: (filters: FilterParams) => [string, V];
  queryFn: (filters: FilterParams) => Promise<PaginatedResponse<T>>;
  columns: ColumnDef<T, any>[];
  isPaginated?: boolean;
} & Omit<
  TableOptions<T>,
  | 'data'
  | 'columns'
  | 'pageCount'
  | 'state'
  | 'getCoreRowModel'
  | 'getSortedRowModel'
  | 'manualPagination'
  | 'manualSorting'
  | 'autoResetPageIndex'
>;

export const useApiTable = <T = unknown, V extends FilterParams = FilterParams>({
  queryKey,
  queryFn,
  columns,
  isPaginated = true,
  ...options
}: ApiTableParams<T, V>) => {
  const [searchParams] = useSearchParams();

  const [filterParams, setFilterParams] = useState<FilterParams>(
    isPaginated
      ? {
          pageIndex: DEFAULT_PAGE_INDEX.toString(),
          pageSize: DEFAULT_PAGE_SIZE.toString(),
        }
      : {}
  );

  const {
    data: queryData,
    isLoading,
    error,
  } = useQuery({
    queryKey: queryKey(filterParams),
    queryFn: () => queryFn(filterParams),
    keepPreviousData: true,
  });

  const pageIndex = parseInt(filterParams.pageIndex, 10);
  const pageSize = parseInt(filterParams.pageSize, 10);
  const data = queryData?.data ?? [];
  const total = queryData?.total ?? 0;

  const table = useReactTable({
    data,
    columns,
    pageCount: isPaginated ? Math.ceil(total / pageSize) : 1,
    state: { pagination: { pageIndex, pageSize } },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    manualSorting: true,
    autoResetPageIndex: false,
    ...options,
  });

  useEffect(() => {
    // Get other keys from searchParams and set them as filters
    const fp: Record<string, string> = {};
    searchParams.forEach((value, key) => (fp[key] = value));
    if (isPaginated) {
      if (!fp.pageIndex) fp.pageIndex = DEFAULT_PAGE_INDEX.toString();
      if (!fp.pageSize) fp.pageSize = DEFAULT_PAGE_SIZE.toString();
    }
    setFilterParams(fp);
  }, [searchParams]);

  return { table, data, total, error, isLoading, filter: filterParams, setFilter: setFilterParams };
};
