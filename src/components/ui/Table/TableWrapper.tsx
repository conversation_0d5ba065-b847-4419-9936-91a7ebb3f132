import { cva, VariantProps } from 'class-variance-authority';
import clsx from 'clsx';

const styles = cva('bg-white border border-gray-200 rounded overflow-y-clip', {
  variants: {},
  defaultVariants: {},
});

export type TappTableWrapperProps = {} & VariantProps<typeof styles> & JSX.IntrinsicElements['section'];

export const TappTableWrapper: React.FC<TappTableWrapperProps> = ({ children, className, ...props }) => {
  // console.log(children);
  // const table =
  //   children instanceof Array
  //     ? children.find((node) => node?.type?.name === 'TappTable')
  //     : (children as any)?.type?.node === 'TappTable'
  //     ? children
  //     : null;

  return <section className={clsx(styles({}), className)}>{children}</section>;
};
