import { PropsWithChildren } from 'react';
import { Box, ChakraProps, Link, LinkBoxProps } from '@chakra-ui/react';
import { LinkProps, Link as RouterLink, useLocation } from 'react-router-dom';

export function TabLink(props: PropsWithChildren & ChakraProps & LinkProps & LinkBoxProps & { isActive?: boolean }) {
  const { pathname } = useLocation();

  const { to, isActive } = props;
  const toPathName = String(to).split('?')[0];
  const match = pathname === toPathName;

  const conditionalProps =
    match || isActive
      ? {
          bg: '#F0F1FF',
          borderColor: 'iris.500',
        }
      : {
          bg: 'linear-gradient(180deg, rgba(73, 86, 228, 0) 58.29%, rgba(73, 86, 228, 0.1) 100%), #F0F1FF',
        };

  return to ? (
    <Link
      as={RouterLink}
      display="flex"
      flex={1}
      justifyContent="center"
      alignItems="center"
      padding={{ base: '20px 32px', sm: '12px 0px', md: '12px 0px' }}
      borderRadius="16px 16px 0px 0px"
      borderTop="2px solid"
      borderColor="periwinkle.100"
      fontWeight="500"
      whiteSpace="nowrap"
      height={{ base: '42px', md: '48px' }}
      _hover={{
        textDecoration: 'none',
        cursor: 'pointer',
      }}
      {...conditionalProps}
      {...props}
    />
  ) : (
    <Box
      display="flex"
      flex={1}
      justifyContent="center"
      alignItems="center"
      padding={{ base: '20px 32px', sm: '12px 0px', md: '12px 0px' }}
      borderRadius="16px 16px 0px 0px"
      borderTop="2px solid"
      borderColor="periwinkle.100"
      fontWeight="500"
      whiteSpace="nowrap"
      height={{ base: '42px', md: '48px' }}
      _hover={{
        textDecoration: 'none',
        cursor: 'pointer',
      }}
      {...conditionalProps}
      {...props}
    />
  );
}
