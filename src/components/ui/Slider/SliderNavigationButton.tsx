import { ChakraProps, Flex, ScaleFade } from '@chakra-ui/react';

import { ReactComponent as ChevronLeftIcon } from '@assets/icons/slider-chevron-left.svg';
import { ReactComponent as ChevronRightIcon } from '@assets/icons/slider-chevrone-right.svg';

export function SliderNavigationButton({
  onClick,
  side,
  showButton,
  ...props
}: ChakraProps & {
  onClick: () => void;
  side: 'left' | 'right';
  showButton: boolean;
}) {
  return (
    <Flex
      onClick={onClick}
      position="absolute"
      top="0"
      zIndex="1"
      height="100%"
      width="140px"
      align="center"
      bg="transparent"
      cursor="pointer"
      {...props}
    >
      <ScaleFade
        initialScale={0.7}
        in={showButton}
      >
        <Flex
          align="center"
          justify="center"
          width="50px"
          height="50px"
          bg="white"
          borderRadius="50%"
          color="fluentHealthText.100"
        >
          {side === 'left' ? (
            <ChevronLeftIcon
              width="12px"
              height="20px"
            />
          ) : (
            <ChevronRightIcon
              width="12px"
              height="20px"
            />
          )}
        </Flex>
      </ScaleFade>
    </Flex>
  );
}
