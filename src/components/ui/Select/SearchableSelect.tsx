import React, { useMemo } from 'react';

import {
  LoadingMessageRenderer,
  Select,
  SelectOptionProps,
  // SelectProps
} from './Select';

// Todo any to be replaced with after correction
// SelectProps & {
//   value?: any;
//   options?: any;
//   onSearch?: (text: string) => void;
//   onChange?: (newValue: SelectOptionProps | any) => void;
//   onLoadMore?: () => void;
//   labelText?: string;
//   hasNextPage?: boolean | undefined;
// }
export function SearchableSelect({
  value,
  options,
  onSearch,
  onChange,
  onLoadMore,
  labelText,
  hasNextPage,
  isMedicationSelect,
  ...props
}: any) {
  if (!options) {
    return null;
  }
  const selectOptions = useMemo<SelectOptionProps[]>(
    () => [
      ...options,
      ...(hasNextPage
        ? [
            {
              label: <LoadingMessageRenderer />,
              value: -1,
              payload: {},
            },
          ]
        : []),
    ],
    [options]
  );

  return (
    <Select
      labelText={labelText}
      value={value}
      options={selectOptions}
      onInputChange={onSearch}
      onChange={onChange}
      onMenuScrollToBottom={onLoadMore}
      menuPosition="fixed"
      maxMenuHeight={222}
      isClearable
      isSearchable
      openMenuOnFocus
      isMedicationSelect={isMedicationSelect}
      // isOptionDisabled={(option: SelectOptionProps | any) => option.value === -1}
      {...props}
    />
  );
}
