import { Box, ChakraProps, Flex, FormLabel, Skeleton, useOutsideClick } from '@chakra-ui/react';
import React, { PropsWithChildren, ReactElement, ReactNode, Ref, forwardRef, useCallback, useRef } from 'react';
import BaseSelect, {
  ClassNamesConfig,
  ClearIndicatorProps,
  DropdownIndicatorProps,
  GroupBase,
  MultiValueProps,
  MultiValueRemoveProps,
  OptionProps,
  Props,
  StylesConfig,
  ValueContainerProps,
  components,
} from 'react-select';
import {
  PlusCircle as AddIcon,
  ChevronDown as ArrowDownIcon,
  CheckCircle as CheckedIcon,
  X as ClearIcon,
  Search as SearchIcon,
  Trash as TrashIcon,
} from 'react-feather';
import { useHover } from 'usehooks-ts';

import { theme } from '../../theme/theme';
import { hexOpacity } from '../../theme/utils';

export interface SelectOptionProps<Payload = any> {
  value: string | number;
  label: string | ReactNode;
  payload?: Payload;
}

export type SelectProps = Props & {
  labelText?: string;
  selectContainerProps?: ChakraProps;
  hideSelectedOptions?: boolean;
  hideLabelWhenSelectedValue?: boolean;
  onInputChange?: (value: string) => void;
  onClickOutside?: () => void;
  MultiValue?: (props: MultiValueProps) => ReactElement;
  DropdownIndicator?: (props: DropdownIndicatorProps) => ReactElement;
  ClearIndicator?: (props: ClearIndicatorProps) => ReactElement | null;
  MultiValueRemove?: (props: MultiValueRemoveProps) => ReactElement | null;
  ValueContainer?: (props: ValueContainerRendererProps) => ReactElement;
  OptionItem?: (props: OptionProps) => ReactElement;
  LoadingMessage?: () => ReactElement;
  disabledOptions?: string[];
  isMedicationSelect?: boolean;
  icon?: string;
};

const SELECT_CLASS_NAMES: ClassNamesConfig = {
  menuList: () => 'hide-scrollbar',
};

export const SELECT_STYLES: StylesConfig = {
  menuPortal: (base) => ({
    ...base,
    zIndex: 3,
  }),
  menu: (base) => ({
    ...base,
    borderRadius: '12px',
    borderColor: theme.colors.fluentHealthSecondary[300],
    padding: '4px',
    zIndex: 3,
  }),
  menuList: (base) => ({
    ...base,
    padding: 0,
    '::webkit-scrollbar': {
      display: 'none',
      msOverflowStyle: 'none',
      scrollbarWidth: 'none',
    },
  }),
  option: (base, state) => ({
    ...base,
    fontSize: '18px',
    color: theme.colors.gray[500],
    padding: '0',
    marginBottom: '4px',
    borderRadius: '8px',
    cursor: 'pointer',
    backgroundColor: state.isSelected ? theme.colors.fluentHealthSecondary[500] : 'transparent',
    ':hover, :active': {
      backgroundColor: theme.colors.fluentHealthSecondary[500],
    },
    ':last-of-type': {
      marginBottom: 0,
    },
  }),
  control: (base, state) => ({
    ...base,
    ...(state.isDisabled ? { userSelect: 'none' } : {}),
    opacity: state.isDisabled ? 0.4 : 1,
    backgroundColor: 'transparent',
    borderRadius: 0,
    outline: 'none',
    boxShadow: 'none',
    border: `1px solid ${theme.colors.iris[500]}`,
    borderTop: 'none',
    borderLeft: 'none',
    borderRight: 'none',
    minHeight: '40px',
    ':hover': {
      borderColor: theme.colors.periwinkle[700],
      '.chakra-form__label': {
        color: theme.colors.periwinkle[700],
      },
      '& .MultiValueGeneric2': {
        color: theme.colors.periwinkle[700],
      },
      '& div[class$="singleValue"]': {
        color: theme.colors.periwinkle[700],
      },
    },
    '.chakra-form__label': {
      fontWeight: 400,
      top: 0,
      left: 0,
      zIndex: 1,
      position: 'absolute',
      backgroundColor: 'transparent',
      pointerEvents: 'none',
      px: 0,
      mr: 0,
      my: '4px',
      transformOrigin: 'left top',
    },
  }),
  indicatorsContainer: (base) => ({
    ...base,
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
  }),
  loadingIndicator: (base) => ({
    ...base,
    color: theme.colors.iris[500],
  }),
  valueContainer: (base) => ({
    ...base,
    padding: 0,
    overflow: 'visible',
    minHeight: '31px',
  }),
  multiValue: (base) => ({
    ...base,
    gap: '2px',
    alignItems: 'center',
    padding: 0,
    margin: '0 8px 0 0',
    backgroundColor: 'transparent',
    '& > .MultiValueGeneric2': {
      whiteSpace: 'break-spaces',
      color: theme.colors.iris[500],
    },
  }),
  multiValueLabel: (base) => ({
    ...base,
    fontSize: '18px',
    padding: 0,
    paddingLeft: 0,
    paddingTop: '4px',
    backgroundColor: 'transparent',
  }),
  multiValueRemove: (base) => ({
    ...base,
    padding: '3px',
    height: 'max-content',
    borderRadius: '50%',
    ':hover': {
      backgroundColor: 'transparent',
      color: theme.colors.fluentHealthComplementary.Red,
    },
  }),
  input: (base) => ({
    ...base,
    fontSize: '18px',
    color: theme.colors.iris[500],
    margin: 0,
  }),
  singleValue: (base) => ({
    ...base,
    fontSize: '18px',
    color: theme.colors.iris[500],
    marginLeft: 0,
    '& > div[class$="MultiValueGeneric2"]': {
      whiteSpace: 'break-spaces',
    },
  }),
  placeholder: (base, state) => ({
    ...base,
    fontSize: '18px',
    color: theme.colors.iris[500],
    marginLeft: 0,
    display: state.isFocused || state.selectProps.inputValue ? 'none' : 'block',
  }),
  dropdownIndicator: (base) => ({
    ...base,
    color: theme.colors.papaya[600],
    padding: '8px 10px 10px 8px',
    cursor: 'pointer',
    ':hover': {
      color: theme.colors.papaya[600],
    },
  }),
  clearIndicator: (base) => ({
    ...base,
    color: theme.colors.papaya[600],
    padding: '8px 4px 10px 4px',
    cursor: 'pointer',
    opacity: 0.6,
    ':hover': {
      color: theme.colors.fluentHealthComplementary.Red,
      opacity: 1,
    },
  }),
  indicatorSeparator: (base) => ({
    ...base,
    display: 'none',
  }),
};

function DropdownIndicatorRenderer(props: DropdownIndicatorProps) {
  const { isMulti } = props || {};
  return (
    <components.DropdownIndicator {...props}>
      {isMulti ? <SearchIcon size={18} /> : <ArrowDownIcon size={18} />}
    </components.DropdownIndicator>
  );
}

export function CustomDropdownIndicatorRenderer(props: DropdownIndicatorProps) {
  return <components.DropdownIndicator {...props} />;
}

function ClearIndicatorRenderer(props: ClearIndicatorProps) {
  const { getValue } = props;
  const firstChip = getValue()[0] as any;
  const isFirstChipNotEmpty = firstChip?.value && String(firstChip.value)?.trim()?.length > 0;

  if (isFirstChipNotEmpty) {
    return (
      <components.ClearIndicator {...props}>
        <ClearIcon size={16} />
      </components.ClearIndicator>
    );
  }

  return null;
}

type ValueContainerRendererProps = PropsWithChildren &
  ValueContainerProps & { labelText?: string; searchText: string; hideLabelWhenSelectedValue?: boolean };

function ValueContainerRenderer({
  children,
  hideLabelWhenSelectedValue,
  labelText,
  searchText,
  ...props
}: ValueContainerRendererProps) {
  const firstChip = props.getValue()[0] as any;
  const isFirstChipNotEmpty = firstChip?.value && String(firstChip.value)?.trim()?.length > 0;
  const shouldRaiseLabel = isFirstChipNotEmpty || searchText.trim().length > 0;

  let label = labelText ? (
    <FormLabel
      fontSize={shouldRaiseLabel ? 'sm' : 'lg'}
      {...(shouldRaiseLabel
        ? {
            transform: 'translateY(-110%)',
            color: 'gray.300 !important',
          }
        : {
            transform: 'translateY(10%)',
            color: 'iris.500',
          })}
    >
      {labelText}
    </FormLabel>
  ) : null;

  if (hideLabelWhenSelectedValue && (searchText?.trim()?.length > 0 || isFirstChipNotEmpty)) {
    label = null;
  }

  return (
    <components.ValueContainer {...props}>
      {label}
      {children}
    </components.ValueContainer>
  );
}

function MultiValueRemoveRenderer() {
  return null;
}

function MultiValueRenderer(props: MultiValueProps) {
  const { children, data, selectProps } = props;

  const selectedValues = selectProps.value as { value: any; label: any }[];
  const currentValue = data as { value: any; label: any };

  return (
    <components.MultiValue
      className="MultiValueGeneric2"
      {...props}
    >
      {children}
      {selectedValues[selectedValues.length - 1]?.value === currentValue?.value ? '' : ','}
    </components.MultiValue>
  );
}

function OptionRenderer(props: OptionProps) {
  const { children, isSelected, isMulti, data } = props;
  const isLoadingOption = (data as any)?.value === -1;

  const optionRef = useRef<HTMLDivElement | null>(null);
  const isHovered = useHover(optionRef);

  let rightIcon = null;
  if (isMulti && !isLoadingOption) {
    if (isHovered && isSelected) {
      rightIcon = (
        <TrashIcon
          size={18}
          color={theme.colors.periwinkle[700]}
        />
      );
    } else if (!isHovered && isSelected) {
      rightIcon = (
        <CheckedIcon
          size={18}
          color={theme.colors.periwinkle[700]}
        />
      );
    } else {
      rightIcon = (
        <AddIcon
          size={18}
          color={isHovered ? theme.colors.periwinkle[700] : hexOpacity(theme.colors.gray[500], 0.3)}
        />
      );
    }
  }

  return (
    <components.Option {...props}>
      <Flex
        ref={optionRef}
        align="center"
        justify="space-between"
        padding="10px 8px"
      >
        {children}
        <Box flexShrink={0}>{rightIcon}</Box>
      </Flex>
    </components.Option>
  );
}

export function LoadingMessageRenderer() {
  return (
    <Flex
      color="gray.300"
      fontSize="md"
      justify="center"
      align="center"
      width="full"
      gap="24px"
      py="6px"
    >
      <Skeleton
        width="full"
        height="24px"
        borderRadius="full"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
      <Skeleton
        width="18px"
        height="18px"
        borderRadius="full"
        flexShrink={0}
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
    </Flex>
  );
}

function CustomOptionRenderer(props: any, isMedicationSelect: boolean) {
  const { data, isDisabled, innerRef, innerProps } = props;

  return (
    <div
      ref={innerRef}
      {...innerProps}
      style={{
        padding: '10px',
        color: isDisabled ? '#999' : '#333',
        backgroundColor: isDisabled ? '#f5f5f5' : '#fff',
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <span>
        {isMedicationSelect
          ? data.label?.replace(` ${data.label?.substring(data.label.lastIndexOf('['))}`, '')
          : data.label}
      </span>
      {isDisabled && <span style={{ fontSize: '12px', color: '#999' }}> (Already added)</span>}
    </div>
  );
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export const Select = forwardRef(
  <Option, IsMulti extends boolean = false, Group extends GroupBase<Option> = GroupBase<Option>>(
    {
      labelText,
      selectContainerProps,
      hideSelectedOptions = false,
      hideLabelWhenSelectedValue,
      onInputChange,
      onClickOutside,
      MultiValue = MultiValueRenderer,
      DropdownIndicator = DropdownIndicatorRenderer,
      ClearIndicator = ClearIndicatorRenderer,
      MultiValueRemove = MultiValueRemoveRenderer,
      ValueContainer = ValueContainerRenderer,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      OptionItem = OptionRenderer, // Custom option renderer
      LoadingMessage = LoadingMessageRenderer,
      disabledOptions = [], // New prop to pass disabled relationships
      isMedicationSelect = false,
      ...props
    }: Props<Option, IsMulti, Group> & SelectProps,
    selectRef: Ref<any>
  ) => {
    const selectContainerRef = useRef<HTMLDivElement | null>(null);
    const searchTextRef = useRef<string>('');
    const uniqueId = `select_${Math.random().toFixed(5).slice(2)}`;

    useOutsideClick({
      ref: selectContainerRef,
      handler: () => {
        if (selectRef !== null && 'current' in selectRef && selectRef.current?.props?.menuIsOpen) {
          selectRef?.current?.onMenuClose();
        }
        onClickOutside?.();
      },
    });

    const inputChangeHandler = useCallback((value: string) => {
      if (onInputChange) {
        onInputChange(value);
      }
      searchTextRef.current = value;
    }, []);

    const valueContainerRenderer = useCallback((valueContainerProps: ValueContainerProps) => {
      return (
        <ValueContainer
          labelText={
            isMedicationSelect
              ? labelText?.replace(` ${labelText.substring(labelText.lastIndexOf('['))}`, '')
              : labelText
          }
          searchText={searchTextRef.current}
          hideLabelWhenSelectedValue={hideLabelWhenSelectedValue}
          {...valueContainerProps}
        />
      );
    }, []);

    // Function to determine if an option should be disabled
    const isOptionDisabled = (option: any) => {
      return disabledOptions.includes(option?.label);
    };

    return (
      <Box
        ref={selectContainerRef}
        {...selectContainerProps}
      >
        <BaseSelect
          ref={selectRef}
          id={uniqueId}
          placeholder={null}
          closeMenuOnSelect={!props.isMulti}
          hideSelectedOptions={hideSelectedOptions}
          components={{
            MultiValue,
            DropdownIndicator,
            ClearIndicator,
            MultiValueRemove,
            ValueContainer: valueContainerRenderer,
            Option: (propsVal: any) => CustomOptionRenderer(propsVal, isMedicationSelect), // Use the custom Option renderer
            LoadingMessage,
          }}
          styles={SELECT_STYLES}
          classNames={SELECT_CLASS_NAMES}
          onInputChange={inputChangeHandler}
          isOptionDisabled={isOptionDisabled} // Pass the function to disable options
          {...props}
        />
      </Box>
    );
  }
);
