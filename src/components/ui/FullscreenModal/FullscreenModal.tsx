import { PropsWithChildren } from 'react';
import { IconButton, Modal, ModalOverlay } from '@chakra-ui/react';
import { ChevronDown, X as CloseIcon } from 'react-feather';

import { useIsMobile } from '../hooks/device.hook';

interface IFullscreenModal {
  isOpen: boolean;
  isMinimized: boolean;
  background: string;
  onClose: () => void;
  onCloseIconClick: () => void;
}
export function FullscreenModal({
  children,
  isOpen,
  isMinimized,
  background,
  onClose,
  onCloseIconClick,
}: PropsWithChildren<IFullscreenModal>) {
  const isMobile = useIsMobile();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      closeOnEsc={false}
      closeOnOverlayClick={false}
    >
      <ModalOverlay bg={background} />

      <IconButton
        aria-label="Close modal"
        zIndex="var(--chakra-zIndices-popover)"
        position="fixed"
        top={{ base: '14px', md: '24px' }}
        right={{ base: '14px', md: '32px' }}
        width={{ base: '32px', md: '56px' }}
        minW="unset"
        height={{ base: '32px', md: '56px' }}
        borderRadius="full"
        color="white"
        _hover={{ color: 'papaya.600', background: 'fluentHealth.500' }}
        icon={
          isMinimized ? (
            <ChevronDown
              size={isMobile ? 26 : 40}
              strokeWidth={1}
            />
          ) : (
            <CloseIcon
              size={isMobile ? 26 : 40}
              strokeWidth={1}
            />
          )
        }
        onClick={onCloseIconClick}
      />
      {children}
    </Modal>
  );
}
