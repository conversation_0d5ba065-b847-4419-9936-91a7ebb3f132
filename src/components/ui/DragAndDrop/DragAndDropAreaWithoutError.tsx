import { Box, Button, ChakraProps, Flex, Text, chakra, useTheme, useToast } from '@chakra-ui/react';
import React, { PropsWithChildren, ReactNode, useState } from 'react';
import { Accept, DropzoneState, useDropzone } from 'react-dropzone';

import { pluralize } from '@lib/utils/utils';
import { hexOpacity } from 'src/components/theme/utils';

export enum DRAG_AND_DROP_AREA_VARIANT {
  DEFAULT = 'default',
  VERTICAL = 'vertical',
}

const MAX_FILES = 5;

// eslint-disable-next-line @typescript-eslint/naming-convention
const StyledDragPrompt = chakra(Box, {
  shouldForwardProp: (prop) =>
    ![
      'isFocused',
      'isFileDialogActive',
      'acceptedFiles',
      'getRootProps',
      'getInputProps',
      'rootRef',
      'inputRef',
      'open',
    ].includes(prop),
});

export const useDragAndDropArea = ({
  initialState,
  maxFiles = MAX_FILES,
  onChange,
}: {
  initialState?: File[];
  maxFiles?: number;
  onChange?: (files: File[]) => void;
}) => {
  const [files, setFiles] = useState<File[]>(initialState || []);

  const uploadFilesHandler = (droppedFiles: File[]) => {
    const totalFilesCount = files.length + droppedFiles.length;

    if (totalFilesCount >= maxFiles) {
      const extraFilesCount = totalFilesCount - maxFiles;
      droppedFiles.reverse().splice(0, extraFilesCount);
    }

    const uploadedFileNameList = files.map((file) => file.name);
    const filteredNewFileList = droppedFiles.filter((file) => !uploadedFileNameList.includes(file.name));
    const updatedFileList = [...files, ...filteredNewFileList];
    setFiles(updatedFileList);
    onChange?.(updatedFileList);
  };

  return {
    files,
    setUploadedFiles: setFiles,
    uploadFilesHandler,
  };
};

export function DragAndDropAlertPrompt({ children, ...props }: PropsWithChildren<ChakraProps>) {
  return (
    <Text
      color="gray.200"
      fontSize="xs"
      textAlign="center"
      {...props}
    >
      {children}
    </Text>
  );
}

// eslint-disable-next-line complexity
export function DragPrompt({
  variant = DRAG_AND_DROP_AREA_VARIANT.DEFAULT,
  disabled,
  maxFiles,
  isDragAccept,
  isDragReject,
  isDragActive,
  fileRejections,
  alertPrompt,
  ...props
}: DropzoneState &
  ChakraProps & {
    variant: DRAG_AND_DROP_AREA_VARIANT;
    disabled: boolean;
    maxFiles?: number;
    alertPrompt?: ReactNode;
  }) {
  const theme = useTheme();

  const dragStyles = {
    accept: {
      bgColor: hexOpacity(theme.colors.fluentHealthComplementary.Green, 0.1),
      borderColor: 'fluentHealthComplementary.Green',
    },
    reject: {
      bgColor: hexOpacity(theme.colors.fluentHealthComplementary.Red, 0.1),
      borderColor: 'fluentHealthComplementary.Red',
    },
    default: {
      bgColor: hexOpacity('#ffffff', 0.5),
      borderColor: 'iris.500',
    },
  };

  const { bgColor, borderColor } =
    isDragActive && isDragAccept
      ? dragStyles.accept
      : isDragActive && isDragReject
      ? dragStyles.reject
      : dragStyles.default;

  const disabledStyle = disabled ? { opacity: 0.5, cursor: 'no-drop' } : {};

  const pluralizedFile = pluralize('file', maxFiles ?? 1);

  return (
    <StyledDragPrompt
      height={variant === DRAG_AND_DROP_AREA_VARIANT.VERTICAL ? '224px' : '70px'}
      borderWidth="2px"
      borderColor={borderColor}
      borderStyle="dashed"
      borderRadius="8px"
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      cursor="pointer"
      overflow="hidden"
      transition="height 0.4s ease, border-color 0.2s ease, background-color 0.2s ease"
      bgColor={bgColor}
      _hover={{
        borderColor: 'fluentHealthSecondary.200',
        bgColor: 'fluentHealthSecondary.500',
      }}
      {...disabledStyle}
      {...props}
    >
      {variant === DRAG_AND_DROP_AREA_VARIANT.DEFAULT && (
        <Flex
          align="center"
          gap="12px"
        >
          <Text color="fluentHealthText.400">
            {isDragAccept && `All ${pluralizedFile} will be accepted`}
            {isDragReject && `Some ${pluralizedFile} will be rejected`}
            {!isDragActive && `Drag and drop ${pluralizedFile} here or`}
          </Text>
          <Button
            variant="outline"
            borderColor="fluentHealthText.400"
            color="fluentHealthText.200"
            size="xs"
            borderRadius="8px"
            py="2"
          >
            Browse files
          </Button>
        </Flex>
      )}
      {variant === DRAG_AND_DROP_AREA_VARIANT.VERTICAL && (
        <Flex
          direction="column"
          align="center"
          gap="12px"
        >
          <Text color="fluentHealthText.400">
            {isDragAccept && `All ${pluralizedFile} will be accepted`}
            {isDragReject && `Some ${pluralizedFile} will be rejected`}
            {!isDragActive && `Drag and drop your ${pluralizedFile} here`}
          </Text>
          <Text
            color="fluentHealthText.400"
            lineHeight="1"
          >
            or
          </Text>
          <Button
            variant="outline"
            borderColor="fluentHealthText.400"
            color="fluentHealthText.200"
            size="xs"
            borderRadius="8px"
            py="2"
          >
            Browse file
          </Button>
        </Flex>
      )}
      {alertPrompt && alertPrompt}
    </StyledDragPrompt>
  );
}

export function DragAndDropArea({
  onFilesDrop,
  onDropRejected,
  acceptedFileTypes = {},
  multiple = true,
  disabled = false,
  maxFiles = 5, // 5 files
  children = DragPrompt,
  ...props
}: ChakraProps & {
  acceptedFileTypes?: Accept;
  multiple?: boolean;
  disabled?: boolean;
  maxFiles?: number;
  children?: (props: any) => React.ReactNode;
  onFilesDrop: (files: File[]) => void;
  onDropRejected?: (fileRejections: DropzoneState['fileRejections']) => void;
}) {
  const [submitting, setSubmitting] = useState(false);

  const toast = useToast();

  const dropzone = useDropzone({
    accept: acceptedFileTypes,
    disabled: disabled || submitting,
    maxFiles,
    multiple,

    // Define drop handler as async to support error handling.
    onDropAccepted: async (acceptedFiles: any) => {
      setSubmitting(true);

      try {
        if (multiple) {
          await onFilesDrop(acceptedFiles);
        } else {
          await onFilesDrop(acceptedFiles.pop());
        }
      } catch (e) {
        toast({
          title: 'Cannot upload file',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
      setSubmitting(false);
    },
    onDropRejected,
  });

  return (
    <Box
      {...dropzone.getRootProps()}
      {...props}
    >
      <input {...dropzone.getInputProps()} />
      {children({ disabled, maxFiles, ...dropzone })}
    </Box>
  );
}
