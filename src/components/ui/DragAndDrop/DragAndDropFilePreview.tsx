import { <PERSON>ton, Card, ChakraProps, Flex, Image, Text, useTheme } from '@chakra-ui/react';
import { X as RemoveIcon } from 'react-feather';

import { FilesPreview } from 'src/app/medical-records/components/FilesPreview';

export function DragAndDropFilePreview({
  file,
  medicalRecord,
  onRemove,
  onRetry,
  size,
  progress,
  progressColor,
  ...props
}: ChakraProps & {
  file: File;
  // Todo medicalRecord need to be corrected for every component call, any has to be removed wgen done for all
  medicalRecord?: any;
  onRemove?: (file: File) => void;
  onRetry?: (file: File) => void;
  size?: number;
  progress?: number;
  progressColor?: string;
}) {
  const theme = useTheme();

  return (
    <Card
      key={file?.name}
      backgroundColor={onRemove ? 'fluentHealthSecondary.500' : 'white'}
      boxShadow="none"
      {...props}
    >
      <Flex
        alignItems="center"
        justifyContent="space-between"
      >
        <Flex alignItems="center">
          <Image
            m="1"
            height="60px"
            width="48px"
            borderRadius="4px"
            objectFit="cover"
            border="1px solid #B6B9D8"
            boxShadow="0px 1px 2px 0px rgba(73, 90, 228, 0.14)"
            src={file?.type?.includes('image') ? URL.createObjectURL(file) : '/pdf-document.png'}
          />
          <Flex
            direction="column"
            ml="4"
          >
            <Text
              fontSize="lg"
              color="fluentHealthText.100"
              mb="4px"
              wordBreak="break-word"
            >
              {file?.name}
            </Text>

            {medicalRecord ? (
              <FilesPreview files={medicalRecord?.content}>
                {({ onClick }) => (
                  <Text
                    fontSize="sm"
                    color="iris.500"
                    cursor="pointer"
                    onClick={onClick}
                  >
                    View all
                  </Text>
                )}
              </FilesPreview>
            ) : (
              <Text
                fontSize="sm"
                color="fluentHealthText.200"
              >
                {Math.round((size ?? file?.size ?? 0) / 1024)} KB
              </Text>
            )}
          </Flex>
        </Flex>
        <Flex>
          {onRemove && (
            <Button
              marginRight="3"
              px="5px"
              py="5px"
              variant="ghost"
              size="smaller"
              _hover={{ bg: 'fluentHealthSecondary.400' }}
              onClick={() => onRemove(file)}
            >
              <RemoveIcon
                size="16px"
                color={theme.colors.fluentHealthText[250]}
              />
            </Button>
          )}
          {onRetry && (
            <Button
              marginRight="16px"
              variant="ghost"
              fontSize="12px"
              size="smaller"
              letterSpacing="-0.24px"
              onClick={() => onRetry(file)}
            >
              Retry
            </Button>
          )}
        </Flex>
      </Flex>
      {progress ? (
        <Flex
          mt="2"
          width="100%"
          height="4px"
          backgroundColor="gray.200"
          borderRadius="md"
          overflow="hidden"
        >
          <Flex
            width={`${progress}%`}
            height="100%"
            backgroundColor={progressColor || 'blue.500'}
            transition="width 0.3s ease-in-out"
          />
        </Flex>
      ) : null}
    </Card>
  );
}
