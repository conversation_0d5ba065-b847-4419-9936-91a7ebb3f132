import { PrismicRichText as BasePrismicRichText } from '@prismicio/react';

interface PrismicElementProps {
  children: React.ReactNode;
  className?: string;
}

type PrismicElementsMapping = {
  heading1: (props: PrismicElementProps) => JSX.Element;
  heading2: (props: PrismicElementProps) => JSX.Element;
  heading3: (props: PrismicElementProps) => JSX.Element;
  heading4: (props: PrismicElementProps) => JSX.Element;
  heading5: (props: PrismicElementProps) => JSX.Element;
  heading6: (props: PrismicElementProps) => JSX.Element;
  paragraph: (props: PrismicElementProps) => JSX.Element;
};

interface PrismicRichTextProps {
  className?: string;
  field: any;
}
const PRISMIC_ELEMENTS_MAPPING: any = {
  heading1: ({ children, className }: any) => <h1 className={className}>{children}</h1>,
  heading2: ({ children, className }: any) => <h2 className={className}>{children}</h2>,
  heading3: ({ children, className }: any) => <h3 className={className}>{children}</h3>,
  heading4: ({ children, className }: any) => <h4 className={className}>{children}</h4>,
  heading5: ({ children, className }: any) => <h5 className={className}>{children}</h5>,
  heading6: ({ children, className }: any) => <h6 className={className}>{children}</h6>,
  paragraph: ({ children, className }: any) => <p className={className}>{children}</p>,
};

function PrismicRichText({ className, field }: PrismicRichTextProps) {
  const componentsWithClassNames: PrismicElementsMapping = Object.keys(PRISMIC_ELEMENTS_MAPPING).reduce((acc, key) => {
    acc[key as keyof PrismicElementsMapping] = (componentProps: PrismicElementProps) =>
      PRISMIC_ELEMENTS_MAPPING[key as keyof PrismicElementsMapping]({
        ...componentProps,
        className,
      });
    return acc;
  }, {} as PrismicElementsMapping);
  return (
    <BasePrismicRichText
      components={componentsWithClassNames}
      field={field}
    />
  );
}

export default PrismicRichText;
