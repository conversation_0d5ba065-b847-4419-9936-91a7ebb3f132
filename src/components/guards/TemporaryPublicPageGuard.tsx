// Package modules
import { Box, Container, Heading } from '@chakra-ui/react';
import React, { PropsWithChildren } from 'react';

// Local modules
import { useAuthService } from '@lib/state';

function ErrorHandler() {
  return (
    <Container py="5">
      <Box
        bgColor="periwinkle.100"
        borderRadius="40px"
        py="120px"
        px="32px"
        textAlign="center"
      >
        <Heading>Access Denied</Heading>
      </Box>
    </Container>
  );
}

export function TemporaryPublicPageGuard({ children }: PropsWithChildren) {
  const { isLoggedIn } = useAuthService();

  if (!isLoggedIn) {
    return <ErrorHandler />;
  }

  // eslint-disable-next-line react/jsx-no-useless-fragment
  return <>{children}</>;
}
