// Package modules
import { PropsWithChildren } from 'react';
import { Navigate, createSearchParams, useLocation } from 'react-router-dom';
// Local modules
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { useAuthService } from '@lib/state';

interface IAuthGuard {
  shouldBeAuthenticated?: boolean;
  children: JSX.Element;
}
export function AuthGuard({ shouldBeAuthenticated = true, children }: PropsWithChildren<IAuthGuard>) {
  const { isLoggedIn } = useAuthService();
  const location = useLocation();
  const { DASHBOARD, PROFILE, BASIC } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;

  if (shouldBeAuthenticated && !isLoggedIn) {
    // User is not authenticated and should be
    const searchParams = createSearchParams({
      redirect: location.pathname + location.search || `/${DASHBOARD}/${VIEW}`,
    });
    return (
      <Navigate
        to={`/login?${searchParams}`}
        replace
      />
    );
  }

  if (!shouldBeAuthenticated && isLoggedIn) {
    // Redirect to the `redirect` parameter or default to `/profile`
    const redirectUrl = new URLSearchParams(location.search).get('redirect') || `/${PROFILE}/${BASIC}/${VIEW}`;
    return (
      <Navigate
        to={redirectUrl}
        replace
      />
    );
  }

  // Render children if no redirection is needed
  return children;
}
