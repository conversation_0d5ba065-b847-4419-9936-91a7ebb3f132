// Package modules
import {
  Modal as ChakraModal,
  <PERSON>ing,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  UseDisclosureProps,
} from '@chakra-ui/react';
import { PropsWithChildren, ReactNode } from 'react';

import { useIsMobile } from './ui/hooks/device.hook';

export enum MODAL_VARIANTS {
  DEFAULT = 'default',
  SHARING = 'sharing',
  PERIWINKLE = 'periwinkle',
}

export interface IModal extends UseDisclosureProps {
  variant?: string;
  title?: string | ReactNode;
  titleFontSize?: string;
  minWidth?: string;
  showModalHeading?: boolean;
  [x: string]: any; // this for UseDisclosureProps
}
export function InfoModal(props: PropsWithChildren<IModal>) {
  const {
    // Modal props
    variant = MODAL_VARIANTS.PERIWINKLE,
    title,
    titleFontSize = '2xl',
    minWidth = '560px',
    showModalHeading = true,
    // Disclosure props
    isOpen,
    onClose,
    description,
  } = props;
  const isMobile = useIsMobile();

  return (
    <ChakraModal
      isOpen={isOpen!}
      onClose={onClose!}
      variant={variant}
      {...props}
    >
      <ModalOverlay />
      <ModalContent minW={{ base: '100%', md: isMobile ? '100%' : minWidth }}>
        <ModalCloseButton
          style={{ top: '25px', right: '25px' }}
          width="28px"
          height="28px"
        />
        {showModalHeading && (
          <ModalHeader>
            <Heading
              fontSize={titleFontSize}
              style={{ marginTop: '44px', marginLeft: '8px', fontWeight: '500' }}
              color="fluentHealthText.100"
            >
              {title}
            </Heading>
          </ModalHeader>
        )}
        {description && (
          <ModalBody
            style={{ marginRight: '8px', marginLeft: '8px', marginBottom: '20px' }}
            color="charcoal.700"
            overflowY="auto"
          >
            {description}
          </ModalBody>
        )}
      </ModalContent>
    </ChakraModal>
  );
}
