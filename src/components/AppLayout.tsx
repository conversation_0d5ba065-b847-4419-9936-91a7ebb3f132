// Package modules
import { Suspense } from 'react';
import { Outlet, useLocation } from 'react-router-dom';

// Local modules
import { Navbar, PublicNavbar } from './Navbar';
import Footer from './Footer';
import { FluentHealthLoader } from './FluentHealthLoader';
// import { Chat } from '../app/chat/Chat';
// import { useShowChat } from '../app/chat/lib/utils';

const PATHS_WITH_HIDDEN_NAVBAR: string[] = ['/login', '/error'];
const PATHS_WITH_HIDDEN_FOOTER: string[] = ['/login'];

export function AppLayout() {
  const { pathname } = useLocation();
  // const showChat = useShowChat();

  return (
    <>
      {!PATHS_WITH_HIDDEN_NAVBAR.includes(pathname) && <Navbar />}
      <Suspense fallback={<FluentHealthLoader mt="32px" />}>
        <Outlet />
      </Suspense>
      {/* {showChat && <Chat />} */}
      {!PATHS_WITH_HIDDEN_FOOTER.includes(pathname) && <Footer />}
    </>
  );
}

export function PublicAppLayout() {
  return (
    <>
      <PublicNavbar />
      <Suspense fallback={<FluentHealthLoader mt="32px" />}>
        <Outlet />
      </Suspense>
      <Footer />
    </>
  );
}
