import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Flex, Input } from '@chakra-ui/react';
import { AddInputFieldProps } from '@src/types/types';
import { createWeightRegex } from '@utils/regex';

/**
 * AddInputField component for entering a value in an input field.
 * This component provides an input field with a clear button and handles
 * clicking outside the input field to trigger the onSelect callback.
 * @param {AddInputFieldProps} props - The component props.
 * @returns {JSX.Element} The AddInputField component.
 */
function AddInputField({
  defaultValue,
  onSelect,
  onClear,
  setIsAdding,
  isAdding,
  maxCharacters,
  maxDecimalPoints,
  onInputChange,
}: AddInputFieldProps) {
  const [inputValue, setInputValue] = useState(defaultValue);
  const inputRef = useRef<HTMLDivElement>(null);

  // Update the input value when the defaultValue prop changes
  useEffect(() => {
    setInputValue(defaultValue);
  }, [defaultValue]);

  // Focus the input field when isAdding is true
  useEffect(() => {
    if (isAdding && inputRef.current) {
      inputRef.current.querySelector('input')?.focus();
    }
  }, [isAdding]);

  // Validate the input value according to the maxCharacters and maxDecimalPoints props
  const isValidInput = (value: string) => {
    const regex = createWeightRegex(maxCharacters, maxDecimalPoints);
    return regex.test(value);
  };

  // Handle input value changes
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (isValidInput(value)) {
      setInputValue(value);
      if (onInputChange) onInputChange(value);
    }
  };

  // Clear the input value and call the onClear callback
  const handleClear = (value: string) => {
    setInputValue(value);
    if (onClear) onClear(value);
    if (onInputChange) onInputChange('0');
  };

  // Detect clicks outside the input field to call onSelect and update isAdding
  const handleClickOutside = (event: MouseEvent) => {
    if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
      if (setIsAdding) {
        setIsAdding(false);
        if (inputValue && onSelect) onSelect(inputValue);
      }
    }
  };

  // Handle the Enter key press to call onSelect
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      if (setIsAdding) setIsAdding(false);
      if (onSelect) onSelect(inputValue);
    }
  };

  // Add and remove the mousedown event listener for outside click detection
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [inputValue]);

  return (
    <Flex
      direction="column"
      alignItems="center"
      ref={inputRef}
    >
      <Box
        position="relative"
        width="100%"
        border="0px"
      >
        <Input
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          borderBottom="1px solid #FFF"
          width="100%"
          textAlign="left"
          borderRadius="0"
          color="white"
          fontSize={{ base: 'md', md: 'xl' }}
          lineHeight="1"
          height="27.5px"
          paddingRight="4"
          paddingLeft="0"
          fontWeight="400"
          fontFamily="var(--chakra-fonts-heading)"
          border="none"
          _focus={{
            borderBottom: '1px solid #FFF',
            boxShadow: 'none',
          }}
        />
        {inputValue && (
          <Button
            size="sm"
            position="absolute"
            right="0.5rem"
            top="50%"
            transform="translateY(-50%)"
            onClick={() => handleClear(inputValue)}
            backgroundColor="transparent"
            _hover={{ backgroundColor: 'transparent' }}
            padding="0"
            fontFamily="Apercu"
            lineHeight="18px"
            fontWeight="400"
            zIndex="1"
          >
            Clear
          </Button>
        )}
      </Box>
    </Flex>
  );
}

export default AddInputField;
