import { NavLink, useRouteError } from 'react-router-dom';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import AppError from './components/error/AppError';

export default function ErrorPage() {
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const error: any = useRouteError();

  return (
    <div className="mx-auto mt-6 max-w-lg rounded bg-white px-4 py-6">
      <p className="mb-4 text-lg font-bold">Sorry, an unexpected error has occurred.</p>
      <AppError error={error.error?.message ?? error.message} />
      <NavLink
        className="mt-3 inline-block text-sm text-blue-500 underline underline-offset-2"
        to={`/${DASHBOARD}/${VIEW}`}
      >
        Navigate home
      </NavLink>
    </div>
  );
}
