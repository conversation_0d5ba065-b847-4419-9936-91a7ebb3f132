// FHIR HL7 Constants
export const FHIR_HL7_ACT_CODE_CODESYSTEM = 'http://terminology.hl7.org/CodeSystem/v3-ActCode';
export const FHIR_HL7_CODE_OBSERVATION_CATEGORY = 'http://terminology.hl7.org/CodeSystem/observation-category';
export const FHIR_HL7_CODE_SYSTEM_ROLE_CODE = 'http://terminology.hl7.org/CodeSystem/v3-RoleCode';
export const FHIR_HL7_STRUCTURE_DEFINITION = 'http://hl7.org/fhir/StructureDefinition/';
export const FHIR_HL7_CODE_SYSTEM_IMMUNIZATION_ORIGIN = 'http://terminology.hl7.org/CodeSystem/immunization-origin';
export const FHIR_HL7_CODE_SYSTEM_COMMUNICATION_CATEGORY =
  'http://terminology.hl7.org/CodeSystem/communication-category';
export const FHIR_HL7_CODE_SYSTEM_COMMUNICATION_REMINDER_ALL_DAY =
  'https://fluentinhealth.com/fhir/StructureDefinition/reminder-all-day';
export const FHIR_HL7_CODE_SYSTEM_COMMUNICATION_OCCURRENCE_TIMING =
  'https://fluentinhealth.com/fhir/StructureDefinition/occurrence-timing';

export const FHIR_HL7_STRUCTURE_DEFINITION_IMMUNIZATION_SUPPORTING_INFO = `${FHIR_HL7_STRUCTURE_DEFINITION}/immunization-supportingInformation`;
export const FHIR_HL7_STRUCTURE_DEFINITION_IMMUNIZATION = `${FHIR_HL7_STRUCTURE_DEFINITION}/IMMUNIZATION`;
export const FHIR_HL7_STRUCTURE_DEFINITION_CONDITION =
  'http://hl7.org/fhir/extensions/StructureDefinition-condition-diseaseCourse.html';
// Fluent Health Custom Constants
export const FH_CODE_SYSTEM_CREATED_AT = 'https://fluentinhealth.com/FHIR/CodeSystem/CreatedAt';
export const FH_CODE_SYSTEM_FACT = 'https://fluentinhealth.com/FHIR/CodeSystem/FACT';
export const FH_CODE_SYSTEM_FLUENT_HEALTH_UI = 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI';
export const FH_STRUCTURE_DEFINITION_PATIENT_ETHNICITY =
  'https://fluentinhealth.com/FHIR/StructureDefinition/PatientEthnicity';
export const FH_STRUCTURE_DEFINITION_PATIENT_GENDER_IDENTITY =
  'https://fluentinhealth.com/FHIR/StructureDefinition/PatientGenderIdentity';
export const FH_STRUCTURE_DEFINITION_DOCUMENTREFERENCE_NOTE =
  'https://fluentinhealth.com/fhir/StructureDefinition/Note';
export const FH_STRUCTURE_DEFINITION_CLINICALSTATUS =
  'https://fluentinhealth.com/fhir/StructureDefinition/clinical-status';
export const SNOMED_URL = 'http://snomed.info/sct';
export const FH_STRUCTURE_DEFINITION_PRESCRIBED = 'https://fluentinhealth.com/fhir/StructureDefinition/Prescribed';
// Other Constants
export const LOINC_URL = 'http://loinc.org';
export const UNITS_MEASURE_URL = 'http://unitsofmeasure.org';
export const LANGUAGE_URN = 'urn:ietf:bcp:47';
export const FH_CODE_SYSTEM_USER_META_TAG = 'https://fluentinhealth.com/FHIR/CodeSystem/UserMetaTag';
export const PRACTITIONER_URN_UUID = 'urn:uuid:1a2acab7-1340-477f-b74f-c8d920cd2159';
export const PRACTITIONER_ROLE_URN_UUID = 'urn:uuid:003a0ff4-d003-4f8f-b3e9-cc5e5c273aad';
export const CONDITION_URN_UUID = 'urn:uuid:906cfac0-b440-4821-b457-96c606d3bc30';
export const ORGANIZATION_URN_UUID = 'urn:uuid:e5ed738b-fccd-4d9c-be4c-9d08a7348e1e';
export const ENCOUNTER_URN_UUID = 'urn:uuid:6f75c963-b0cd-4d07-b7b1-1d6167993251';
export const MEDICATION_STATEMENT_URN_UUID = 'urn:uuid:34e6ffde-f6b7-403c-b08c-8a2992d8b1b9';
export const HL7_FAMILY_MEMBER_CONDITION_EVIDENCE =
  'http://hl7.org/fhir/StructureDefinition/familymemberhistory-condition-evidence';
