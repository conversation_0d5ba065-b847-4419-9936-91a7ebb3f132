import axios from 'axios';

import { AuthService } from '@lib/authService';
import { ELASTIC_SEARCH_API_URL, PROJECT_ID } from '@lib/constants';

export async function getSearchSuggestion(variables: any) {
  if (!variables || typeof variables !== 'object') return [];
  const mustQueries: any[] = [];
  const shouldQueries: any[] = [];

  // Fuzzy search string
  if (variables?.filters) {
    mustQueries.push({
      query_string: {
        query: `*${variables?.filters}*`,
        default_operator: 'AND',
        analyzer: 'default_search',
        analyze_wildcard: true,
      },
    });
  }

  // Report Type
  if (variables?.reportType) {
    mustQueries.push({
      match: {
        'type.coding.code': variables?.reportType,
      },
    });
  }
  // Tag code(s) — OR logic using should
  if (variables?.tag) {
    const tagArray = variables?.tag?.split(',').map((tag: string) => tag?.trim());
    tagArray.forEach((tag: string) => {
      if (tag) {
        shouldQueries.push({
          match: {
            'meta.tag.code': tag,
          },
        });
      }
    });
  }
  // Bookmark (optional single tag)
  if (variables?.bookmark) {
    mustQueries.push({
      match: {
        'meta.tag.code': variables?.bookmark,
      },
    });
  }

  // Date Range
  if (variables?.fromDate && variables?.toDate) {
    mustQueries.push({
      range: {
        'content.attachment.creation': {
          gte: variables?.fromDate,
          lte: variables?.toDate,
        },
      },
    });
  }
  // Combine must + optional should
  const boolQuery: any = {
    must: mustQueries,
  };

  if (shouldQueries?.length > 0) {
    boolQuery.should = shouldQueries;
    boolQuery.minimum_should_match = 1; // <- important: ensure at least one `should` matches
  }
  const {
    data,
    // status,
  } = await axios.post(
    `${ELASTIC_SEARCH_API_URL}/${PROJECT_ID}-resource-documentreference/_search`,
    {
      query: {
        bool: boolQuery,
      },
      size: 10000,
    },
    { headers: AuthService.instance.withAuthHeader() }
  );

  return data || [];
}
