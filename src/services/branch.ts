import branch from 'branch-sdk';
import { generateBranchUrl } from '@utils/branchUtils';
import { getDeeplinkPath, safeJsonParse } from '@utils/utils';

const BRANCH_KEY = import.meta.env.VITE_BRANCH_KEY as string;
if (!BR<PERSON>CH_KEY) throw new Error('Branch Key is not provided. Please add VITE_BRANCH_KEY in .env');
const options = { no_journeys: true };

branch.init(BRANCH_KEY, options, (err: string | null, data: any) => {
  if (err) {
    console.error('Branch.io Initialization Error:', err);
    return;
  }
  try {
    const response = typeof data === 'string' ? safeJsonParse(data) : data;
    const deeplinkPath = getDeeplinkPath(response);
    if (deeplinkPath) {
      const formattedPath = deeplinkPath.startsWith('/') ? deeplinkPath : `/${deeplinkPath}`;
      localStorage.setItem('deep_link_path', formattedPath);
      if (formattedPath !== window.location.pathname) {
        window.location.pathname = formattedPath;
      } else {
        localStorage.removeItem('deep_link_path');
      }
    }
  } catch (_err) {
    console.error('error', _err);
  }

  generateBranchUrl(data);
});

export default branch;
