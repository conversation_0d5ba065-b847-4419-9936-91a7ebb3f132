import { CACHE_NAME } from './lib/constants';

// Install the service worker
this.addEventListener('install', (event) => {
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        // Array of URLs to cache
        const urlsToCache = ['/'];

        // Try to add all the resources to the cache
        return Promise.all(
          urlsToCache.map((url) => {
            return fetch(url).then((response) => {
              if (!response.ok) {
                // If the response is not ok, throw an error
                throw new Error(`Failed to fetch ${url}`);
              }
              return cache.put(url, response); // Add the resource to the cache
            });
          })
        );
      })
      .catch((error) => {
        console.error('Caching failed: ', error);
      })
  );
});

// Activate the service worker
this.addEventListener('activate', (event) => {
  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.forEach((cache) => {
          if (cache !== CACHE_NAME) {
            caches.delete(cache); // delete old cache versions
          }
        })
      );
    })
  );
});

// Push event listener - Handling incoming push notifications
this.addEventListener('push', (event) => {
  let options = {
    body: 'Default notification body',
    icon: '/images/icon.png', // default icon
    badge: '/images/badge.png', // default badge
    data: {
      url: '/', // default URL to open when clicked
    },
  };

  // Check if the event contains a push message (if using CleverTap)
  if (event.data) {
    const data = event.data.json();
    options = {
      body: data.body || options.body,
      icon: data.icon || options.icon,
      badge: data.badge || options.badge,
      data: {
        url: data.url || options.data.url,
      },
    };
  }

  // Show the push notification
  event.waitUntil(this.registration.showNotification('New Message', options));
});

// Notification click event listener
this.addEventListener('notificationclick', (event) => {
  event.notification.close(); // Close the notification
  const { url } = event.notification.data; // Open the URL in a new window/tab

  // Open the URL when the notification is clicked
  event.waitUntil(this.clients.openWindow(url).then(() => {}));
});

// Background sync (optional) for when the network is restored
this.addEventListener('sync', (event) => {
  if (event.tag === 'clevertap-sync') {
    event.waitUntil(
      // Handle background sync tasks (e.g., sending offline data)
      fetch('/api/sync-data') // Replace with actual API for syncing
        .then((response) => response.json())
        .then(() => {})
    );
  }
});
