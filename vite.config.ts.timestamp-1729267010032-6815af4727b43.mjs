// vite.config.ts
import { defineConfig, loadEnv } from "file:///home/<USER>/Workspace/webapp/node_modules/vite/dist/node/index.js";
import react from "file:///home/<USER>/Workspace/webapp/node_modules/@vitejs/plugin-react-swc/index.mjs";
import tsconfigPaths from "file:///home/<USER>/Workspace/webapp/node_modules/vite-tsconfig-paths/dist/index.mjs";
import { createHtmlPlugin } from "file:///home/<USER>/Workspace/webapp/node_modules/vite-plugin-html/dist/index.mjs";
import svgr from "file:///home/<USER>/Workspace/webapp/node_modules/vite-plugin-svgr/dist/index.mjs";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, ".");
  return {
    plugins: [
      react(),
      svgr(),
      tsconfigPaths(),
      createHtmlPlugin({
        inject: {
          data: {
            ...env,
            ENV_NAME: mode === "development" ? "Local" : mode.toLowerCase().replace(/\b\w/g, (s) => s.toUpperCase())
          }
        }
      })
    ],
    server: {
      watch: {
        usePolling: true
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
