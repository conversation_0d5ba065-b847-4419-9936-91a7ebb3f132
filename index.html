<!DOCTYPE html>
<html lang="en" class="overflow-x-clip">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/fh-logo-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fluent Health<%- ENV_NAME === 'Production' ? '' : ' - ' + ENV_NAME %></title>
    <meta name="BUILD_VERSION" content="%VITE_BUILD_VERSION%" />
    <style>
      body {
        padding: 0;
        margin: 0;
      }

      #root_loader {
        position: fixed;
        z-index: 1;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(359.13deg, #FFF2DF 0.3%, #DADCFF 50.02%, #FFF2DF 99.82%);
      }

      #root_loader {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      #root_loader div:nth-child(1) {
        width: 115px;
        height: 45px;
        background-color: rgba(73, 86, 228, 0.3);
        border-top-left-radius: 100px;
        border-top-right-radius: 100px;
        animation-name: root_loader_item_1_animation;
        animation-duration: 2s;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
      }

      #root_loader div:nth-child(3) {
        width: 60px;
        height: 23px;
        background-color: #4956E4;
        border-top-left-radius: 100px;
        border-top-right-radius: 100px;
        animation-name: root_loader_item_2_animation;
        animation-duration: 2s;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
      }

      #root_loader div:nth-child(2) {
        width: 87px;
        height: 33px;
        background-color: rgba(73, 86, 228, 0.5);
        border-top-left-radius: 100px;
        border-top-right-radius: 100px;
      }

      @keyframes root_loader_item_1_animation {
        0% {
          width: 115px;
          height: 45px;
          background-color: rgba(73, 86, 228, 0.3);
        }
        50% {
          width: 60px;
          height: 23px;
          background-color: #4956E4;
        }
        100% {
          width: 115px;
          height: 45px;
          background-color: rgba(73, 86, 228, 0.3);
        }
      }

      @keyframes root_loader_item_2_animation {
        0% {
          width: 60px;
          height: 23px;
          background-color: #4956E4;
        }
        50% {
          width: 115px;
          height: 45px;
          background-color: rgba(73, 86, 228, 0.03);
        }
        100% {
          width: 60px;
          height: 23px;
          background-color: #4956E4;
        }
      }
    </style>
  </head>
  <body class="overflow-x-clip">
    <div id="root">
      <!--
           This is a placeholder to display before the entire application is loaded.
           It contains three divs that shrink or grow in sequence with animation.
           Together, these divs make up the app's logo.
       -->
      <div id="root_loader">
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
