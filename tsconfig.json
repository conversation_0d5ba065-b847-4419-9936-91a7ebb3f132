{
  "compilerOptions": {
    "types": ["vite-plugin-svgr/client"], // allows to import svg files as ReactComponent
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./",
    "paths": {
      "@lib": ["src/lib"],
      "@lib/*": ["src/lib/*"],
      "@user/lib/*": ["src/app/user/lib/*"],
      "@assets/*": ["src/assets/*"],
      "@src/types/*": ["src/types/*"],
      "@gql/*": ["src/gql/*"],
      "@utils/*": ["src/utils/*"],
      "@components/*": ["src/components/*"],
      "@services/*": ["src/services/*"],
    }
  },
  "include": ["src", "config.dev.ts", "codegen.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
