# https://cloud.google.com/build/docs/api/reference/rest/v1/projects.builds#Build
# `set -e` to stop on error
# `set -x` show printed commands
options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
  automapSubstitutions: true
steps:
  - id: Build
    name: node:20-alpine
    script: |
      set -ex
      npm install
      npm run build:$PROJECT_ID
      npm run lint
    env:
      - VITE_BUILD_VERSION=${TAG_NAME:-$SHORT_SHA}

  - id: Deploy
    name: asia-south1-docker.pkg.dev/$PROJECT_ID/firebase/firebase:latest
    args:
      - hosting:channel:deploy
      - $_HEAD_BRANCH
      - --expires=14d
      - --only=$_HOSTING
      - --project=$PROJECT_ID
