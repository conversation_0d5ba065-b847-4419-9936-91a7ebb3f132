## Components

---

### Text Input (Material Design)

Customized Chakra FormGroup component which simulates the Material input design (floating label upon focus).

1. Usage:

```tsx
const { register } = useForm({
   defaultValues: { /* ... */ }
});
const VALIDATION = {
  minLength: 1,
  // ...
};

<FormControl variant="floating">
  <Input placeholder=" " id="someField" {...register('someField', VALIDATION} />
  {/* It is important that the Label comes after the Control due to css selectors */}
  <FormLabel>First name</FormLabel>
  <FormErrorMessage>Your First name is invalid</FormErrorMessage>
</FormControl>
```

FormControl has to be used with `useForm -> register` (See [doc reference](https://react-hook-form.com/api/useform/register/))

2. Styling:

```ts
// We have used the Chakra theme expansion
// File: src/theme/components/form.tsx
export const Form = {
  variants: {
    floating: {
      // We have overridden the "floating" variant of the form
      container: {
        input: {
          /* styling for the default state of the Input */
        },

        label: {
          /* styling for the default state of the Label */
        },

        /* Styling for the focused states of the container */
        _focusWithin: {
          label: {
            /* ... */
          },
          input: {
            /* ... */
          },
        },

        'input:not(:placeholder-shown) + label, .chakra-select__wrapper + label, textarea:not(:placeholder-shown) ~ label':
          {
            /* ... */
          },

        'input:focus-visible': {
          /* ... */
        },
      },
    },
  },
};
```

3. States:

   - Default / Unfocused
     <img src="./assets/components_1_1.png">

   - Focused (clicked)
     <img src="./assets/components_1_2.png">

   - Focused (typing)
     <img src="./assets/components_1_3.png">

---
