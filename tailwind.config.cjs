const colors = require('tailwindcss/colors');

/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: { ...colors.violet, DEFAULT: colors.violet[500] },
        accent: { ...colors.blue, DEFAULT: colors.blue[500] },
        error: { ...colors.rose, DEFAULT: colors.rose[500] },
        success: { ...colors.teal, DEFAULT: colors.teal[500] },
      },
      fontFamily: {
        sans: [
          'Inter',
          'ui-sans-serif',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          '"Segoe UI"',
          'Roboto',
          '"Helvetica Neue"',
          'Arial',
          '"Noto Sans"',
          'sans-serif',
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"',
        ],
      },
      fontSize: {
        xxs: ['0.625rem', { lineHeight: '0.875rem' }],
      },
      borderRadius: {
        sm: '0.25rem',
        DEFAULT: '0.375rem',
        md: '0.5rem',
        lg: '0.75rem',
        xl: '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      animation: {
        fadeIn: 'fadeIn 100ms ease-out',
        fadeOut: 'fadeOut 75ms ease-in',
      },
      keyframes: {
        fadeIn: {
          '0%': { transform: 'scale(95%)', opacity: '0' },
          '100%': { transform: 'scale(100%)', opacity: '1' },
        },
        fadeOut: {
          '0%': { transform: 'scale(100%)', opacity: '1' },
          '100%': { transform: 'scale(95%)', opacity: '0' },
        },
      },
    },
    plugins: [],
  },
};
